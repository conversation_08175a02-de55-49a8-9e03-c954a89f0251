#!/usr/bin/env python3
"""
Simple file server to serve GLB files to the web interface
"""

import http.server
import socketserver
import os
import json
from datetime import datetime

class FileServerHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=".", **kwargs)
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        if self.path == '/api/files':
            self.list_glb_files()
        elif self.path == '/api/test':
            self.send_json_response({
                'test': 'success',
                'server': 'file_server',
                'timestamp': datetime.now().isoformat()
            })
        else:
            super().do_GET()
    
    def list_glb_files(self):
        """List all GLB files"""
        try:
            models_dir = 'backend/outputs/models'
            files = []
            
            if os.path.exists(models_dir):
                for filename in os.listdir(models_dir):
                    if filename.endswith('.glb'):
                        filepath = os.path.join(models_dir, filename)
                        file_size = os.path.getsize(filepath)
                        
                        # Determine character type from filename
                        character_type = 'humanoid'
                        if 'robot' in filename:
                            character_type = 'robot'
                        elif 'alien' in filename:
                            character_type = 'alien'
                        elif 'warrior' in filename:
                            character_type = 'warrior'
                        
                        files.append({
                            'filename': filename,
                            'path': f'{models_dir}/{filename}',
                            'size': file_size,
                            'character_type': character_type,
                            'url': f'/{models_dir}/{filename}'
                        })
            
            self.send_json_response({
                'files': files,
                'count': len(files),
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            self.send_json_response({
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }, 500)
    
    def send_json_response(self, data, status_code=200):
        """Send JSON response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        
        response = json.dumps(data, indent=2)
        self.wfile.write(response.encode('utf-8'))

def start_file_server():
    """Start the file server"""
    PORT = 8080
    
    print("📁 Starting GLB File Server")
    print("=" * 25)
    print(f"🌐 Server: http://localhost:{PORT}")
    print(f"📋 Files: http://localhost:{PORT}/api/files")
    print(f"🎮 GLB Files: http://localhost:{PORT}/backend/outputs/models/")
    print("\n💡 This server serves your generated GLB files!")
    print("🔗 Use this with your web interface to load real characters")
    print("⌨️ Press Ctrl+C to stop")
    print("=" * 25)
    
    try:
        with socketserver.TCPServer(("", PORT), FileServerHandler) as httpd:
            print(f"✅ File server started on port {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 File server stopped")
    except Exception as e:
        print(f"\n❌ Server error: {e}")

if __name__ == "__main__":
    start_file_server()
