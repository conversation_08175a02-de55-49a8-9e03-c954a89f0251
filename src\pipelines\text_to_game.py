"""
Pipeline for converting text prompts to video games.
"""

import os
import tempfile
import json
import time
import shutil

# Import integration modules
from integrations.huggingface.text_to_3d import HuggingFaceTextTo3D
from integrations.huggingface.text_to_image import HuggingFaceTextToImage
from integrations.comfyui.api import Comfy<PERSON><PERSON><PERSON>
from integrations.blender.automation import B<PERSON>der<PERSON><PERSON><PERSON>
from integrations.unity.automation import UnityAutomation

class TextToGamePipeline:
    """
    Pipeline for converting text prompts to video games.
    
    This pipeline uses:
    1. HuggingFace models for generating 3D models and textures from text
    2. ComfyUI for additional image generation
    3. Blender for 3D model processing
    4. Unity for game creation
    """
    
    def __init__(self):
        """Initialize the text to game pipeline."""
        self.huggingface_3d = HuggingFaceTextTo3D()
        self.huggingface_image = HuggingFaceTextToImage()
        self.comfyui = ComfyUIAPI()
        self.blender = BlenderAutomation()
        self.unity = UnityAutomation()
        
        # Create output directories if they don't exist
        os.makedirs('output/3d_models', exist_ok=True)
        os.makedirs('output/images', exist_ok=True)
        os.makedirs('output/games', exist_ok=True)
    
    def process(self, input_data, parameters):
        """
        Process a text prompt to generate a video game.
        
        Args:
            input_data: Text prompt describing the game
            parameters: Additional parameters for the generation
                - game_type: Type of game to create (default: 'simple_3d')
                - num_assets: Number of assets to generate (default: 5)
                - engine: Game engine to use (default: 'unity')
                
        Returns:
            dict: Result containing paths to generated files
        """
        try:
            # Get parameters
            game_type = parameters.get('game_type', 'simple_3d')
            num_assets = min(int(parameters.get('num_assets', 5)), 10)  # Limit to 10 assets max
            engine = parameters.get('engine', 'unity')
            
            # Create a temporary directory for game assets
            temp_dir = tempfile.mkdtemp()
            assets_dir = os.path.join(temp_dir, 'assets')
            os.makedirs(assets_dir, exist_ok=True)
            
            # Step 1: Parse the text prompt to extract game elements
            game_elements = self._parse_game_elements(input_data)
            
            # Step 2: Generate assets for each game element
            asset_paths = []
            for i, element in enumerate(game_elements[:num_assets]):
                print(f"Generating asset {i+1}/{len(game_elements[:num_assets])}: {element}")
                
                # Generate 3D model
                mesh_path = self.huggingface_3d.generate_mesh_from_text(
                    text_prompt=element,
                    model='shap-e'
                )
                
                # Generate reference image
                reference_image_path = self.comfyui.generate_image(
                    prompt=element,
                    negative_prompt="low quality, bad anatomy, worst quality, low resolution",
                    width=512,
                    height=512,
                    steps=30
                )
                
                # Generate textures
                texture_paths = self.comfyui.generate_textures(
                    mesh_path=mesh_path,
                    reference_image_path=reference_image_path,
                    quality='medium'
                )
                
                # Process model in Blender
                processed_model_path = os.path.join(assets_dir, f"asset_{i}.glb")
                final_model_path = self.blender.process_model(
                    mesh_path=mesh_path,
                    texture_paths=texture_paths,
                    output_path=processed_model_path,
                    optimize=True
                )
                
                asset_paths.append(final_model_path)
            
            # Step 3: Generate environment
            print("Generating game environment...")
            environment_prompt = f"A game environment for {input_data}"
            environment_mesh_path = self.huggingface_3d.generate_mesh_from_text(
                text_prompt=environment_prompt,
                model='shap-e'
            )
            
            environment_image_path = self.comfyui.generate_image(
                prompt=environment_prompt,
                negative_prompt="low quality, bad anatomy, worst quality, low resolution",
                width=1024,
                height=1024,
                steps=30
            )
            
            environment_texture_paths = self.comfyui.generate_textures(
                mesh_path=environment_mesh_path,
                reference_image_path=environment_image_path,
                quality='high'
            )
            
            environment_model_path = os.path.join(assets_dir, "environment.glb")
            final_environment_path = self.blender.process_model(
                mesh_path=environment_mesh_path,
                texture_paths=environment_texture_paths,
                output_path=environment_model_path,
                optimize=True
            )
            
            asset_paths.append(final_environment_path)
            
            # Step 4: Create game using Unity
            print("Creating game in Unity...")
            game_name = f"game_{int(time.time())}"
            output_path = os.path.join('output/games', game_name)
            
            if engine.lower() == 'unity':
                game_path = self.unity.create_game(
                    game_name=game_name,
                    asset_paths=asset_paths,
                    output_path=output_path
                )
            else:
                # For other engines, we would need to implement similar functionality
                raise ValueError(f"Engine {engine} not supported yet")
            
            # Generate a preview image
            preview_image = self.blender.render_preview(final_environment_path)
            
            # Clean up temporary directory
            shutil.rmtree(temp_dir)
            
            # Return results
            return {
                'game_path': game_path,
                'asset_paths': asset_paths,
                'preview_image': preview_image
            }
            
        except Exception as e:
            print(f"Error in text to game pipeline: {str(e)}")
            raise
    
    def _parse_game_elements(self, text_prompt):
        """
        Parse a text prompt to extract game elements.
        
        This is a simple implementation that just splits the prompt into parts.
        In a real implementation, this would use more sophisticated NLP techniques.
        
        Args:
            text_prompt: Text prompt describing the game
            
        Returns:
            list: List of game elements
        """
        # Simple parsing - split by commas and 'and'
        elements = []
        
        # Replace 'and' with comma for splitting
        text = text_prompt.replace(' and ', ', ')
        
        # Split by comma
        parts = text.split(',')
        
        # Clean up parts
        for part in parts:
            part = part.strip()
            if part and len(part) > 3:  # Ignore very short parts
                elements.append(part)
        
        # If no elements were found, use the whole prompt
        if not elements:
            elements = [text_prompt]
        
        return elements
