"""
Main application file for the AI Content Generation Pipeline.
This serves as the entry point for the application.
"""

import os
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import integration modules
from backend.server import create_api_routes
from backend.pipeline_manager import PipelineManager

# Create Flask app
app = Flask(__name__, static_folder='frontend/build', static_url_path='/')
CORS(app)

# Initialize pipeline manager
pipeline_manager = PipelineManager()

# Register API routes
create_api_routes(app, pipeline_manager)

# Serve frontend
@app.route('/')
def serve_frontend():
    return app.send_static_file('index.html')

# Error handling
@app.errorhandler(404)
def not_found(e):
    return app.send_static_file('index.html')

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=True)
