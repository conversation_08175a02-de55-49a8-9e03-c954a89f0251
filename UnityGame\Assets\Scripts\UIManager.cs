using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UIManager : MonoBehaviour
{
    // UI elements
    public Text scoreText;
    public Text healthText;
    public Slider healthSlider;
    public GameObject mainMenuPanel;
    public GameObject gamePanel;
    public GameObject pauseMenuPanel;
    public GameObject gameOverPanel;
    
    // Buttons
    public Button startButton;
    public Button resumeButton;
    public Button restartButton;
    public Button quitButton;
    
    // Game manager reference
    private GameManager gameManager;
    
    // Start is called before the first frame update
    void Start()
    {
        // Get game manager reference
        gameManager = GameManager.instance;
        
        // Set up button listeners
        if (startButton != null)
        {
            startButton.onClick.AddListener(StartGame);
        }
        
        if (resumeButton != null)
        {
            resumeButton.onClick.AddListener(ResumeGame);
        }
        
        if (restartButton != null)
        {
            restartButton.onClick.AddListener(RestartGame);
        }
        
        if (quitButton != null)
        {
            quitButton.onClick.AddListener(QuitGame);
        }
    }

    // Update is called once per frame
    void Update()
    {
        // Update score text
        if (scoreText != null && gameManager != null)
        {
            scoreText.text = "Score: " + gameManager.score.ToString();
        }
        
        // Update health text and slider
        PlayerHealth playerHealth = FindObjectOfType<PlayerHealth>();
        if (playerHealth != null)
        {
            if (healthText != null)
            {
                healthText.text = "Health: " + playerHealth.currentHealth.ToString() + "/" + playerHealth.maxHealth.ToString();
            }
            
            if (healthSlider != null)
            {
                healthSlider.value = (float)playerHealth.currentHealth / playerHealth.maxHealth;
            }
        }
    }
    
    // Start the game
    public void StartGame()
    {
        if (gameManager != null)
        {
            gameManager.StartGame();
        }
    }
    
    // Resume the game
    public void ResumeGame()
    {
        if (gameManager != null)
        {
            gameManager.SetGameState(GameManager.GameState.Playing);
        }
    }
    
    // Restart the game
    public void RestartGame()
    {
        if (gameManager != null)
        {
            gameManager.RestartGame();
        }
    }
    
    // Quit the game
    public void QuitGame()
    {
        if (gameManager != null)
        {
            gameManager.QuitGame();
        }
    }
    
    // Show a message
    public void ShowMessage(string message, float duration = 3f)
    {
        StartCoroutine(ShowMessageCoroutine(message, duration));
    }
    
    // Show a message for a specified duration
    IEnumerator ShowMessageCoroutine(string message, float duration)
    {
        // Create a message panel
        GameObject messagePanel = new GameObject("MessagePanel");
        messagePanel.transform.SetParent(transform, false);
        
        // Add a background image
        Image backgroundImage = messagePanel.AddComponent<Image>();
        backgroundImage.color = new Color(0f, 0f, 0f, 0.5f);
        
        // Set the panel size and position
        RectTransform panelRect = messagePanel.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.5f, 0.5f);
        panelRect.anchorMax = new Vector2(0.5f, 0.5f);
        panelRect.pivot = new Vector2(0.5f, 0.5f);
        panelRect.sizeDelta = new Vector2(400f, 100f);
        
        // Create a text object
        GameObject textObject = new GameObject("MessageText");
        textObject.transform.SetParent(messagePanel.transform, false);
        
        // Add text component
        Text messageText = textObject.AddComponent<Text>();
        messageText.text = message;
        messageText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        messageText.fontSize = 24;
        messageText.color = Color.white;
        messageText.alignment = TextAnchor.MiddleCenter;
        
        // Set the text size and position
        RectTransform textRect = textObject.GetComponent<RectTransform>();
        textRect.anchorMin = new Vector2(0f, 0f);
        textRect.anchorMax = new Vector2(1f, 1f);
        textRect.pivot = new Vector2(0.5f, 0.5f);
        textRect.sizeDelta = Vector2.zero;
        
        // Wait for the specified duration
        yield return new WaitForSeconds(duration);
        
        // Destroy the message panel
        Destroy(messagePanel);
    }
}
