"""
Demo script for custom workflows and direct editing.

This script demonstrates how to:
1. Create custom ComfyUI workflows
2. Directly edit 3D models in Blender
3. Directly edit and create content in Unreal Engine
"""

import os
import sys
import argparse
import time
from pathlib import Path

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)

# Import our modules
from src.workflow_generators.comfyui_workflow_generator import ComfyUIWorkflowGenerator
from src.integrations.blender.direct_editing import BlenderDirectEditor
from src.integrations.unreal.direct_editing import UnrealDirectEditor
from src.integrated_workflow import IntegratedWorkflow

def demo_custom_workflow():
    """Demonstrate creating custom ComfyUI workflows."""
    print("Demonstrating custom ComfyUI workflow creation...")
    
    # Initialize the workflow generator
    generator = ComfyUIWorkflowGenerator()
    
    # Create an image-to-3D workflow
    print("\nCreating an image-to-3D workflow...")
    workflow = generator.create_image_to_3d_workflow(use_mickmumpitz=True)
    workflow_path = generator.save_workflow(workflow, "custom_image_to_3d_workflow.json")
    
    # Create a text-to-3D workflow
    print("\nCreating a text-to-3D workflow...")
    workflow = generator.create_text_to_3d_workflow(use_mickmumpitz=True)
    workflow_path = generator.save_workflow(workflow, "custom_text_to_3d_workflow.json")
    
    # Create a character generation workflow
    print("\nCreating a character generation workflow...")
    workflow = generator.create_character_generation_workflow(use_mickmumpitz=True)
    workflow_path = generator.save_workflow(workflow, "custom_character_workflow.json")
    
    print("\nCustom workflows created successfully!")
    print("You can load these workflows in ComfyUI to generate content.")

def demo_blender_editing():
    """Demonstrate direct editing in Blender."""
    print("Demonstrating direct editing in Blender...")
    
    # Initialize the Blender editor
    editor = BlenderDirectEditor()
    
    # Ask the user if they want to create a new model or edit an existing one
    print("\nDo you want to create a new model or edit an existing one?")
    print("1. Create a new model")
    print("2. Edit an existing model")
    choice = input("Enter your choice (1 or 2): ")
    
    if choice == "1":
        # Create a new model
        output_path = os.path.join(project_root, "output", "3d_models", f"new_model_{int(time.time())}.glb")
        print(f"\nCreating a new model and saving to: {output_path}")
        
        # Ask for the edit mode
        print("\nSelect the edit mode:")
        print("1. Sculpt mode (for organic modeling)")
        print("2. Edit mode (for precise vertex/edge/face editing)")
        print("3. Texture mode (for painting textures)")
        mode_choice = input("Enter your choice (1, 2, or 3): ")
        
        edit_mode = "sculpt"
        if mode_choice == "2":
            edit_mode = "edit"
        elif mode_choice == "3":
            edit_mode = "texture"
        
        # Create a new model
        process = editor.create_new_model(output_path=output_path, edit_mode=edit_mode)
        
        print("\nBlender has been opened for creating a new model.")
        print("When you're done, save the model and close Blender.")
        
    elif choice == "2":
        # Edit an existing model
        print("\nEnter the path to the 3D model you want to edit:")
        model_path = input("Model path: ")
        
        if not os.path.exists(model_path):
            print(f"Error: Model file not found: {model_path}")
            return
        
        # Ask for the edit mode
        print("\nSelect the edit mode:")
        print("1. Sculpt mode (for organic modeling)")
        print("2. Edit mode (for precise vertex/edge/face editing)")
        print("3. Texture mode (for painting textures)")
        mode_choice = input("Enter your choice (1, 2, or 3): ")
        
        edit_mode = "sculpt"
        if mode_choice == "2":
            edit_mode = "edit"
        elif mode_choice == "3":
            edit_mode = "texture"
        
        # Ask for the output path
        print("\nEnter the path to save the edited model (leave empty to overwrite the original):")
        output_path = input("Output path: ")
        
        if not output_path:
            output_path = model_path
        
        # Edit the existing model
        process = editor.edit_existing_model(model_path, edit_mode=edit_mode, output_path=output_path)
        
        print("\nBlender has been opened for editing the model.")
        print("When you're done, save the model and close Blender.")
    
    else:
        print("Invalid choice.")

def demo_unreal_editing():
    """Demonstrate direct editing in Unreal Engine."""
    print("Demonstrating direct editing in Unreal Engine...")
    
    # Check if Unreal Engine project path is set
    unreal_project_path = os.environ.get('UNREAL_PROJECT_PATH')
    if not unreal_project_path:
        print("\nError: UNREAL_PROJECT_PATH environment variable not set.")
        print("Please set the UNREAL_PROJECT_PATH environment variable to the path of your Unreal Engine project.")
        return
    
    # Initialize the Unreal Engine editor
    editor = UnrealDirectEditor()
    
    # Ask the user what they want to do
    print("\nWhat do you want to do in Unreal Engine?")
    print("1. Open Unreal Engine")
    print("2. Import an asset")
    print("3. Create a new level")
    choice = input("Enter your choice (1, 2, or 3): ")
    
    if choice == "1":
        # Open Unreal Engine
        print("\nOpening Unreal Engine...")
        process = editor.open_unreal_editor()
        
        print("\nUnreal Engine has been opened.")
        print("You can now work with your project in Unreal Engine.")
    
    elif choice == "2":
        # Import an asset
        print("\nEnter the path to the asset you want to import:")
        asset_path = input("Asset path: ")
        
        if not os.path.exists(asset_path):
            print(f"Error: Asset file not found: {asset_path}")
            return
        
        # Ask for the destination path
        print("\nEnter the destination path in the Unreal Engine project (default: /Game/ImportedAssets):")
        destination_path = input("Destination path: ")
        
        if not destination_path:
            destination_path = "/Game/ImportedAssets"
        
        # Ask for the asset type
        print("\nSelect the asset type:")
        print("1. Static Mesh")
        print("2. Skeletal Mesh")
        print("3. Animation")
        print("4. Texture")
        type_choice = input("Enter your choice (1, 2, 3, or 4): ")
        
        asset_type = "StaticMesh"
        if type_choice == "2":
            asset_type = "SkeletalMesh"
        elif type_choice == "3":
            asset_type = "Animation"
        elif type_choice == "4":
            asset_type = "Texture"
        
        # Import the asset
        success = editor.import_asset(asset_path, destination_path=destination_path, asset_type=asset_type)
        
        if success:
            print(f"\nAsset imported successfully: {asset_path}")
            print(f"Destination: {destination_path}")
        else:
            print(f"\nFailed to import asset: {asset_path}")
    
    elif choice == "3":
        # Create a new level
        print("\nEnter the name of the new level:")
        level_name = input("Level name: ")
        
        # Ask for the template
        print("\nSelect the template:")
        print("1. Default")
        print("2. Empty")
        print("3. Custom (path to an existing level)")
        template_choice = input("Enter your choice (1, 2, or 3): ")
        
        template = "Default"
        if template_choice == "2":
            template = "Empty"
        elif template_choice == "3":
            print("\nEnter the path to the template level:")
            template = input("Template path: ")
        
        # Ask for the save path
        print("\nEnter the save path in the Unreal Engine project (default: /Game/Maps):")
        save_path = input("Save path: ")
        
        if not save_path:
            save_path = "/Game/Maps"
        
        # Create the level
        success = editor.create_level(level_name, template=template, save_path=save_path)
        
        if success:
            print(f"\nLevel created successfully: {level_name}")
            print(f"Save path: {save_path}")
        else:
            print(f"\nFailed to create level: {level_name}")
    
    else:
        print("Invalid choice.")

def demo_integrated_workflow():
    """Demonstrate the integrated workflow."""
    print("Demonstrating the integrated workflow...")
    
    # Initialize the integrated workflow
    workflow = IntegratedWorkflow()
    
    # Ask the user what kind of pipeline they want to create
    print("\nWhat kind of pipeline do you want to create?")
    print("1. Image to 3D")
    print("2. Text to 3D")
    print("3. Text to Image (Character)")
    choice = input("Enter your choice (1, 2, or 3): ")
    
    input_type = "image"
    output_type = "3d"
    
    if choice == "1":
        input_type = "image"
        output_type = "3d"
    elif choice == "2":
        input_type = "text"
        output_type = "3d"
    elif choice == "3":
        input_type = "text"
        output_type = "image"
    else:
        print("Invalid choice. Using default: Image to 3D.")
    
    # Ask for the input path
    print(f"\nEnter the path to the {input_type} input file:")
    input_path = input("Input path: ")
    
    # Ask for the output path
    print(f"\nEnter the path to save the {output_type} output:")
    output_path = input("Output path: ")
    
    # Create the pipeline
    result = workflow.create_complete_pipeline(input_type, output_type, input_path=input_path, output_path=output_path)
    
    if result['success']:
        print(f"\nPipeline created successfully.")
        print(f"Workflow: {result['workflow_path']}")
        
        # Provide next steps
        print("\nNext steps:")
        print("1. Open ComfyUI and load the workflow file")
        print("2. Run the workflow to generate the output")
        
        if output_type == "3d":
            print("3. Edit the generated 3D model in Blender:")
            print(f"   python src/integrated_workflow.py edit-in-blender --model-path {output_path}")
            
            print("4. Import the edited model into Unreal Engine:")
            print(f"   python src/integrated_workflow.py import-to-unreal --asset-path {output_path}")
    else:
        print(f"\nFailed to create pipeline: {result.get('error', 'Unknown error')}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Demo for custom workflows and direct editing')
    parser.add_argument('--workflow', action='store_true', help='Demonstrate custom ComfyUI workflow creation')
    parser.add_argument('--blender', action='store_true', help='Demonstrate direct editing in Blender')
    parser.add_argument('--unreal', action='store_true', help='Demonstrate direct editing in Unreal Engine')
    parser.add_argument('--integrated', action='store_true', help='Demonstrate the integrated workflow')
    args = parser.parse_args()
    
    # If no arguments are provided, show help
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    # Run the requested demos
    if args.workflow:
        demo_custom_workflow()
    
    if args.blender:
        demo_blender_editing()
    
    if args.unreal:
        demo_unreal_editing()
    
    if args.integrated:
        demo_integrated_workflow()

if __name__ == "__main__":
    main()
