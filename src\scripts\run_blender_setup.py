"""
<PERSON><PERSON><PERSON> to run <PERSON>lender with our ComfyUI render setup.

This script automates the process of setting up Blender for ComfyUI rendering
by running our setup script in Blender.
"""

import os
import sys
import argparse
import subprocess
import tempfile
from pathlib import Path

def create_blender_script(blender_file, output_dir, frame_start, frame_end, resolution_x, resolution_y, render_type):
    """
    Create a temporary Python script to run in Blender.
    
    Args:
        blender_file: Path to the Blender file
        output_dir: Directory to save the render passes
        frame_start: First frame to render
        frame_end: Last frame to render
        resolution_x: X resolution of the render
        resolution_y: Y resolution of the render
        render_type: Type of render ('animation' or 'still')
        
    Returns:
        str: Path to the temporary script
    """
    # Get the absolute path to the project directory
    project_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    
    # Create a temporary script
    script_content = f"""
import sys
import os
import bpy

# Add project directory to Python path
sys.path.append(r'{project_dir}')

# Import our setup module
from src.integrations.blender.comfyui_render_setup import BlenderComfyUISetup

# Open the Blender file if provided
if '{blender_file}':
    bpy.ops.wm.open_mainfile(filepath=r'{blender_file}')

# Create setup object
setup = BlenderComfyUISetup(output_dir=r'{output_dir}')

# Set up all passes
setup.setup_all_passes()

# Render animation or still
if '{render_type}' == 'animation':
    setup.render_animation(frame_start={frame_start}, frame_end={frame_end}, resolution_x={resolution_x}, resolution_y={resolution_y})
else:
    setup.render_still(resolution_x={resolution_x}, resolution_y={resolution_y})

print("Blender setup and rendering complete.")
"""
    
    # Write the script to a temporary file
    with tempfile.NamedTemporaryFile(suffix='.py', delete=False) as temp:
        temp.write(script_content.encode('utf-8'))
        temp_script_path = temp.name
    
    return temp_script_path

def run_blender(blender_path, script_path):
    """
    Run Blender with the specified script.
    
    Args:
        blender_path: Path to the Blender executable
        script_path: Path to the Python script to run in Blender
        
    Returns:
        int: Return code from Blender
    """
    cmd = [
        blender_path,
        '--background',
        '--python', script_path
    ]
    
    try:
        process = subprocess.run(cmd, check=True)
        return process.returncode
    except subprocess.CalledProcessError as e:
        print(f"Error running Blender: {e}")
        return e.returncode

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Run Blender with ComfyUI render setup')
    parser.add_argument('--blender-path', type=str, default=os.environ.get('BLENDER_PATH', 'blender'),
                        help='Path to the Blender executable')
    parser.add_argument('--blender-file', type=str, default='',
                        help='Path to the Blender file to open')
    parser.add_argument('--output-dir', type=str, default='output/blender',
                        help='Directory to save the render passes')
    parser.add_argument('--frame-start', type=int, default=1,
                        help='First frame to render')
    parser.add_argument('--frame-end', type=int, default=250,
                        help='Last frame to render')
    parser.add_argument('--resolution-x', type=int, default=512,
                        help='X resolution of the render')
    parser.add_argument('--resolution-y', type=int, default=512,
                        help='Y resolution of the render')
    parser.add_argument('--render-type', type=str, choices=['animation', 'still'], default='animation',
                        help='Type of render')
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Create the Blender script
    script_path = create_blender_script(
        args.blender_file,
        args.output_dir,
        args.frame_start,
        args.frame_end,
        args.resolution_x,
        args.resolution_y,
        args.render_type
    )
    
    try:
        # Run Blender with the script
        print(f"Running Blender with setup script...")
        return_code = run_blender(args.blender_path, script_path)
        
        if return_code == 0:
            print(f"Blender setup and rendering complete.")
            print(f"Output directory: {args.output_dir}")
            print("\nNext steps:")
            print("1. Open ComfyUI")
            print("2. Load mickmumpitz's 3D rendering workflow")
            print("3. Update the input paths to point to the output directories")
            print("4. Run the workflow to generate the final rendered output")
        else:
            print(f"Blender exited with code {return_code}")
    finally:
        # Clean up the temporary script
        if os.path.exists(script_path):
            os.unlink(script_path)

if __name__ == '__main__':
    main()
