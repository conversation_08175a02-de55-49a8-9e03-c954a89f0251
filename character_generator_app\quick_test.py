"""
Quick test script for the 3D Character Generator API
"""

import requests
import json
import time

API_BASE = "http://localhost:5000"

def test_api():
    """Test the API endpoints."""
    print("🎮 Testing 3D Character Generator API")
    print("=" * 40)
    
    # Test API connection
    try:
        response = requests.get(f"{API_BASE}/")
        print("✅ API is running!")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"❌ API connection failed: {e}")
        return
    
    # Test character generation
    print("\n🚀 Testing character generation...")
    data = {
        "text_prompt": "A friendly cartoon robot with blue and silver colors",
        "image_path": "",
        "style_options": {
            "realistic": False,
            "cartoon": True,
            "anime": False,
            "lowPoly": False
        }
    }
    
    try:
        response = requests.post(f"{API_BASE}/api/generate", json=data)
        if response.status_code == 200:
            result = response.json()
            job_id = result['job_id']
            print(f"✅ Character generation started!")
            print(f"Job ID: {job_id}")
            
            # Monitor progress
            print("\n⏳ Monitoring progress...")
            for i in range(30):  # Check for up to 60 seconds
                try:
                    status_response = requests.get(f"{API_BASE}/api/status/{job_id}")
                    if status_response.status_code == 200:
                        job = status_response.json()
                        print(f"Status: {job['status']} - Progress: {job.get('progress', 0)}%")
                        
                        if job['status'] == 'completed':
                            print("✅ Character generation completed!")
                            
                            # Test download
                            print("\n📥 Testing file download...")
                            download_response = requests.get(f"{API_BASE}/api/download/{job_id}/model")
                            if download_response.status_code == 200:
                                with open(f"test_character_{job_id[:8]}.glb", 'wb') as f:
                                    f.write(download_response.content)
                                print(f"✅ Model downloaded: test_character_{job_id[:8]}.glb")
                            else:
                                print("❌ Download failed")
                            break
                        elif job['status'] == 'error':
                            print(f"❌ Generation failed: {job.get('error', 'Unknown error')}")
                            break
                    
                    time.sleep(2)
                except Exception as e:
                    print(f"Error checking status: {e}")
                    break
            else:
                print("⏰ Timeout waiting for completion")
        else:
            print(f"❌ Generation failed: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Generation error: {e}")
    
    # Test jobs list
    print("\n📋 Testing jobs list...")
    try:
        response = requests.get(f"{API_BASE}/api/jobs")
        if response.status_code == 200:
            jobs = response.json().get('jobs', [])
            print(f"✅ Found {len(jobs)} jobs")
            for job in jobs[:3]:  # Show first 3 jobs
                print(f"  - {job.get('id', 'unknown')[:8]}: {job.get('status', 'unknown')} ({job.get('progress', 0)}%)")
        else:
            print("❌ Failed to get jobs list")
    except Exception as e:
        print(f"❌ Jobs list error: {e}")
    
    print("\n🎉 API test completed!")

if __name__ == "__main__":
    test_api()
