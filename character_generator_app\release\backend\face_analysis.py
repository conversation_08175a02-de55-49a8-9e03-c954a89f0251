#!/usr/bin/env python3
"""
Focused face analysis module for character generation
"""

import cv2
import numpy as np
import os
import json
from PIL import Image

class FaceAnalyzer:
    """Simplified but effective face analysis for character generation"""
    
    def __init__(self):
        self.face_cascade = None
        self.eye_cascade = None
        self.init_opencv()
    
    def init_opencv(self):
        """Initialize OpenCV face detection"""
        try:
            cascade_path = cv2.data.haarcascades
            
            self.face_cascade = cv2.CascadeClassifier(
                os.path.join(cascade_path, 'haarcascade_frontalface_default.xml')
            )
            self.eye_cascade = cv2.CascadeClassifier(
                os.path.join(cascade_path, 'haarcascade_eye.xml')
            )
            
            print("✅ Face detection initialized")
            
        except Exception as e:
            print(f"⚠️ Face detection initialization error: {e}")
    
    def analyze_face(self, image_path):
        """Main face analysis function"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Could not load image")
            
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Detect faces
            faces = self.detect_faces(gray)
            
            if len(faces) == 0:
                print("⚠️ No faces detected, analyzing full image")
                return self.analyze_full_image(image_rgb)
            
            # Use largest face
            face = max(faces, key=lambda f: f[2] * f[3])
            x, y, w, h = face
            
            # Extract face region
            face_region = image_rgb[y:y+h, x:x+w]
            face_gray = gray[y:y+h, x:x+w]
            
            # Analyze face features
            analysis = {
                'face_detected': True,
                'face_bounds': {'x': x, 'y': y, 'width': w, 'height': h},
                'image_size': {'width': image.shape[1], 'height': image.shape[0]},
                'face_shape': self.analyze_face_shape(face_region),
                'skin_tone': self.extract_skin_tone(face_region),
                'eyes': self.analyze_eyes(face_gray),
                'facial_proportions': self.calculate_proportions(face_region),
                'symmetry': self.calculate_symmetry(face_region),
                'color_palette': self.extract_colors(face_region)
            }
            
            return analysis
            
        except Exception as e:
            print(f"Face analysis error: {e}")
            return self.get_default_analysis()
    
    def detect_faces(self, gray_image):
        """Detect faces in image"""
        if self.face_cascade is None:
            return []
        
        try:
            faces = self.face_cascade.detectMultiScale(
                gray_image,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30)
            )
            return faces
        except Exception as e:
            print(f"Face detection error: {e}")
            return []
    
    def analyze_face_shape(self, face_region):
        """Analyze face shape"""
        h, w = face_region.shape[:2]
        aspect_ratio = w / h
        
        if aspect_ratio > 0.9:
            shape = "round"
        elif aspect_ratio < 0.7:
            shape = "oval"
        elif aspect_ratio < 0.8:
            shape = "long"
        else:
            shape = "square"
        
        return {
            'shape': shape,
            'aspect_ratio': float(aspect_ratio),
            'width': w,
            'height': h
        }
    
    def extract_skin_tone(self, face_region):
        """Extract skin tone from face"""
        try:
            # Convert to HSV for better skin detection
            hsv = cv2.cvtColor(face_region, cv2.COLOR_RGB2HSV)
            
            # Skin color range in HSV
            lower_skin = np.array([0, 20, 70], dtype=np.uint8)
            upper_skin = np.array([20, 255, 255], dtype=np.uint8)
            
            # Create skin mask
            skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            # Get skin pixels
            skin_pixels = face_region[skin_mask > 0]
            
            if len(skin_pixels) > 50:
                avg_color = np.mean(skin_pixels, axis=0)
            else:
                # Fallback to center region
                h, w = face_region.shape[:2]
                center_region = face_region[h//3:2*h//3, w//3:2*w//3]
                avg_color = np.mean(center_region.reshape(-1, 3), axis=0)
            
            return {
                'rgb': avg_color.tolist(),
                'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
                'tone_category': self.categorize_skin_tone(avg_color)
            }
            
        except Exception as e:
            print(f"Skin tone extraction error: {e}")
            return {'rgb': [200, 180, 160], 'hex': '#c8b4a0', 'tone_category': 'medium'}
    
    def categorize_skin_tone(self, rgb_color):
        """Categorize skin tone"""
        brightness = np.mean(rgb_color)
        
        if brightness < 100:
            return 'dark'
        elif brightness < 160:
            return 'medium'
        else:
            return 'light'
    
    def analyze_eyes(self, face_gray):
        """Analyze eye features"""
        eyes_data = {
            'detected': False,
            'count': 0,
            'positions': [],
            'eye_distance': 0
        }
        
        try:
            if self.eye_cascade is not None:
                eyes = self.eye_cascade.detectMultiScale(face_gray, 1.1, 5)
                
                eyes_data['detected'] = len(eyes) > 0
                eyes_data['count'] = len(eyes)
                eyes_data['positions'] = eyes.tolist()
                
                if len(eyes) >= 2:
                    # Calculate distance between first two eyes
                    eye1_center = (eyes[0][0] + eyes[0][2]//2, eyes[0][1] + eyes[0][3]//2)
                    eye2_center = (eyes[1][0] + eyes[1][2]//2, eyes[1][1] + eyes[1][3]//2)
                    
                    distance = np.sqrt((eye1_center[0] - eye2_center[0])**2 + 
                                     (eye1_center[1] - eye2_center[1])**2)
                    eyes_data['eye_distance'] = float(distance)
        
        except Exception as e:
            print(f"Eye analysis error: {e}")
        
        return eyes_data
    
    def calculate_proportions(self, face_region):
        """Calculate facial proportions"""
        h, w = face_region.shape[:2]
        
        # Divide face into thirds
        upper_third = h // 3
        middle_third = h // 3
        lower_third = h - 2 * (h // 3)
        
        return {
            'face_width': w,
            'face_height': h,
            'upper_third': upper_third,
            'middle_third': middle_third,
            'lower_third': lower_third,
            'width_to_height_ratio': w / h
        }
    
    def calculate_symmetry(self, face_region):
        """Calculate facial symmetry"""
        try:
            h, w = face_region.shape[:2]
            
            # Convert to grayscale if needed
            if len(face_region.shape) == 3:
                gray_face = cv2.cvtColor(face_region, cv2.COLOR_RGB2GRAY)
            else:
                gray_face = face_region
            
            # Split face in half
            left_half = gray_face[:, :w//2]
            right_half = gray_face[:, w//2:]
            
            # Flip right half
            right_half_flipped = cv2.flip(right_half, 1)
            
            # Resize to match
            min_w = min(left_half.shape[1], right_half_flipped.shape[1])
            left_half = left_half[:, :min_w]
            right_half_flipped = right_half_flipped[:, :min_w]
            
            # Calculate difference
            diff = cv2.absdiff(left_half, right_half_flipped)
            symmetry_score = 1.0 - (np.mean(diff) / 255.0)
            
            return {
                'symmetry_score': float(max(0, min(1, symmetry_score)))
            }
            
        except Exception as e:
            print(f"Symmetry calculation error: {e}")
            return {'symmetry_score': 0.8}
    
    def extract_colors(self, face_region):
        """Extract dominant colors from face"""
        try:
            # Reshape to list of pixels
            pixels = face_region.reshape(-1, 3)
            
            # Sample colors from different regions
            h, w = face_region.shape[:2]
            
            # Sample from forehead, cheeks, chin
            regions = [
                face_region[:h//3, :],  # Forehead
                face_region[h//3:2*h//3, :w//3],  # Left cheek
                face_region[h//3:2*h//3, 2*w//3:],  # Right cheek
                face_region[2*h//3:, :]  # Chin area
            ]
            
            colors = []
            for region in regions:
                if region.size > 0:
                    avg_color = np.mean(region.reshape(-1, 3), axis=0)
                    colors.append(avg_color.tolist())
            
            return {
                'dominant_colors': colors,
                'hex_colors': ['#{:02x}{:02x}{:02x}'.format(int(c[0]), int(c[1]), int(c[2])) for c in colors]
            }
            
        except Exception as e:
            print(f"Color extraction error: {e}")
            return {
                'dominant_colors': [[200, 180, 160]],
                'hex_colors': ['#c8b4a0']
            }
    
    def analyze_full_image(self, image_rgb):
        """Analyze full image when no face detected"""
        h, w = image_rgb.shape[:2]
        
        # Use center region as "face"
        center_region = image_rgb[h//4:3*h//4, w//4:3*w//4]
        
        return {
            'face_detected': False,
            'face_bounds': None,
            'image_size': {'width': w, 'height': h},
            'face_shape': {'shape': 'unknown', 'aspect_ratio': 1.0},
            'skin_tone': self.extract_average_color(center_region),
            'eyes': {'detected': False, 'count': 0},
            'facial_proportions': {'width_to_height_ratio': 1.0},
            'symmetry': {'symmetry_score': 0.8},
            'color_palette': self.extract_colors(center_region),
            'full_image_analysis': True
        }
    
    def extract_average_color(self, region):
        """Extract average color from region"""
        avg_color = np.mean(region.reshape(-1, 3), axis=0)
        return {
            'rgb': avg_color.tolist(),
            'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
            'tone_category': self.categorize_skin_tone(avg_color)
        }
    
    def get_default_analysis(self):
        """Return default analysis when everything fails"""
        return {
            'face_detected': False,
            'analysis_failed': True,
            'face_shape': {'shape': 'oval', 'aspect_ratio': 0.75},
            'skin_tone': {'rgb': [200, 180, 160], 'hex': '#c8b4a0', 'tone_category': 'medium'},
            'eyes': {'detected': False, 'count': 0},
            'facial_proportions': {'width_to_height_ratio': 0.75},
            'symmetry': {'symmetry_score': 0.8},
            'color_palette': {'dominant_colors': [[200, 180, 160]], 'hex_colors': ['#c8b4a0']}
        }
    
    def save_analysis(self, analysis, output_path):
        """Save analysis results to JSON file"""
        try:
            with open(output_path, 'w') as f:
                json.dump(analysis, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving analysis: {e}")
            return False
