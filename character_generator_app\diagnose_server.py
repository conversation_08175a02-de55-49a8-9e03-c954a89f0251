#!/usr/bin/env python3
"""
Diagnose server startup issues and fix them
"""

import sys
import os
import traceback
import subprocess

def test_imports():
    """Test all required imports"""
    print("🔍 Testing Imports")
    print("=" * 20)
    
    imports_to_test = [
        ('flask', 'Flask'),
        ('flask_cors', 'CORS'),
        ('flask_socketio', 'SocketIO'),
        ('requests', None),
        ('PIL', 'Image'),
        ('cv2', None),
        ('numpy', None),
    ]
    
    failed_imports = []
    
    for module, attr in imports_to_test:
        try:
            if attr:
                exec(f"from {module} import {attr}")
            else:
                exec(f"import {module}")
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
        except Exception as e:
            print(f"⚠️ {module}: {e}")
            failed_imports.append(module)
    
    return failed_imports

def test_backend_imports():
    """Test backend-specific imports"""
    print("\n🔍 Testing Backend Imports")
    print("=" * 25)
    
    sys.path.append('backend')
    
    backend_modules = [
        'config',
        'comfyui_client',
        'huggingface_client',
        'comfyui_workflows'
    ]
    
    failed_modules = []
    
    for module in backend_modules:
        try:
            exec(f"import {module}")
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_modules.append(module)
        except Exception as e:
            print(f"⚠️ {module}: {e}")
            failed_modules.append(module)
    
    return failed_modules

def test_app_creation():
    """Test Flask app creation"""
    print("\n🔍 Testing Flask App Creation")
    print("=" * 30)
    
    try:
        sys.path.append('backend')
        
        # Test basic Flask app
        from flask import Flask
        app = Flask(__name__)
        print("✅ Basic Flask app created")
        
        # Test with CORS
        from flask_cors import CORS
        CORS(app)
        print("✅ CORS enabled")
        
        # Test route creation
        @app.route('/')
        def test_route():
            return {'status': 'ok'}
        
        print("✅ Test route created")
        
        # Test app configuration
        app.config['SECRET_KEY'] = 'test-key'
        app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024
        print("✅ App configured")
        
        return app
        
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")
        traceback.print_exc()
        return None

def test_character_generation():
    """Test character generation functions"""
    print("\n🔍 Testing Character Generation")
    print("=" * 32)
    
    try:
        sys.path.append('backend')
        
        # Import character generation functions
        from app import create_character_glb, analyze_character_prompt
        print("✅ Character functions imported")
        
        # Test character analysis
        character_type = analyze_character_prompt("A blue robot")
        print(f"✅ Character analysis: {character_type['name']}")
        
        # Test GLB creation (small test)
        glb_content = create_character_glb("A test character")
        print(f"✅ GLB creation: {len(glb_content)} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ Character generation failed: {e}")
        traceback.print_exc()
        return False

def check_port_availability():
    """Check if port 5000 is available"""
    print("\n🔍 Checking Port Availability")
    print("=" * 30)
    
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if result == 0:
            print("❌ Port 5000 is in use")
            return False
        else:
            print("✅ Port 5000 is available")
            return True
            
    except Exception as e:
        print(f"⚠️ Error checking port: {e}")
        return True

def install_missing_packages(failed_imports):
    """Install missing packages"""
    if not failed_imports:
        return True
    
    print(f"\n📦 Installing Missing Packages")
    print("=" * 32)
    
    for package in failed_imports:
        try:
            print(f"Installing {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ {package} installed")
            else:
                print(f"❌ {package} installation failed:")
                print(result.stderr)
                
        except Exception as e:
            print(f"❌ Error installing {package}: {e}")
    
    return True

def main():
    """Main diagnostic function"""
    print("🔧 Backend Server Diagnostics")
    print("=" * 30)
    
    # Test basic imports
    failed_imports = test_imports()
    
    # Install missing packages if needed
    if failed_imports:
        install_missing_packages(failed_imports)
        print("\n🔄 Re-testing imports after installation...")
        failed_imports = test_imports()
    
    # Test backend imports
    failed_backend = test_backend_imports()
    
    # Test Flask app creation
    app = test_app_creation()
    
    # Test character generation
    char_gen_ok = test_character_generation()
    
    # Check port availability
    port_ok = check_port_availability()
    
    # Summary
    print("\n📊 Diagnostic Summary")
    print("=" * 20)
    print(f"Basic imports: {'✅' if not failed_imports else '❌'}")
    print(f"Backend imports: {'✅' if not failed_backend else '❌'}")
    print(f"Flask app: {'✅' if app else '❌'}")
    print(f"Character generation: {'✅' if char_gen_ok else '❌'}")
    print(f"Port 5000: {'✅' if port_ok else '❌'}")
    
    if not failed_imports and not failed_backend and app and char_gen_ok and port_ok:
        print("\n🎉 All diagnostics passed! Server should start successfully.")
        return True
    else:
        print("\n⚠️ Some issues detected. Check the details above.")
        return False

if __name__ == "__main__":
    success = main()
    input(f"\nPress Enter to exit... (Success: {success})")
