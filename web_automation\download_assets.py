"""
<PERSON><PERSON><PERSON> to download free 3D assets from websites.
"""

import os
import time
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from tqdm import tqdm

# Create directories for assets
os.makedirs('assets', exist_ok=True)
os.makedirs('assets/models', exist_ok=True)
os.makedirs('assets/textures', exist_ok=True)
os.makedirs('assets/audio', exist_ok=True)

def setup_driver():
    """Set up and return a Chrome WebDriver."""
    chrome_options = Options()
    # Uncomment the line below to run Chrome in headless mode
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-notifications")
    chrome_options.add_argument("--disable-popup-blocking")
    
    # Set up download preferences
    prefs = {
        "download.default_directory": os.path.abspath("assets"),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    # Initialize the Chrome WebDriver
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
    return driver

def download_file(url, filename):
    """Download a file from a URL and save it to the specified filename."""
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    
    with open(filename, 'wb') as file, tqdm(
        desc=filename,
        total=total_size,
        unit='B',
        unit_scale=True,
        unit_divisor=1024,
    ) as bar:
        for data in response.iter_content(chunk_size=1024):
            size = file.write(data)
            bar.update(size)

def download_from_turbosquid():
    """Download free 3D models from TurboSquid."""
    print("Downloading free 3D models from TurboSquid...")
    
    driver = setup_driver()
    
    try:
        # Navigate to TurboSquid free 3D models page
        driver.get("https://www.turbosquid.com/Search/3D-Models/free")
        
        # Wait for the page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".ProductCardstyles__ProductCardContainer-sc-5v5yx6-1"))
        )
        
        # Get the first few free models
        model_links = driver.find_elements(By.CSS_SELECTOR, ".ProductCardstyles__ProductCardContainer-sc-5v5yx6-1 a")
        model_urls = [link.get_attribute("href") for link in model_links[:3]]
        
        for i, url in enumerate(model_urls):
            print(f"Processing model {i+1}/{len(model_urls)}: {url}")
            
            # Navigate to the model page
            driver.get(url)
            
            # Wait for the page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".product-title"))
            )
            
            # Get the model name
            model_name = driver.find_element(By.CSS_SELECTOR, ".product-title").text
            model_name = model_name.replace(" ", "_").lower()
            
            # Check if there's a download button
            try:
                download_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "a.download-button"))
                )
                
                # Click the download button
                download_button.click()
                
                # Wait for the download to start
                time.sleep(5)
                
                print(f"Downloaded model: {model_name}")
            except:
                print(f"Could not download model: {model_name}")
    
    except Exception as e:
        print(f"Error downloading from TurboSquid: {e}")
    
    finally:
        driver.quit()

def download_from_free3d():
    """Download free 3D models from Free3D."""
    print("Downloading free 3D models from Free3D...")
    
    driver = setup_driver()
    
    try:
        # Navigate to Free3D free 3D models page
        driver.get("https://free3d.com/3d-models/")
        
        # Wait for the page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".model-card"))
        )
        
        # Get the first few free models
        model_links = driver.find_elements(By.CSS_SELECTOR, ".model-card a")
        model_urls = [link.get_attribute("href") for link in model_links[:3]]
        
        for i, url in enumerate(model_urls):
            if not url or not url.startswith("http"):
                continue
                
            print(f"Processing model {i+1}/{len(model_urls)}: {url}")
            
            # Navigate to the model page
            driver.get(url)
            
            # Wait for the page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".model-title"))
            )
            
            # Get the model name
            model_name = driver.find_element(By.CSS_SELECTOR, ".model-title").text
            model_name = model_name.replace(" ", "_").lower()
            
            # Check if there's a download button
            try:
                download_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "a.download-button"))
                )
                
                # Click the download button
                download_button.click()
                
                # Wait for the download to start
                time.sleep(5)
                
                print(f"Downloaded model: {model_name}")
            except:
                print(f"Could not download model: {model_name}")
    
    except Exception as e:
        print(f"Error downloading from Free3D: {e}")
    
    finally:
        driver.quit()

def download_from_kenney():
    """Download free game assets from Kenney.nl."""
    print("Downloading free game assets from Kenney.nl...")
    
    # Kenney.nl has direct download links for assets
    assets = [
        {
            "name": "platformer-kit",
            "url": "https://kenney.nl/content/1-assets/1-platformer-kit/platformerkit_1.1.zip",
            "type": "models"
        },
        {
            "name": "nature-kit",
            "url": "https://kenney.nl/content/1-assets/1-nature-kit/naturekit_1.1.zip",
            "type": "models"
        },
        {
            "name": "ui-pack",
            "url": "https://kenney.nl/content/1-assets/1-ui-pack/uipack_fixed.zip",
            "type": "textures"
        }
    ]
    
    for asset in assets:
        filename = os.path.join("assets", asset["type"], f"{asset['name']}.zip")
        print(f"Downloading {asset['name']} to {filename}...")
        
        try:
            download_file(asset["url"], filename)
            print(f"Downloaded {asset['name']}")
        except Exception as e:
            print(f"Error downloading {asset['name']}: {e}")

def main():
    """Main function."""
    print("Starting asset download...")
    
    # Download assets from Kenney.nl (direct downloads)
    download_from_kenney()
    
    # Note: The following functions may not work as expected due to website restrictions
    # Uncomment if you want to try them
    # download_from_turbosquid()
    # download_from_free3d()
    
    print("Asset download complete!")

if __name__ == "__main__":
    main()
