#!/usr/bin/env python3
"""Test all imports systematically"""

import sys
import os
sys.path.append('backend')

def test_basic_imports():
    """Test basic Python imports"""
    try:
        import json
        import time
        import uuid
        from flask import Flask
        from flask_cors import CORS
        print("✅ Basic imports successful")
        return True
    except Exception as e:
        print(f"❌ Basic imports failed: {e}")
        return False

def test_ai_imports():
    """Test AI integration imports"""
    results = {}
    
    # Test ComfyUI client
    try:
        import comfyui_client
        print("✅ ComfyUI client imported")
        results['comfyui_client'] = True
    except Exception as e:
        print(f"❌ ComfyUI client import failed: {e}")
        results['comfyui_client'] = False
    
    # Test Hugging Face client
    try:
        import huggingface_client
        print("✅ Hugging Face client imported")
        results['huggingface_client'] = True
    except Exception as e:
        print(f"❌ Hugging Face client import failed: {e}")
        results['huggingface_client'] = False
    
    # Test workflows
    try:
        import comfyui_workflows
        print("✅ ComfyUI workflows imported")
        results['comfyui_workflows'] = True
    except Exception as e:
        print(f"❌ ComfyUI workflows import failed: {e}")
        results['comfyui_workflows'] = False
    
    # Test config
    try:
        import config
        print("✅ Config imported")
        results['config'] = True
    except Exception as e:
        print(f"❌ Config import failed: {e}")
        results['config'] = False
    
    return results

def test_app_import():
    """Test main app import"""
    try:
        import app
        print("✅ Main app imported successfully")
        return True
    except Exception as e:
        print(f"❌ Main app import failed: {e}")
        return False

def main():
    print("🧪 Testing All Imports")
    print("=" * 30)
    
    # Test basic imports
    basic_ok = test_basic_imports()
    
    # Test AI imports
    print("\n🤖 Testing AI Integration Imports:")
    ai_results = test_ai_imports()
    
    # Test main app
    print("\n📱 Testing Main App Import:")
    app_ok = test_app_import()
    
    # Summary
    print("\n📊 Import Test Summary:")
    print("=" * 25)
    print(f"Basic imports: {'✅' if basic_ok else '❌'}")
    for module, status in ai_results.items():
        print(f"{module}: {'✅' if status else '❌'}")
    print(f"Main app: {'✅' if app_ok else '❌'}")
    
    all_good = basic_ok and all(ai_results.values()) and app_ok
    
    if all_good:
        print("\n🎉 All imports successful!")
    else:
        print("\n⚠️ Some imports failed - checking dependencies...")

if __name__ == "__main__":
    main()
