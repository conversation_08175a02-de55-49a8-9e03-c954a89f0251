#!/usr/bin/env python3
"""
Download essential models for ComfyUI character generation
"""

import os
import requests
from pathlib import Path
import sys

def download_file(url, destination):
    """Download a file with progress bar"""
    print(f"Downloading {url}")
    print(f"To: {destination}")
    
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    
    with open(destination, 'wb') as file:
        downloaded = 0
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                file.write(chunk)
                downloaded += len(chunk)
                if total_size > 0:
                    percent = (downloaded / total_size) * 100
                    print(f"\rProgress: {percent:.1f}%", end='', flush=True)
    
    print(f"\nDownload complete: {destination}")

def main():
    # ComfyUI models directory
    comfyui_path = Path("C:/Users/<USER>/OneDrive/Desktop/ComfyUI_windows_portable_nvidia (1)/ComfyUI_windows_portable/ComfyUI")
    checkpoints_dir = comfyui_path / "models" / "checkpoints"
    
    if not checkpoints_dir.exists():
        print(f"ComfyUI checkpoints directory not found: {checkpoints_dir}")
        return
    
    # Essential models for character generation
    models = {
        "Stable Diffusion 1.5": {
            "url": "https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.ckpt",
            "filename": "v1-5-pruned-emaonly.ckpt",
            "size": "4.27 GB"
        }
    }
    
    print("🎭 ComfyUI Model Downloader for Character Generation")
    print("=" * 60)
    
    for model_name, info in models.items():
        model_path = checkpoints_dir / info["filename"]
        
        if model_path.exists():
            print(f"✅ {model_name} already exists")
            continue
        
        print(f"\n📥 Downloading {model_name} ({info['size']})")
        response = input(f"Download {model_name}? (y/n): ")
        
        if response.lower() == 'y':
            try:
                download_file(info["url"], model_path)
                print(f"✅ {model_name} downloaded successfully!")
            except Exception as e:
                print(f"❌ Error downloading {model_name}: {e}")
        else:
            print(f"⏭️ Skipped {model_name}")
    
    print("\n🎉 Model download process complete!")
    print("\nTo use the models:")
    print("1. Restart ComfyUI if it's running")
    print("2. The models will be available in the CheckpointLoaderSimple node")
    print("3. Your character generation workflows will now work!")

if __name__ == "__main__":
    main()
