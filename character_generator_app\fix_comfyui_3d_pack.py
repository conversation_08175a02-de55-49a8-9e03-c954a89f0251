#!/usr/bin/env python3
"""
Fix ComfyUI-3D-Pack dependencies
"""

import subprocess
import sys
import os

def install_3d_pack_dependencies():
    """Install missing dependencies for ComfyUI-3D-Pack"""
    
    print("🔧 Fixing ComfyUI-3D-Pack Dependencies")
    print("=" * 40)
    
    # Path to ComfyUI's embedded Python
    comfyui_python = r"C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)\ComfyUI_windows_portable\python_embeded\python.exe"
    
    if not os.path.exists(comfyui_python):
        print(f"❌ ComfyUI Python not found at: {comfyui_python}")
        return False
    
    print(f"✅ Found ComfyUI Python: {comfyui_python}")
    
    # Dependencies needed for ComfyUI-3D-Pack
    dependencies = [
        "pyhocon",
        "qrcode",  # For ComfyQR
        "xformers",  # For better performance
    ]
    
    for dep in dependencies:
        print(f"\n📦 Installing {dep}...")
        try:
            result = subprocess.run([
                comfyui_python, "-m", "pip", "install", dep
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ {dep} installed successfully")
            else:
                print(f"⚠️ {dep} installation had issues:")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"⚠️ {dep} installation timed out")
        except Exception as e:
            print(f"❌ Error installing {dep}: {e}")
    
    print("\n🎉 Dependency installation complete!")
    print("Now restart ComfyUI to load the 3D Pack nodes.")
    
    return True

def check_3d_pack_installation():
    """Check if ComfyUI-3D-Pack is properly installed"""
    
    pack_path = r"C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI-3D-Pack"
    
    if os.path.exists(pack_path):
        print(f"✅ ComfyUI-3D-Pack found at: {pack_path}")
        
        # Check for key files
        key_files = [
            "__init__.py",
            "requirements.txt"
        ]
        
        for file in key_files:
            file_path = os.path.join(pack_path, file)
            if os.path.exists(file_path):
                print(f"✅ {file} exists")
            else:
                print(f"❌ {file} missing")
        
        return True
    else:
        print(f"❌ ComfyUI-3D-Pack not found at: {pack_path}")
        return False

def main():
    print("🎮 ComfyUI-3D-Pack Dependency Fixer")
    print("=" * 35)
    
    # Check installation
    if not check_3d_pack_installation():
        print("Please install ComfyUI-3D-Pack first")
        return
    
    # Install dependencies
    install_3d_pack_dependencies()
    
    print("\n📋 Next Steps:")
    print("1. Close any running ComfyUI instances")
    print("2. Restart ComfyUI")
    print("3. Check that 3D Pack nodes are available")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
