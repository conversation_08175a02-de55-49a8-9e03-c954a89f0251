#!/usr/bin/env python3
"""Test server startup step by step"""

import sys
import os
sys.path.append('backend')

def test_basic_flask():
    """Test basic Flask server"""
    try:
        from flask import Flask
        
        app = Flask(__name__)
        
        @app.route('/')
        def hello():
            return "Hello from test server!"
        
        print("✅ Basic Flask app created")
        
        # Try to start on a different port
        print("🌐 Starting test server on port 5001...")
        app.run(host='0.0.0.0', port=5001, debug=False)
        
    except Exception as e:
        print(f"❌ Basic Flask test failed: {e}")
        import traceback
        traceback.print_exc()

def test_port_availability():
    """Test if ports are available"""
    import socket
    
    ports_to_test = [5000, 5001, 8000, 8080]
    
    for port in ports_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"❌ Port {port} is in use")
            else:
                print(f"✅ Port {port} is available")
                
        except Exception as e:
            print(f"⚠️ Error testing port {port}: {e}")

if __name__ == "__main__":
    print("🧪 Server Testing")
    print("=" * 20)
    
    # Test port availability
    print("🔍 Checking port availability:")
    test_port_availability()
    
    print("\n🌐 Testing basic Flask server:")
    test_basic_flask()
