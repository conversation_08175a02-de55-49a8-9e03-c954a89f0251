#!/usr/bin/env python3
"""
Face-to-Character mapping system for exact face matching in 3D characters
"""

import numpy as np
import math
import json
from face_processor import FaceProcessor

class FaceToCharacterMapper:
    """Maps face features to 3D character parameters"""
    
    def __init__(self):
        self.face_processor = FaceProcessor()
    
    def map_face_to_character(self, image_path, base_prompt="A character"):
        """
        Map face features from image to 3D character parameters
        
        Args:
            image_path: Path to the face image
            base_prompt: Base character description
            
        Returns:
            dict: Character parameters with face-matched features
        """
        try:
            # Extract face features
            print("🔍 Analyzing face features...")
            face_features = self.face_processor.extract_face_features(image_path)
            
            # Map features to character parameters
            character_params = self.create_character_parameters(face_features, base_prompt)
            
            # Generate enhanced geometry based on face
            character_params['geometry'] = self.create_face_matched_geometry(face_features)
            
            # Create face-matched materials
            character_params['materials'] = self.create_face_matched_materials(face_features)
            
            # Add face texture mapping
            character_params['face_texture'] = self.create_face_texture_mapping(face_features, image_path)
            
            print("✅ Face-to-character mapping complete")
            return character_params
            
        except Exception as e:
            print(f"❌ Face mapping error: {e}")
            # Return fallback character
            return self.create_fallback_character(base_prompt)
    
    def create_character_parameters(self, face_features, base_prompt):
        """Create character parameters based on face features"""
        
        # Base character type from prompt
        character_type = self.analyze_prompt(base_prompt)
        
        # Override with face-specific features
        character_params = {
            'name': character_type['name'],
            'base_prompt': base_prompt,
            'face_matched': True,
            'proportions': self.map_face_proportions(face_features),
            'colors': self.map_face_colors(face_features),
            'style': self.determine_style_from_face(face_features),
            'age_group': face_features.get('age_estimate', 'adult'),
            'gender_hint': face_features.get('gender_estimate', 'neutral'),
            'face_analysis': face_features
        }
        
        return character_params
    
    def map_face_proportions(self, face_features):
        """Map face proportions to 3D character proportions"""
        
        # Default proportions
        proportions = {
            'head_scale': 1.0,
            'head_width': 1.0,
            'head_height': 1.0,
            'eye_distance': 1.0,
            'jawline_width': 1.0,
            'neck_width': 1.0,
            'facial_symmetry': 1.0
        }
        
        # Map face shape to head proportions
        face_shape = face_features.get('face_shape', {})
        if face_shape:
            aspect_ratio = face_shape.get('aspect_ratio', 0.75)
            shape_type = face_shape.get('shape', 'oval')
            
            # Adjust head proportions based on face shape
            if shape_type == 'round':
                proportions['head_width'] = 1.1
                proportions['head_height'] = 0.95
            elif shape_type == 'square':
                proportions['head_width'] = 1.05
                proportions['jawline_width'] = 1.15
            elif shape_type == 'oval':
                proportions['head_height'] = 1.05
                proportions['head_width'] = 0.95
            
            # Scale based on aspect ratio
            proportions['head_width'] *= aspect_ratio
        
        # Map eye features
        eye_features = face_features.get('eye_features', {})
        if eye_features.get('detected') and eye_features.get('eye_distance'):
            # Normalize eye distance (this is simplified)
            eye_distance_norm = min(2.0, max(0.5, eye_features['eye_distance'] / 50.0))
            proportions['eye_distance'] = eye_distance_norm
        
        # Map facial structure
        facial_structure = face_features.get('facial_structure', {})
        if facial_structure:
            symmetry = facial_structure.get('symmetry_score', 0.8)
            proportions['facial_symmetry'] = symmetry
            
            jawline = facial_structure.get('jawline_strength', {})
            if jawline.get('strength') == 'strong':
                proportions['jawline_width'] = 1.2
            elif jawline.get('strength') == 'soft':
                proportions['jawline_width'] = 0.9
        
        return proportions
    
    def map_face_colors(self, face_features):
        """Map face colors to character materials"""
        
        # Default colors
        colors = {
            'skin_primary': [0.8, 0.6, 0.4],
            'skin_secondary': [0.75, 0.55, 0.35],
            'accent_color': [0.2, 0.4, 0.8],
            'hair_color': [0.3, 0.2, 0.1],
            'eye_color': [0.2, 0.3, 0.4]
        }
        
        # Map skin tone
        skin_tone = face_features.get('skin_tone', {})
        if skin_tone and 'rgb' in skin_tone:
            # Normalize RGB values to 0-1 range
            skin_rgb = [c/255.0 for c in skin_tone['rgb']]
            colors['skin_primary'] = skin_rgb
            
            # Create slightly darker secondary skin color
            colors['skin_secondary'] = [max(0, c - 0.05) for c in skin_rgb]
        
        # Map color palette
        color_palette = face_features.get('color_palette', {})
        if color_palette and 'dominant_colors' in color_palette:
            dominant_colors = color_palette['dominant_colors']
            
            if len(dominant_colors) > 1:
                # Use second dominant color as accent
                accent_rgb = [c/255.0 for c in dominant_colors[1]]
                colors['accent_color'] = accent_rgb
            
            if len(dominant_colors) > 2:
                # Use third color for hair
                hair_rgb = [c/255.0 for c in dominant_colors[2]]
                colors['hair_color'] = hair_rgb
        
        return colors
    
    def determine_style_from_face(self, face_features):
        """Determine character style based on face features"""
        
        # Default to realistic
        style = 'realistic'
        
        # Consider age for style hints
        age_estimate = face_features.get('age_estimate', 'adult')
        if age_estimate == 'young':
            style = 'anime'  # Younger faces might suit anime style
        
        # Consider facial structure
        facial_structure = face_features.get('facial_structure', {})
        symmetry = facial_structure.get('symmetry_score', 0.8)
        
        if symmetry > 0.9:
            style = 'realistic'  # High symmetry suits realistic style
        elif symmetry < 0.7:
            style = 'cartoon'  # Lower symmetry might suit cartoon style
        
        return style
    
    def create_face_matched_geometry(self, face_features):
        """Create 3D geometry that matches the face"""
        
        # Get proportions
        proportions = self.map_face_proportions(face_features)
        
        # Enhanced geometry parameters
        geometry = {
            'head_vertices': self.generate_head_vertices(proportions),
            'facial_landmarks': self.map_facial_landmarks(face_features),
            'detail_level': 'high',  # Use high detail for face matching
            'subdivision_level': 2,  # More subdivisions for smoother face
            'custom_proportions': proportions
        }
        
        return geometry
    
    def generate_head_vertices(self, proportions):
        """Generate head vertices based on face proportions"""
        
        # Base head parameters
        head_center = [0, 1.6, 0]
        base_radius = 0.15
        
        # Apply proportions
        width_scale = proportions.get('head_width', 1.0)
        height_scale = proportions.get('head_height', 1.0)
        
        # Generate ellipsoid head shape
        vertices = []
        rings = 16
        sectors = 20
        
        for r in range(rings + 1):
            lat = math.pi * (-0.5 + r / rings)
            y = math.sin(lat) * height_scale
            xz = math.cos(lat)
            
            for s in range(sectors + 1):
                lon = 2 * math.pi * s / sectors
                x = xz * math.cos(lon) * width_scale
                z = xz * math.sin(lon)
                
                # Apply facial asymmetry if needed
                symmetry = proportions.get('facial_symmetry', 1.0)
                if x > 0:  # Right side of face
                    x *= symmetry
                
                vertices.extend([
                    head_center[0] + base_radius * x,
                    head_center[1] + base_radius * y,
                    head_center[2] + base_radius * z
                ])
        
        return vertices
    
    def map_facial_landmarks(self, face_features):
        """Map facial landmarks for detailed face matching"""
        
        landmarks = {
            'eyes': [],
            'nose': [],
            'mouth': [],
            'jawline': [],
            'eyebrows': []
        }
        
        # Map eye positions
        eye_features = face_features.get('eye_features', {})
        if eye_features.get('detected') and eye_features.get('positions'):
            for eye_pos in eye_features['positions']:
                # Convert 2D eye position to 3D landmark
                x, y, w, h = eye_pos
                landmarks['eyes'].append({
                    'center': [x + w/2, y + h/2],
                    'size': [w, h]
                })
        
        # Add default landmarks if none detected
        if not landmarks['eyes']:
            landmarks['eyes'] = [
                {'center': [-0.05, 1.65], 'size': [0.02, 0.015]},  # Left eye
                {'center': [0.05, 1.65], 'size': [0.02, 0.015]}    # Right eye
            ]
        
        return landmarks
    
    def create_face_matched_materials(self, face_features):
        """Create materials that match the face"""
        
        colors = self.map_face_colors(face_features)
        
        materials = [
            {
                "name": "face_matched_skin",
                "pbrMetallicRoughness": {
                    "baseColorFactor": colors['skin_primary'] + [1.0],
                    "metallicFactor": 0.0,
                    "roughnessFactor": 0.7  # Skin-like roughness
                },
                "emissiveFactor": [0.0, 0.0, 0.0]
            },
            {
                "name": "face_matched_accent",
                "pbrMetallicRoughness": {
                    "baseColorFactor": colors['accent_color'] + [1.0],
                    "metallicFactor": 0.1,
                    "roughnessFactor": 0.5
                },
                "emissiveFactor": [0.0, 0.0, 0.0]
            }
        ]
        
        return materials
    
    def create_face_texture_mapping(self, face_features, image_path):
        """Create texture mapping for face"""
        
        texture_mapping = {
            'source_image': image_path,
            'face_bounds': face_features.get('face_bounds'),
            'uv_mapping': 'face_projection',
            'texture_resolution': 512,
            'apply_to_regions': ['head', 'neck']
        }
        
        return texture_mapping
    
    def analyze_prompt(self, prompt):
        """Analyze base prompt for character type"""
        prompt_lower = prompt.lower()
        
        character_type = {'name': 'humanoid', 'style': 'realistic'}
        
        if any(word in prompt_lower for word in ['robot', 'android', 'cyborg']):
            character_type['name'] = 'robot'
        elif any(word in prompt_lower for word in ['alien', 'extraterrestrial']):
            character_type['name'] = 'alien'
        elif any(word in prompt_lower for word in ['warrior', 'knight', 'soldier']):
            character_type['name'] = 'warrior'
        elif any(word in prompt_lower for word in ['wizard', 'mage', 'sorcerer']):
            character_type['name'] = 'wizard'
        
        return character_type
    
    def create_fallback_character(self, base_prompt):
        """Create fallback character when face analysis fails"""
        return {
            'name': 'humanoid',
            'base_prompt': base_prompt,
            'face_matched': False,
            'proportions': {
                'head_scale': 1.0,
                'head_width': 1.0,
                'head_height': 1.0,
                'eye_distance': 1.0,
                'jawline_width': 1.0
            },
            'colors': {
                'skin_primary': [0.8, 0.6, 0.4],
                'accent_color': [0.2, 0.4, 0.8]
            },
            'style': 'realistic',
            'fallback_used': True
        }
