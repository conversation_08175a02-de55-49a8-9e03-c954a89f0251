"""
ComfyUI Workflow Generator

This script helps create custom ComfyUI workflows programmatically.
It can generate workflows for various tasks like:
- Image to 3D conversion
- Text to 3D generation
- Character creation
- Animation rendering

The generated workflows can be saved as JSON files and loaded into ComfyUI.
"""

import os
import json
import uuid
import copy
from pathlib import Path

class ComfyUIWorkflowGenerator:
    """
    Class for generating ComfyUI workflows programmatically.
    """
    
    def __init__(self, output_dir=None):
        """
        Initialize the workflow generator.
        
        Args:
            output_dir: Directory to save generated workflows (default: 'workflows/generated')
        """
        self.output_dir = output_dir or 'workflows/generated'
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize node counter
        self.node_counter = 1
        
        # Initialize workflow template
        self.workflow = {
            "last_node_id": 1,
            "last_link_id": 1,
            "nodes": [],
            "links": [],
            "groups": [],
            "config": {},
            "extra": {},
            "version": 0.4
        }
    
    def _generate_node_id(self):
        """Generate a unique node ID."""
        node_id = self.node_counter
        self.node_counter += 1
        return node_id
    
    def _generate_link_id(self):
        """Generate a unique link ID."""
        link_id = self.workflow["last_link_id"]
        self.workflow["last_link_id"] += 1
        return link_id
    
    def add_node(self, class_type, pos_x, pos_y, inputs=None, outputs=None, properties=None):
        """
        Add a node to the workflow.
        
        Args:
            class_type: Type of the node (e.g., 'KSampler', 'CLIPTextEncode')
            pos_x: X position of the node in the UI
            pos_y: Y position of the node in the UI
            inputs: Dictionary of input values (default: None)
            outputs: Dictionary of output values (default: None)
            properties: Dictionary of node properties (default: None)
            
        Returns:
            int: ID of the created node
        """
        node_id = self._generate_node_id()
        
        node = {
            "id": node_id,
            "type": class_type,
            "pos": [pos_x, pos_y],
            "size": [400, 200],  # Default size
            "flags": {},
            "order": node_id,
            "mode": 0,
            "inputs": inputs or {},
            "outputs": outputs or {},
            "properties": properties or {},
            "widgets_values": []
        }
        
        self.workflow["nodes"].append(node)
        self.workflow["last_node_id"] = max(self.workflow["last_node_id"], node_id)
        
        return node_id
    
    def add_link(self, from_node, from_output, to_node, to_input):
        """
        Add a link between nodes.
        
        Args:
            from_node: ID of the source node
            from_output: Output slot of the source node
            to_node: ID of the target node
            to_input: Input slot of the target node
            
        Returns:
            int: ID of the created link
        """
        link_id = self._generate_link_id()
        
        link = [
            link_id,
            from_node,
            from_output,
            to_node,
            to_input
        ]
        
        self.workflow["links"].append(link)
        
        return link_id
    
    def create_image_to_3d_workflow(self, use_mickmumpitz=True):
        """
        Create a workflow for converting images to 3D models.
        
        Args:
            use_mickmumpitz: Whether to use mickmumpitz's workflow (default: True)
            
        Returns:
            dict: The created workflow
        """
        # Reset workflow
        self.workflow = {
            "last_node_id": 1,
            "last_link_id": 1,
            "nodes": [],
            "links": [],
            "groups": [],
            "config": {},
            "extra": {},
            "version": 0.4
        }
        self.node_counter = 1
        
        if use_mickmumpitz:
            # Load mickmumpitz's workflow as a base
            mickmumpitz_path = Path('workflows/mickmumpitz/201002_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v4.json')
            if mickmumpitz_path.exists():
                with open(mickmumpitz_path, 'r', encoding='utf-8') as f:
                    self.workflow = json.load(f)
                return self.workflow
        
        # Create a basic image-to-3D workflow
        # 1. Load Image node
        load_image_id = self.add_node(
            "LoadImage",
            100, 100,
            properties={"filename": "image.png"}
        )
        
        # 2. Image to Depth node
        depth_id = self.add_node(
            "ImageToDepth",
            400, 100
        )
        
        # 3. Depth to Normal node
        normal_id = self.add_node(
            "DepthToNormal",
            700, 100
        )
        
        # 4. Normal to 3D node
        model_3d_id = self.add_node(
            "NormalTo3D",
            1000, 100
        )
        
        # 5. Save 3D Model node
        save_model_id = self.add_node(
            "Save3DModel",
            1300, 100,
            properties={"filename": "output.obj"}
        )
        
        # Add links
        self.add_link(load_image_id, 0, depth_id, 0)
        self.add_link(depth_id, 0, normal_id, 0)
        self.add_link(normal_id, 0, model_3d_id, 0)
        self.add_link(model_3d_id, 0, save_model_id, 0)
        
        return self.workflow
    
    def create_text_to_3d_workflow(self, use_mickmumpitz=True):
        """
        Create a workflow for generating 3D models from text.
        
        Args:
            use_mickmumpitz: Whether to use mickmumpitz's workflow (default: True)
            
        Returns:
            dict: The created workflow
        """
        # Reset workflow
        self.workflow = {
            "last_node_id": 1,
            "last_link_id": 1,
            "nodes": [],
            "links": [],
            "groups": [],
            "config": {},
            "extra": {},
            "version": 0.4
        }
        self.node_counter = 1
        
        # Create a basic text-to-3D workflow
        # 1. Text Input node
        text_input_id = self.add_node(
            "CLIPTextEncode",
            100, 100,
            properties={"text": "A beautiful 3D model of a castle"}
        )
        
        # 2. Text to Image node
        text_to_image_id = self.add_node(
            "KSampler",
            400, 100
        )
        
        # 3. Image to Depth node
        depth_id = self.add_node(
            "ImageToDepth",
            700, 100
        )
        
        # 4. Depth to 3D node
        model_3d_id = self.add_node(
            "DepthTo3D",
            1000, 100
        )
        
        # 5. Save 3D Model node
        save_model_id = self.add_node(
            "Save3DModel",
            1300, 100,
            properties={"filename": "output.obj"}
        )
        
        # Add links
        self.add_link(text_input_id, 0, text_to_image_id, 0)
        self.add_link(text_to_image_id, 0, depth_id, 0)
        self.add_link(depth_id, 0, model_3d_id, 0)
        self.add_link(model_3d_id, 0, save_model_id, 0)
        
        return self.workflow
    
    def create_character_generation_workflow(self, use_mickmumpitz=True):
        """
        Create a workflow for generating characters.
        
        Args:
            use_mickmumpitz: Whether to use mickmumpitz's workflow (default: True)
            
        Returns:
            dict: The created workflow
        """
        # Reset workflow
        self.workflow = {
            "last_node_id": 1,
            "last_link_id": 1,
            "nodes": [],
            "links": [],
            "groups": [],
            "config": {},
            "extra": {},
            "version": 0.4
        }
        self.node_counter = 1
        
        if use_mickmumpitz:
            # Load mickmumpitz's workflow as a base
            mickmumpitz_path = Path('workflows/mickmumpitz/241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json')
            if mickmumpitz_path.exists():
                with open(mickmumpitz_path, 'r', encoding='utf-8') as f:
                    self.workflow = json.load(f)
                return self.workflow
        
        # Create a basic character generation workflow
        # (Simplified version as placeholder)
        text_input_id = self.add_node(
            "CLIPTextEncode",
            100, 100,
            properties={"text": "A fantasy character with armor"}
        )
        
        sampler_id = self.add_node(
            "KSampler",
            400, 100
        )
        
        save_image_id = self.add_node(
            "SaveImage",
            700, 100,
            properties={"filename": "character.png"}
        )
        
        # Add links
        self.add_link(text_input_id, 0, sampler_id, 0)
        self.add_link(sampler_id, 0, save_image_id, 0)
        
        return self.workflow
    
    def save_workflow(self, workflow, filename=None):
        """
        Save a workflow to a JSON file.
        
        Args:
            workflow: Workflow to save
            filename: Name of the file (default: None, which generates a random name)
            
        Returns:
            str: Path to the saved workflow file
        """
        if filename is None:
            filename = f"workflow_{uuid.uuid4().hex[:8]}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, indent=2)
        
        print(f"Workflow saved to: {filepath}")
        return filepath

# Example usage
if __name__ == "__main__":
    generator = ComfyUIWorkflowGenerator()
    
    # Create and save an image-to-3D workflow
    image_to_3d_workflow = generator.create_image_to_3d_workflow()
    generator.save_workflow(image_to_3d_workflow, "image_to_3d_workflow.json")
    
    # Create and save a text-to-3D workflow
    text_to_3d_workflow = generator.create_text_to_3d_workflow()
    generator.save_workflow(text_to_3d_workflow, "text_to_3d_workflow.json")
    
    # Create and save a character generation workflow
    character_workflow = generator.create_character_generation_workflow()
    generator.save_workflow(character_workflow, "character_workflow.json")
