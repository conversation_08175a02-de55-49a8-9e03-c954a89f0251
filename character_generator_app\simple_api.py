#!/usr/bin/env python3
"""
Simple API server that definitely works
"""

import http.server
import socketserver
import json
import os
import uuid
from datetime import datetime

class APIHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/api/test':
            self.send_json({'test': 'success', 'backend': 'working'})
        elif self.path == '/':
            self.send_json({'message': 'Character Generator API', 'status': 'running'})
        else:
            self.send_json({'error': 'Not found'}, 404)
    
    def do_POST(self):
        if self.path == '/api/generate':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
                
                prompt = data.get('prompt', 'A character')
                job_id = str(uuid.uuid4())[:8]
                
                print(f"🎯 Generating: {prompt}")
                
                # Try real generation
                try:
                    import sys
                    sys.path.append('backend')
                    from app import create_character_glb, analyze_character_prompt
                    
                    character_type = analyze_character_prompt(prompt)
                    glb_content = create_character_glb(prompt)
                    
                    filename = f"real_char_{job_id}.glb"
                    os.makedirs('backend/outputs/models', exist_ok=True)
                    filepath = os.path.join('backend', 'outputs', 'models', filename)
                    
                    with open(filepath, 'wb') as f:
                        f.write(glb_content)
                    
                    print(f"✅ Real generation: {filename}")
                    
                    response = {
                        'success': True,
                        'job_id': job_id,
                        'filename': filename,
                        'character_type': character_type['name'],
                        'file_size': len(glb_content),
                        'method': 'real'
                    }
                    
                except Exception as e:
                    print(f"⚠️ Using simulation: {e}")
                    
                    filename = f"sim_char_{job_id}.txt"
                    content = f"Character: {prompt}\nGenerated: {datetime.now()}"
                    
                    os.makedirs('backend/outputs/models', exist_ok=True)
                    filepath = os.path.join('backend', 'outputs', 'models', filename)
                    
                    with open(filepath, 'w') as f:
                        f.write(content)
                    
                    response = {
                        'success': True,
                        'job_id': job_id,
                        'filename': filename,
                        'character_type': 'simulated',
                        'file_size': len(content),
                        'method': 'simulation'
                    }
                
                self.send_json(response)
                
            except Exception as e:
                print(f"❌ Error: {e}")
                self.send_json({'success': False, 'error': str(e)}, 500)
        else:
            self.send_json({'error': 'Not found'}, 404)
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def send_json(self, data, status=200):
        self.send_response(status)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def log_message(self, format, *args):
        print(f"🌐 {format % args}")

if __name__ == "__main__":
    PORT = 5000
    print(f"🚀 Starting API server on port {PORT}")
    print(f"📱 Test: http://localhost:{PORT}/api/test")
    
    with socketserver.TCPServer(("", PORT), APIHandler) as httpd:
        print("✅ Server running!")
        httpd.serve_forever()
