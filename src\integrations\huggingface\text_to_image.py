"""
Hugging Face integration for text-to-image conversion.
"""

import os
import requests
import json
import tempfile
import time
from PIL import Image
import io
import base64

class HuggingFaceTextToImage:
    """
    Client for Hugging Face text-to-image models.
    
    This class provides methods to interact with various Hugging Face models
    that can convert text prompts to images.
    """
    
    def __init__(self, api_token=None):
        """
        Initialize the Hugging Face client.
        
        Args:
            api_token: Hugging Face API token (default: from environment variable)
        """
        self.api_token = api_token or os.environ.get('HUGGINGFACE_API_TOKEN')
        self.api_url = "https://api-inference.huggingface.co/models"
        
        # Available models for text-to-image conversion
        self.available_models = {
            'stable-diffusion': 'runwayml/stable-diffusion-v1-5',
            'stable-diffusion-xl': 'stabilityai/stable-diffusion-xl-base-1.0',
            'sdxl-turbo': 'stabilityai/sdxl-turbo',
            'kandinsky': 'kandinsky-community/kandinsky-2-2',
            'midjourney-v4': 'prompthero/midjourney-v4-diffusion',
            'openjourney': 'prompthero/openjourney',
            'dreamshaper': 'Lykon/dreamshaper-8'
        }
    
    def generate_image(self, text_prompt, negative_prompt="", width=512, height=512, steps=30, model='stable-diffusion', output_dir=None):
        """
        Generate an image from a text prompt using a Hugging Face model.
        
        Args:
            text_prompt: Text prompt describing the image
            negative_prompt: Negative prompt (default: "")
            width: Image width (default: 512)
            height: Image height (default: 512)
            steps: Number of sampling steps (default: 30)
            model: Model to use for image generation (default: 'stable-diffusion')
            output_dir: Directory to save the output (default: temporary directory)
            
        Returns:
            str: Path to the generated image file
        """
        if model not in self.available_models:
            raise ValueError(f"Model {model} not available. Choose from: {list(self.available_models.keys())}")
        
        model_id = self.available_models[model]
        
        # Create output directory if not provided
        if output_dir is None:
            output_dir = tempfile.mkdtemp()
        os.makedirs(output_dir, exist_ok=True)
        
        # Prepare headers
        headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
        
        # Prepare payload
        payload = {
            "inputs": text_prompt,
            "parameters": {
                "negative_prompt": negative_prompt,
                "width": width,
                "height": height,
                "num_inference_steps": steps,
                "guidance_scale": 7.5
            }
        }
        
        # Make API request
        print(f"Sending request to Hugging Face model: {model_id}")
        response = requests.post(
            f"{self.api_url}/{model_id}",
            headers=headers,
            json=payload
        )
        
        # Check for errors
        if response.status_code != 200:
            raise Exception(f"Error from Hugging Face API: {response.text}")
        
        # Save the response (which should be an image)
        output_path = os.path.join(output_dir, f"image_{int(time.time())}.png")
        
        # The response format depends on the model
        # For most text-to-image models, it's a direct binary response
        
        if response.headers.get('Content-Type').startswith('image/'):
            # Handle direct image response
            with open(output_path, 'wb') as f:
                f.write(response.content)
        elif response.headers.get('Content-Type') == 'application/json':
            # Handle JSON response
            data = response.json()
            if 'image' in data:
                # Decode base64 image
                image_data = base64.b64decode(data['image'])
                with open(output_path, 'wb') as f:
                    f.write(image_data)
            else:
                # Handle other JSON response formats
                raise NotImplementedError(f"JSON response format not implemented for model {model}")
        else:
            # Handle other response formats
            with open(output_path, 'wb') as f:
                f.write(response.content)
        
        print(f"Image saved to: {output_path}")
        return output_path
    
    def list_available_models(self):
        """
        List available models for text-to-image conversion.
        
        Returns:
            dict: Dictionary of available models
        """
        return self.available_models
