"""
Integrated Workflow for AI Content Generation

This script provides a unified interface for creating and editing content
using ComfyUI, Blender, and Unreal Engine.
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path

# Import our modules
from src.workflow_generators.comfyui_workflow_generator import ComfyUIWorkflowGenerator
from src.integrations.blender.direct_editing import BlenderDirectEditor
from src.integrations.unreal.direct_editing import UnrealDirectEditor
from src.integrations.comfyui.mickmumpitz_workflows import MickmumpitzWorkflows

class IntegratedWorkflow:
    """
    Class for managing integrated workflows across ComfyUI, Blender, and Unreal Engine.
    """

    def __init__(self, comfyui_path=None, blender_path=None, unreal_path=None, unreal_project_path=None):
        """
        Initialize the integrated workflow.

        Args:
            comfyui_path: Path to ComfyUI installation (default: from environment variable)
            blender_path: Path to Blender executable (default: from environment variable)
            unreal_path: Path to Unreal Engine executable (default: from environment variable)
            unreal_project_path: Path to Unreal Engine project (default: from environment variable)
        """
        self.comfyui_path = comfyui_path or os.environ.get('COMFYUI_PATH', 'C:/Users/<USER>/OneDrive/Desktop/ComfyUI_windows_portable_nvidia (1)/ComfyUI_windows_portable/ComfyUI')
        self.blender_path = blender_path or os.environ.get('BLENDER_PATH', 'blender')
        self.unreal_path = unreal_path or os.environ.get('UNREAL_PATH', 'UE4Editor.exe')
        self.unreal_project_path = unreal_project_path or os.environ.get('UNREAL_PROJECT_PATH')

        # Initialize components
        self.comfyui_generator = ComfyUIWorkflowGenerator()
        self.blender_editor = BlenderDirectEditor(blender_path=self.blender_path)
        self.unreal_editor = UnrealDirectEditor(unreal_path=self.unreal_path, unreal_project_path=self.unreal_project_path)
        self.mickmumpitz = MickmumpitzWorkflows(comfyui_path=self.comfyui_path)

    def create_image_to_3d_workflow(self, output_filename=None, use_mickmumpitz=True):
        """
        Create a workflow for converting images to 3D models.

        Args:
            output_filename: Name of the output workflow file (default: None, which generates a name)
            use_mickmumpitz: Whether to use mickmumpitz's workflow (default: True)

        Returns:
            str: Path to the created workflow file
        """
        workflow = self.comfyui_generator.create_image_to_3d_workflow(use_mickmumpitz=use_mickmumpitz)
        return self.comfyui_generator.save_workflow(workflow, filename=output_filename)

    def create_text_to_3d_workflow(self, output_filename=None, use_mickmumpitz=True):
        """
        Create a workflow for generating 3D models from text.

        Args:
            output_filename: Name of the output workflow file (default: None, which generates a name)
            use_mickmumpitz: Whether to use mickmumpitz's workflow (default: True)

        Returns:
            str: Path to the created workflow file
        """
        workflow = self.comfyui_generator.create_text_to_3d_workflow(use_mickmumpitz=use_mickmumpitz)
        return self.comfyui_generator.save_workflow(workflow, filename=output_filename)

    def create_character_workflow(self, output_filename=None, use_mickmumpitz=True):
        """
        Create a workflow for generating characters.

        Args:
            output_filename: Name of the output workflow file (default: None, which generates a name)
            use_mickmumpitz: Whether to use mickmumpitz's workflow (default: True)

        Returns:
            str: Path to the created workflow file
        """
        workflow = self.comfyui_generator.create_character_generation_workflow(use_mickmumpitz=use_mickmumpitz)
        return self.comfyui_generator.save_workflow(workflow, filename=output_filename)

    def edit_3d_model_in_blender(self, model_path=None, edit_mode='sculpt', output_path=None):
        """
        Edit a 3D model in Blender.

        Args:
            model_path: Path to the 3D model to edit (default: None, which creates a new model)
            edit_mode: Editing mode ('sculpt', 'edit', or 'texture') (default: 'sculpt')
            output_path: Path to save the edited model (default: None, which uses the input path)

        Returns:
            subprocess.Popen: The Blender process
        """
        if model_path:
            return self.blender_editor.edit_existing_model(model_path, edit_mode=edit_mode, output_path=output_path)
        else:
            return self.blender_editor.create_new_model(output_path=output_path, edit_mode=edit_mode)

    def import_to_unreal(self, asset_path, destination_path='/Game/ImportedAssets', asset_type='StaticMesh'):
        """
        Import an asset into Unreal Engine.

        Args:
            asset_path: Path to the asset file
            destination_path: Path in the Unreal Engine project to import the asset to (default: '/Game/ImportedAssets')
            asset_type: Type of the asset ('StaticMesh', 'SkeletalMesh', 'Animation', 'Texture') (default: 'StaticMesh')

        Returns:
            bool: True if the import was successful, False otherwise
        """
        return self.unreal_editor.import_asset(asset_path, destination_path=destination_path, asset_type=asset_type)

    def create_unreal_level(self, level_name, template='Default', save_path='/Game/Maps'):
        """
        Create a new level in Unreal Engine.

        Args:
            level_name: Name of the new level
            template: Template to use ('Default', 'Empty', or a path to an existing level) (default: 'Default')
            save_path: Path in the Unreal Engine project to save the level to (default: '/Game/Maps')

        Returns:
            bool: True if the level creation was successful, False otherwise
        """
        return self.unreal_editor.create_level(level_name, template=template, save_path=save_path)

    def open_unreal_editor(self, map_path=None):
        """
        Open the Unreal Engine editor.

        Args:
            map_path: Path to the map to open (default: None, which opens the default map)

        Returns:
            subprocess.Popen: The Unreal Engine process
        """
        return self.unreal_editor.open_unreal_editor(map_path=map_path)

    def setup_mickmumpitz_nodes(self):
        """
        Set up the required ComfyUI nodes for mickmumpitz's workflows.

        Returns:
            bool: True if the setup was successful, False otherwise
        """
        try:
            self.mickmumpitz.setup_required_nodes()
            return True
        except Exception as e:
            print(f"Error setting up mickmumpitz nodes: {e}")
            return False

    def create_complete_pipeline(self, input_type, output_type, input_path=None, output_path=None):
        """
        Create a complete pipeline from input to output.

        Args:
            input_type: Type of input ('image', 'text', '3d', 'video')
            output_type: Type of output ('image', '3d', 'video', 'game')
            input_path: Path to the input file (default: None)
            output_path: Path to save the output (default: None, which generates a path)

        Returns:
            dict: Information about the created pipeline
        """
        result = {
            'input_type': input_type,
            'output_type': output_type,
            'input_path': input_path,
            'output_path': output_path,
            'workflow_path': None,
            'success': False
        }

        try:
            # Step 1: Create the appropriate workflow
            if input_type == 'image' and output_type == '3d':
                workflow_path = self.create_image_to_3d_workflow()
                result['workflow_path'] = workflow_path
            elif input_type == 'text' and output_type == '3d':
                workflow_path = self.create_text_to_3d_workflow()
                result['workflow_path'] = workflow_path
            elif input_type == 'text' and output_type == 'image':
                workflow_path = self.create_character_workflow()
                result['workflow_path'] = workflow_path
            else:
                raise ValueError(f"Unsupported pipeline: {input_type} to {output_type}")

            # Step 2: If the output is a 3D model, offer to edit it in Blender
            if output_type == '3d' and output_path:
                print(f"Workflow created: {workflow_path}")
                print(f"After generating the 3D model, you can edit it in Blender using:")
                print(f"  integrated_workflow.py edit-in-blender --model-path {output_path}")

            # Step 3: If the output is a game, offer to import it into Unreal Engine
            if output_type == 'game' and output_path:
                print(f"Workflow created: {workflow_path}")
                print(f"After generating the assets, you can import them into Unreal Engine using:")
                print(f"  integrated_workflow.py import-to-unreal --asset-path {output_path}")

            result['success'] = True

        except Exception as e:
            print(f"Error creating pipeline: {e}")
            result['error'] = str(e)

        return result

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Integrated Workflow for AI Content Generation')
    subparsers = parser.add_subparsers(dest='command', help='Command to execute')

    # Create workflow command
    create_parser = subparsers.add_parser('create-workflow', help='Create a ComfyUI workflow')
    create_parser.add_argument('--type', choices=['image-to-3d', 'text-to-3d', 'character'], required=True,
                              help='Type of workflow to create')
    create_parser.add_argument('--output', type=str, help='Output filename')
    create_parser.add_argument('--use-mickmumpitz', action='store_true', help='Use mickmumpitz\'s workflow')

    # Edit in Blender command
    blender_parser = subparsers.add_parser('edit-in-blender', help='Edit a 3D model in Blender')
    blender_parser.add_argument('--model-path', type=str, help='Path to the 3D model to edit')
    blender_parser.add_argument('--edit-mode', choices=['sculpt', 'edit', 'texture'], default='sculpt',
                               help='Editing mode')
    blender_parser.add_argument('--output-path', type=str, help='Path to save the edited model')

    # Import to Unreal command
    unreal_parser = subparsers.add_parser('import-to-unreal', help='Import an asset into Unreal Engine')
    unreal_parser.add_argument('--asset-path', type=str, required=True, help='Path to the asset file')
    unreal_parser.add_argument('--destination-path', type=str, default='/Game/ImportedAssets',
                              help='Path in the Unreal Engine project to import the asset to')
    unreal_parser.add_argument('--asset-type', choices=['StaticMesh', 'SkeletalMesh', 'Animation', 'Texture'],
                              default='StaticMesh', help='Type of the asset')

    # Create Unreal level command
    level_parser = subparsers.add_parser('create-unreal-level', help='Create a new level in Unreal Engine')
    level_parser.add_argument('--level-name', type=str, required=True, help='Name of the new level')
    level_parser.add_argument('--template', type=str, default='Default',
                             help='Template to use (Default, Empty, or a path to an existing level)')
    level_parser.add_argument('--save-path', type=str, default='/Game/Maps',
                             help='Path in the Unreal Engine project to save the level to')

    # Open Unreal editor command
    open_unreal_parser = subparsers.add_parser('open-unreal', help='Open the Unreal Engine editor')
    open_unreal_parser.add_argument('--map-path', type=str, help='Path to the map to open')

    # Setup mickmumpitz nodes command
    setup_parser = subparsers.add_parser('setup-mickmumpitz', help='Set up the required ComfyUI nodes for mickmumpitz\'s workflows')

    # Create complete pipeline command
    pipeline_parser = subparsers.add_parser('create-pipeline', help='Create a complete pipeline from input to output')
    pipeline_parser.add_argument('--input-type', choices=['image', 'text', '3d', 'video'], required=True,
                                help='Type of input')
    pipeline_parser.add_argument('--output-type', choices=['image', '3d', 'video', 'game'], required=True,
                                help='Type of output')
    pipeline_parser.add_argument('--input-path', type=str, help='Path to the input file')
    pipeline_parser.add_argument('--output-path', type=str, help='Path to save the output')

    args = parser.parse_args()

    # Initialize the integrated workflow
    workflow = IntegratedWorkflow()

    # Execute the command
    if args.command == 'create-workflow':
        if args.type == 'image-to-3d':
            workflow_path = workflow.create_image_to_3d_workflow(output_filename=args.output, use_mickmumpitz=args.use_mickmumpitz)
            print(f"Image-to-3D workflow created: {workflow_path}")
        elif args.type == 'text-to-3d':
            workflow_path = workflow.create_text_to_3d_workflow(output_filename=args.output, use_mickmumpitz=args.use_mickmumpitz)
            print(f"Text-to-3D workflow created: {workflow_path}")
        elif args.type == 'character':
            workflow_path = workflow.create_character_workflow(output_filename=args.output, use_mickmumpitz=args.use_mickmumpitz)
            print(f"Character workflow created: {workflow_path}")

    elif args.command == 'edit-in-blender':
        process = workflow.edit_3d_model_in_blender(model_path=args.model_path, edit_mode=args.edit_mode, output_path=args.output_path)
        print(f"Blender opened for editing.")

    elif args.command == 'import-to-unreal':
        success = workflow.import_to_unreal(args.asset_path, destination_path=args.destination_path, asset_type=args.asset_type)
        if success:
            print(f"Asset imported successfully: {args.asset_path}")
        else:
            print(f"Failed to import asset: {args.asset_path}")

    elif args.command == 'create-unreal-level':
        success = workflow.create_unreal_level(args.level_name, template=args.template, save_path=args.save_path)
        if success:
            print(f"Level created successfully: {args.level_name}")
        else:
            print(f"Failed to create level: {args.level_name}")

    elif args.command == 'open-unreal':
        process = workflow.open_unreal_editor(map_path=args.map_path)
        print(f"Unreal Engine opened.")

    elif args.command == 'setup-mickmumpitz':
        success = workflow.setup_mickmumpitz_nodes()
        if success:
            print(f"mickmumpitz nodes set up successfully.")
        else:
            print(f"Failed to set up mickmumpitz nodes.")

    elif args.command == 'create-pipeline':
        result = workflow.create_complete_pipeline(args.input_type, args.output_type, input_path=args.input_path, output_path=args.output_path)
        if result['success']:
            print(f"Pipeline created successfully.")
            print(f"Workflow: {result['workflow_path']}")
        else:
            print(f"Failed to create pipeline: {result.get('error', 'Unknown error')}")

    else:
        parser.print_help()

if __name__ == "__main__":
    main()
