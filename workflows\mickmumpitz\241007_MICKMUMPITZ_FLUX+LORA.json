{"last_node_id": 88, "last_link_id": 230, "nodes": [{"id": 8, "type": "VAEDecode", "pos": {"0": 820, "1": 370}, "size": {"0": 210, "1": 46}, "flags": {"collapsed": true}, "order": 21, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 24}, {"name": "vae", "type": "VAE", "link": 12}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 16, "type": "KSamplerSelect", "pos": {"0": 360, "1": 900}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [19, 169, 184], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["deis"], "color": "#323", "bgcolor": "#535"}, {"id": 12, "type": "UNETLoader", "pos": {"0": -820, "1": 70}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [209], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev-fp8.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 30, "type": "ModelSamplingFlux", "pos": {"0": 360, "1": 1140}, "size": {"0": 315, "1": 130}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 193, "slot_index": 0}, {"name": "width", "type": "INT", "link": 115, "slot_index": 1, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 114, "slot_index": 2, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [54, 55, 217], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, 1280, 720], "color": "#323", "bgcolor": "#535"}, {"id": 65, "type": "VAEDecode", "pos": {"0": 1180, "1": 610}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 188}, {"name": "vae", "type": "VAE", "link": 165}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [166], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 66, "type": "SamplerCustomAdvanced", "pos": {"0": 2120, "1": 610}, "size": {"0": 270, "1": 330}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 181, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 168, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 169, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 192, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 177, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": [179], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}}, {"id": 86, "type": "Upscale Model Loader", "pos": {"0": 2700, "1": 50}, "size": {"0": 315, "1": 78}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [221], "shape": 3}, {"name": "MODEL_NAME_TEXT", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "Upscale Model Loader"}, "widgets_values": ["4x-ClearRealityV1.pth"]}, {"id": 80, "type": "SaveImage", "pos": {"0": 4146, "1": -775}, "size": {"0": 1112.4222412109375, "1": 614.6506958007812}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 206}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 10, "type": "VAELoader", "pos": {"0": -10, "1": 450}, "size": {"0": 311.81634521484375, "1": 60.429901123046875}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [12, 165, 178, 220], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 27, "type": "EmptySD3LatentImage", "pos": {"0": 360, "1": 610}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 112, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 113, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [116, 186], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1280, 720, 1], "color": "#323", "bgcolor": "#535"}, {"id": 34, "type": "PrimitiveNode", "pos": {"0": -330, "1": 607}, "size": {"0": 210, "1": 82}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [112, 115], "slot_index": 0, "widget": {"name": "width"}}], "title": "width", "properties": {"Run widget replace on values": false}, "widgets_values": [1280, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 35, "type": "PrimitiveNode", "pos": {"0": -326, "1": 740}, "size": {"0": 210, "1": 82}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [113, 114], "slot_index": 0, "widget": {"name": "height"}}], "title": "height", "properties": {"Run widget replace on values": false}, "widgets_values": [720, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 67, "type": "SplitSigmasDenoise", "pos": {"0": 1780, "1": 810}, "size": {"0": 315, "1": 78}, "flags": {"collapsed": false}, "order": 17, "mode": 0, "inputs": [{"name": "sigmas", "type": "SIGMAS", "link": 172}], "outputs": [{"name": "high_sigmas", "type": "SIGMAS", "links": [191], "slot_index": 0, "shape": 3}, {"name": "low_sigmas", "type": "SIGMAS", "links": [192], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "SplitSigmasDenoise"}, "widgets_values": [0.45]}, {"id": 73, "type": "SamplerCustomAdvanced", "pos": {"0": 850, "1": 610}, "size": {"0": 270, "1": 330}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 182, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 183, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 184, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 187, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 186, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": [188], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}}, {"id": 13, "type": "SamplerCustomAdvanced", "pos": {"0": 1490, "1": 610}, "size": {"0": 272.3617858886719, "1": 326}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 37, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 30, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 19, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 191, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 116, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [24, 176], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": [], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}}, {"id": 69, "type": "VAEDecode", "pos": {"0": 2408, "1": 610}, "size": {"0": 210, "1": 50}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 179}, {"name": "vae", "type": "VAE", "link": 178}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [216, 230], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 71, "type": "<PERSON><PERSON><PERSON><PERSON>", "pos": {"0": 1879, "1": 941}, "size": {"0": 210, "1": 26}, "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [181], "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>"}}, {"id": 22, "type": "BasicGuider", "pos": {"0": 895, "1": 513}, "size": {"0": 222.3482666015625, "1": 46}, "flags": {"collapsed": false}, "order": 18, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 54, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 42, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [30, 168, 183], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}}, {"id": 42, "type": "CLIPTextEncode", "pos": {"0": 358, "1": 360}, "size": {"0": 285.6000061035156, "1": 81}, "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 197}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [226], "slot_index": 0}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["", true], "color": "#322", "bgcolor": "#533"}, {"id": 81, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -382, "1": 243}, "size": {"0": 315, "1": 126}, "flags": {}, "order": 10, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 209}, {"name": "clip", "type": "CLIP", "link": 210}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [207], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [208], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["flux\\FluxGym\\gym-snibbelwibcaption-000012.safetensors", 0.92, 0.92]}, {"id": 25, "type": "RandomNoise", "pos": {"0": 360, "1": 763}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [37, 182], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [891324517745075, "randomize"], "color": "#323", "bgcolor": "#535"}, {"id": 85, "type": "UltimateSDUpscale", "pos": {"0": 2700, "1": 190}, "size": {"0": 315, "1": 826}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 216}, {"name": "model", "type": "MODEL", "link": 217}, {"name": "positive", "type": "CONDITIONING", "link": 225}, {"name": "negative", "type": "CONDITIONING", "link": 226}, {"name": "vae", "type": "VAE", "link": 220}, {"name": "upscale_model", "type": "UPSCALE_MODEL", "link": 221}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [222, 227], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UltimateSDUpscale"}, "widgets_values": [2, 519221671988204, "fixed", 25, 1, "deis", "beta", 0.25, "Linear", 1024, 1024, 8, 32, "None", 1, 64, 8, 16, false, false]}, {"id": 75, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -28, "1": 242}, "size": {"0": 315, "1": 126}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 207}, {"name": "clip", "type": "CLIP", "link": 208}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [193], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [197, 214], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["flux\\FluxGym\\tinaai-000012.safetensors", 0.9, 0.9]}, {"id": 11, "type": "DualCLIPLoader", "pos": {"0": -840, "1": 250}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [210], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp8_e4m3fn.safetensors", "clip_l.safetensors", "flux"]}, {"id": 79, "type": "BetterFilmGrain", "pos": {"0": 4217, "1": 191}, "size": {"0": 315, "1": 178}, "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 222}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [206], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BetterFilmGrain"}, "widgets_values": [0.3, 0.34, 0.6, 0, 233641896702998, "randomize"]}, {"id": 68, "type": "InjectLatentNoise+", "pos": {"0": 1790, "1": 610}, "size": {"0": 315, "1": 150}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "latent", "type": "LATENT", "link": 176}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [177], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "InjectLatentNoise+"}, "widgets_values": [0, "fixed", 2.25, "false"]}, {"id": 70, "type": "SaveImage", "pos": {"0": 2988, "1": -759}, "size": {"0": 1112.4222412109375, "1": 614.6506958007812}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 227}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 17, "type": "BasicScheduler", "pos": {"0": 360, "1": 1000}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 55, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [172, 187], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", 35, 1], "color": "#323", "bgcolor": "#535"}, {"id": 88, "type": "SaveImage", "pos": {"0": 1807, "1": -773}, "size": {"0": 1112.4222412109375, "1": 614.6506958007812}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 230}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 64, "type": "SaveImage", "pos": {"0": 657, "1": -777}, "size": {"0": 1112.4222412109375, "1": 614.6506958007812}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 166}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 26, "type": "FluxGuidance", "pos": {"0": 806, "1": 417}, "size": {"0": 317.4000244140625, "1": 58}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 229}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [42], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3], "color": "#233", "bgcolor": "#355"}, {"id": 6, "type": "CLIPTextEncode", "pos": {"0": 347, "1": 155}, "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 214}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [225, 229], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["IMG_1016.CR2, candid photography of one woman called <PERSON><PERSON><PERSON> in a museum,  she is walking through the modern white museum looking at classic french art, skylight, amateur photography, shot on iphone, dynamic shot, motion blur, light smile, she marvels at the art, close up of her face", true], "color": "#232", "bgcolor": "#353"}], "links": [[12, 10, 0, 8, 1, "VAE"], [19, 16, 0, 13, 2, "SAMPLER"], [24, 13, 0, 8, 0, "LATENT"], [30, 22, 0, 13, 1, "GUIDER"], [37, 25, 0, 13, 0, "NOISE"], [42, 26, 0, 22, 1, "CONDITIONING"], [54, 30, 0, 22, 0, "MODEL"], [55, 30, 0, 17, 0, "MODEL"], [112, 34, 0, 27, 0, "INT"], [113, 35, 0, 27, 1, "INT"], [114, 35, 0, 30, 2, "INT"], [115, 34, 0, 30, 1, "INT"], [116, 27, 0, 13, 4, "LATENT"], [165, 10, 0, 65, 1, "VAE"], [166, 65, 0, 64, 0, "IMAGE"], [168, 22, 0, 66, 1, "GUIDER"], [169, 16, 0, 66, 2, "SAMPLER"], [172, 17, 0, 67, 0, "SIGMAS"], [176, 13, 0, 68, 0, "LATENT"], [177, 68, 0, 66, 4, "LATENT"], [178, 10, 0, 69, 1, "VAE"], [179, 66, 1, 69, 0, "LATENT"], [181, 71, 0, 66, 0, "NOISE"], [182, 25, 0, 73, 0, "NOISE"], [183, 22, 0, 73, 1, "GUIDER"], [184, 16, 0, 73, 2, "SAMPLER"], [186, 27, 0, 73, 4, "LATENT"], [187, 17, 0, 73, 3, "SIGMAS"], [188, 73, 1, 65, 0, "LATENT"], [191, 67, 0, 13, 3, "SIGMAS"], [192, 67, 1, 66, 3, "SIGMAS"], [193, 75, 0, 30, 0, "MODEL"], [197, 75, 1, 42, 0, "CLIP"], [206, 79, 0, 80, 0, "IMAGE"], [207, 81, 0, 75, 0, "MODEL"], [208, 81, 1, 75, 1, "CLIP"], [209, 12, 0, 81, 0, "MODEL"], [210, 11, 0, 81, 1, "CLIP"], [214, 75, 1, 6, 0, "CLIP"], [216, 69, 0, 85, 0, "IMAGE"], [217, 30, 0, 85, 1, "MODEL"], [220, 10, 0, 85, 4, "VAE"], [221, 86, 0, 85, 5, "UPSCALE_MODEL"], [222, 85, 0, 79, 0, "IMAGE"], [225, 6, 0, 85, 2, "CONDITIONING"], [226, 42, 0, 85, 3, "CONDITIONING"], [227, 85, 0, 70, 0, "IMAGE"], [229, 6, 0, 26, 0, "CONDITIONING"], [230, 69, 0, 88, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.2593742460100005, "offset": [2152.9982340975816, 1670.5233037002006]}, "groupNodes": {}}, "version": 0.4}