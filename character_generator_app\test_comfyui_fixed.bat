@echo off
echo 🚀 Testing ComfyUI with Fixed CUDA Support
echo ==========================================

echo.
echo 📍 Navigating to ComfyUI directory...
cd "C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)\ComfyUI_windows_portable"

echo.
echo 🎮 Starting ComfyUI with CUDA support...
echo 💡 Should now detect RTX 4060 GPU properly...
echo.

.\python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build

echo.
echo 🛑 ComfyUI has stopped
pause
