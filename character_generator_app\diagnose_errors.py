"""
Diagnostic script to identify and fix errors in the 3D Character Generator
"""

import requests
import json
import traceback

API_BASE = "http://localhost:5000"

def test_endpoint(method, endpoint, data=None, files=None):
    """Test a specific API endpoint and return detailed results."""
    try:
        url = f"{API_BASE}{endpoint}"
        print(f"\n🔍 Testing {method} {url}")
        
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            if files:
                response = requests.post(url, files=files)
            else:
                response = requests.post(url, json=data)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ Success: {json.dumps(result, indent=2)[:200]}...")
                return True, result
            except:
                print(f"✅ Success: {response.text[:200]}...")
                return True, response.text
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False, response.text
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        traceback.print_exc()
        return False, str(e)

def diagnose_all():
    """Run comprehensive diagnostics."""
    print("🔧 3D Character Generator - Error Diagnosis")
    print("=" * 50)
    
    # Test 1: Basic API connection
    print("\n📡 Testing API Connection...")
    success, result = test_endpoint("GET", "/")
    if not success:
        print("❌ CRITICAL: API is not responding!")
        return
    
    # Test 2: Jobs list
    print("\n📋 Testing Jobs List...")
    success, jobs_result = test_endpoint("GET", "/api/jobs")
    if success:
        try:
            jobs = jobs_result.get('jobs', [])
            print(f"Found {len(jobs)} jobs")
            if jobs:
                print("Sample job structure:")
                sample_job = jobs[0]
                for key, value in sample_job.items():
                    print(f"  {key}: {type(value).__name__} = {str(value)[:50]}")
        except Exception as e:
            print(f"Error analyzing jobs: {e}")
    
    # Test 3: File upload
    print("\n📤 Testing File Upload...")
    try:
        # Create a simple test file
        test_content = b"fake image content for testing"
        files = {'file': ('test.png', test_content, 'image/png')}
        success, upload_result = test_endpoint("POST", "/api/upload", files=files)
        
        if success:
            uploaded_path = upload_result.get('processed_path')
            print(f"Upload successful: {uploaded_path}")
        else:
            print("Upload failed")
            uploaded_path = None
    except Exception as e:
        print(f"Upload test failed: {e}")
        uploaded_path = None
    
    # Test 4: Character generation
    print("\n🚀 Testing Character Generation...")
    gen_data = {
        "text_prompt": "A simple test character",
        "image_path": uploaded_path or "",
        "style_options": {
            "realistic": False,
            "cartoon": True,
            "anime": False,
            "lowPoly": False
        }
    }
    
    success, gen_result = test_endpoint("POST", "/api/generate", data=gen_data)
    if success:
        job_id = gen_result.get('job_id')
        print(f"Generation started: {job_id}")
        
        # Test 5: Job status
        if job_id:
            print(f"\n📊 Testing Job Status for {job_id[:8]}...")
            success, status_result = test_endpoint("GET", f"/api/status/{job_id}")
            if success:
                print(f"Job status: {status_result.get('status')}")
                print(f"Job progress: {status_result.get('progress', 0)}%")
                
                # Check job structure
                print("Job data structure:")
                for key, value in status_result.items():
                    print(f"  {key}: {type(value).__name__} = {str(value)[:50]}")
    
    # Test 6: Check for common frontend issues
    print("\n🌐 Frontend Compatibility Check...")
    
    # Check CORS headers
    try:
        response = requests.options(f"{API_BASE}/api/upload")
        cors_headers = {k: v for k, v in response.headers.items() if 'cors' in k.lower() or 'access-control' in k.lower()}
        if cors_headers:
            print("✅ CORS headers present:")
            for header, value in cors_headers.items():
                print(f"  {header}: {value}")
        else:
            print("⚠️  No CORS headers found")
    except Exception as e:
        print(f"CORS check failed: {e}")
    
    print("\n🎯 Diagnosis Complete!")
    print("\nIf you're still seeing errors, please share:")
    print("1. The exact error message")
    print("2. Where you see the error (browser console, webpage, etc.)")
    print("3. What action triggers the error")

if __name__ == "__main__":
    diagnose_all()
