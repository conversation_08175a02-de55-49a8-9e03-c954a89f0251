#!/usr/bin/env python3
"""
Main character generator module that orchestrates all components
"""

import os
import uuid
from datetime import datetime

# Import our modular components
from face_analysis import FaceAnalyzer
from mesh_generator import MeshGenerator
from material_system import MaterialSystem
from glb_exporter import GLBExporter

class CharacterGenerator:
    """Main character generator that coordinates all subsystems"""
    
    def __init__(self):
        self.face_analyzer = FaceAnalyzer()
        self.mesh_generator = MeshGenerator()
        self.material_system = MaterialSystem()
        self.glb_exporter = GLBExporter()
        
        # Ensure output directories exist
        self.ensure_output_directories()
    
    def ensure_output_directories(self):
        """Ensure all output directories exist"""
        directories = [
            'backend/outputs',
            'backend/outputs/models',
            'backend/outputs/analysis',
            'backend/outputs/textures'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def generate_character_from_image(self, image_path, character_description="A realistic character", 
                                    quality='high', style='realistic'):
        """Generate a complete 3D character from a face image"""
        try:
            print("🚀 Starting character generation from image...")
            print(f"   Image: {image_path}")
            print(f"   Description: {character_description}")
            print(f"   Quality: {quality}")
            print(f"   Style: {style}")
            
            # Step 1: Analyze face
            print("\n🔍 Step 1: Analyzing face...")
            face_analysis = self.face_analyzer.analyze_face(image_path)
            
            if not face_analysis.get('face_detected', False):
                print("⚠️ No face detected, using full image analysis")
            
            # Step 2: Create character parameters
            print("\n🎨 Step 2: Creating character parameters...")
            character_params = self.create_character_parameters(
                face_analysis, character_description, style
            )
            
            # Step 3: Generate 3D mesh
            print("\n🎮 Step 3: Generating 3D mesh...")
            vertices, indices, normals = self.mesh_generator.generate_character_mesh(
                face_analysis, character_params, quality
            )
            
            # Step 4: Create materials
            print("\n🎨 Step 4: Creating materials...")
            materials = self.material_system.create_character_materials(
                face_analysis, character_params, style
            )
            
            # Step 5: Export to GLB
            print("\n📦 Step 5: Exporting to GLB...")
            metadata = self.create_metadata(face_analysis, character_params, quality, style)
            glb_content = self.glb_exporter.export_character_glb(
                vertices, indices, normals, materials, metadata
            )
            
            # Step 6: Save files
            print("\n💾 Step 6: Saving files...")
            result = self.save_character_files(
                glb_content, face_analysis, character_params, image_path
            )
            
            print(f"\n✅ Character generation complete!")
            print(f"   GLB file: {result['glb_file']}")
            print(f"   File size: {result['file_size']:,} bytes")
            print(f"   Vertices: {result['vertex_count']:,}")
            print(f"   Faces: {result['face_count']:,}")
            
            return result
            
        except Exception as e:
            print(f"❌ Character generation failed: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def generate_character_from_text(self, character_description, quality='medium', style='realistic'):
        """Generate a character from text description only"""
        try:
            print("🚀 Starting character generation from text...")
            print(f"   Description: {character_description}")
            print(f"   Quality: {quality}")
            print(f"   Style: {style}")
            
            # Create default face analysis
            face_analysis = self.create_default_face_analysis()
            
            # Create character parameters
            character_params = self.create_character_parameters(
                face_analysis, character_description, style
            )
            
            # Generate mesh
            vertices, indices, normals = self.mesh_generator.generate_character_mesh(
                face_analysis, character_params, quality
            )
            
            # Create materials
            materials = self.material_system.create_character_materials(
                face_analysis, character_params, style
            )
            
            # Export to GLB
            metadata = self.create_metadata(face_analysis, character_params, quality, style)
            glb_content = self.glb_exporter.export_character_glb(
                vertices, indices, normals, materials, metadata
            )
            
            # Save files
            result = self.save_character_files(
                glb_content, face_analysis, character_params, None
            )
            
            print(f"\n✅ Text-based character generation complete!")
            print(f"   GLB file: {result['glb_file']}")
            
            return result
            
        except Exception as e:
            print(f"❌ Text-based character generation failed: {e}")
            return None
    
    def create_character_parameters(self, face_analysis, character_description, style):
        """Create character parameters from face analysis and description"""
        try:
            # Analyze character type from description
            character_type = self.analyze_character_description(character_description)
            
            # Extract colors from face analysis
            skin_tone = face_analysis.get('skin_tone', {})
            color_palette = face_analysis.get('color_palette', {})
            
            # Create character parameters
            character_params = {
                'name': character_type['name'],
                'description': character_description,
                'style': style,
                'face_matched': face_analysis.get('face_detected', False),
                
                # Proportions based on face analysis
                'proportions': self.extract_proportions(face_analysis),
                
                # Colors based on face analysis
                'colors': self.extract_colors(face_analysis, character_type),
                
                # Character-specific attributes
                'attributes': character_type['attributes'],
                
                # Generation metadata
                'generation_info': {
                    'timestamp': datetime.now().isoformat(),
                    'face_analysis_method': face_analysis.get('method', 'unknown'),
                    'has_face_data': face_analysis.get('face_detected', False)
                }
            }
            
            return character_params
            
        except Exception as e:
            print(f"Character parameters creation error: {e}")
            return self.create_default_character_parameters(character_description, style)
    
    def analyze_character_description(self, description):
        """Analyze character description to determine type and attributes"""
        description_lower = description.lower()
        
        # Character type detection
        if any(word in description_lower for word in ['robot', 'android', 'cyborg', 'mech']):
            character_type = 'robot'
            attributes = {'metallic': True, 'futuristic': True, 'mechanical': True}
        elif any(word in description_lower for word in ['alien', 'extraterrestrial', 'space']):
            character_type = 'alien'
            attributes = {'exotic': True, 'otherworldly': True, 'unique_features': True}
        elif any(word in description_lower for word in ['warrior', 'knight', 'soldier', 'fighter']):
            character_type = 'warrior'
            attributes = {'strong': True, 'armored': True, 'battle_ready': True}
        elif any(word in description_lower for word in ['wizard', 'mage', 'sorcerer', 'magic']):
            character_type = 'wizard'
            attributes = {'mystical': True, 'robed': True, 'magical': True}
        elif any(word in description_lower for word in ['fantasy', 'elf', 'dwarf', 'orc']):
            character_type = 'fantasy'
            attributes = {'fantasy_race': True, 'medieval': True, 'mythical': True}
        else:
            character_type = 'humanoid'
            attributes = {'realistic': True, 'human_like': True, 'natural': True}
        
        return {
            'name': character_type,
            'attributes': attributes
        }
    
    def extract_proportions(self, face_analysis):
        """Extract proportions from face analysis"""
        try:
            face_shape = face_analysis.get('face_shape', {})
            facial_proportions = face_analysis.get('facial_proportions', {})
            symmetry = face_analysis.get('symmetry', {})
            
            proportions = {
                'head_width': 1.0,
                'head_height': 1.0,
                'head_depth': 1.0,
                'body_scale': 1.0,
                'limb_scale': 1.0,
                'facial_symmetry': symmetry.get('symmetry_score', 0.8)
            }
            
            # Adjust based on face shape
            if face_shape:
                aspect_ratio = face_shape.get('aspect_ratio', 0.8)
                shape_type = face_shape.get('shape', 'oval')
                
                if shape_type == 'round':
                    proportions['head_width'] = 1.1
                    proportions['head_height'] = 0.95
                elif shape_type == 'long':
                    proportions['head_height'] = 1.1
                    proportions['head_width'] = 0.9
                elif shape_type == 'square':
                    proportions['head_width'] = 1.05
                    proportions['head_depth'] = 1.05
                
                # Apply aspect ratio
                proportions['head_width'] *= aspect_ratio
            
            return proportions
            
        except Exception as e:
            print(f"Proportion extraction error: {e}")
            return {
                'head_width': 1.0, 'head_height': 1.0, 'head_depth': 1.0,
                'body_scale': 1.0, 'limb_scale': 1.0, 'facial_symmetry': 0.8
            }
    
    def extract_colors(self, face_analysis, character_type):
        """Extract colors from face analysis"""
        try:
            skin_tone = face_analysis.get('skin_tone', {})
            color_palette = face_analysis.get('color_palette', {})
            
            colors = {
                'skin_primary': skin_tone.get('rgb', [200, 180, 160]),
                'skin_secondary': None,
                'accent_color': [0.2, 0.4, 0.8],
                'hair_color': [0.3, 0.2, 0.1],
                'eye_color': [0.2, 0.3, 0.4]
            }
            
            # Normalize skin color to 0-1 range
            if max(colors['skin_primary']) > 1.0:
                colors['skin_primary'] = [c/255.0 for c in colors['skin_primary']]
            
            # Create secondary skin color (slightly darker)
            colors['skin_secondary'] = [max(0, c - 0.05) for c in colors['skin_primary']]
            
            # Extract accent color from palette
            if color_palette and color_palette.get('dominant_colors'):
                dominant_colors = color_palette['dominant_colors']
                if len(dominant_colors) > 1:
                    accent_rgb = dominant_colors[1]
                    if max(accent_rgb) > 1.0:
                        accent_rgb = [c/255.0 for c in accent_rgb]
                    colors['accent_color'] = accent_rgb
            
            # Adjust colors based on character type
            if character_type['name'] == 'robot':
                colors['accent_color'] = [0.7, 0.8, 0.9]  # Metallic blue
            elif character_type['name'] == 'alien':
                colors['skin_primary'] = [0.6, 0.8, 0.6]  # Greenish
            elif character_type['name'] == 'warrior':
                colors['accent_color'] = [0.8, 0.2, 0.2]  # Red armor
            
            return colors
            
        except Exception as e:
            print(f"Color extraction error: {e}")
            return {
                'skin_primary': [0.8, 0.6, 0.4],
                'skin_secondary': [0.75, 0.55, 0.35],
                'accent_color': [0.2, 0.4, 0.8],
                'hair_color': [0.3, 0.2, 0.1],
                'eye_color': [0.2, 0.3, 0.4]
            }
    
    def create_metadata(self, face_analysis, character_params, quality, style):
        """Create metadata for the character"""
        return {
            'character_info': {
                'name': character_params.get('name', 'character'),
                'description': character_params.get('description', ''),
                'style': style,
                'quality': quality
            },
            'face_analysis': {
                'face_detected': face_analysis.get('face_detected', False),
                'face_shape': face_analysis.get('face_shape', {}),
                'skin_tone': face_analysis.get('skin_tone', {}),
                'symmetry_score': face_analysis.get('symmetry', {}).get('symmetry_score', 0.8)
            },
            'generation_info': {
                'timestamp': datetime.now().isoformat(),
                'generator_version': '2.0',
                'components': ['face_analysis', 'mesh_generator', 'material_system', 'glb_exporter']
            }
        }
    
    def save_character_files(self, glb_content, face_analysis, character_params, source_image_path):
        """Save character files and return result info"""
        try:
            # Generate unique job ID
            job_id = str(uuid.uuid4())[:8]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create filename
            character_name = character_params.get('name', 'character')
            filename = f"{character_name}_{timestamp}_{job_id}.glb"
            
            # Save GLB file
            glb_path = os.path.join('backend', 'outputs', 'models', filename)
            with open(glb_path, 'wb') as f:
                f.write(glb_content)
            
            # Save face analysis
            analysis_filename = f"{character_name}_{timestamp}_{job_id}_analysis.json"
            analysis_path = os.path.join('backend', 'outputs', 'analysis', analysis_filename)
            self.face_analyzer.save_analysis(face_analysis, analysis_path)
            
            # Calculate statistics
            vertex_count = len(glb_content) // 12  # Rough estimate
            face_count = vertex_count // 3  # Rough estimate
            
            # Validate GLB
            is_valid, validation_message = self.glb_exporter.validate_glb_output(glb_content)
            
            result = {
                'success': True,
                'job_id': job_id,
                'glb_file': filename,
                'glb_path': glb_path,
                'analysis_file': analysis_filename,
                'analysis_path': analysis_path,
                'file_size': len(glb_content),
                'vertex_count': vertex_count,
                'face_count': face_count,
                'character_params': character_params,
                'face_analysis': face_analysis,
                'validation': {
                    'is_valid': is_valid,
                    'message': validation_message
                },
                'source_image': source_image_path
            }
            
            return result
            
        except Exception as e:
            print(f"File saving error: {e}")
            return {
                'success': False,
                'error': str(e),
                'glb_content_size': len(glb_content) if glb_content else 0
            }
    
    def create_default_face_analysis(self):
        """Create default face analysis for text-only generation"""
        return {
            'face_detected': False,
            'face_shape': {'shape': 'oval', 'aspect_ratio': 0.8},
            'skin_tone': {'rgb': [200, 180, 160], 'hex': '#c8b4a0', 'tone_category': 'medium'},
            'eyes': {'detected': False, 'count': 0},
            'facial_proportions': {'width_to_height_ratio': 0.8},
            'symmetry': {'symmetry_score': 0.8},
            'color_palette': {'dominant_colors': [[200, 180, 160]], 'hex_colors': ['#c8b4a0']},
            'method': 'default_for_text_generation'
        }
    
    def create_default_character_parameters(self, description, style):
        """Create default character parameters"""
        return {
            'name': 'humanoid',
            'description': description,
            'style': style,
            'face_matched': False,
            'proportions': {
                'head_width': 1.0, 'head_height': 1.0, 'head_depth': 1.0,
                'body_scale': 1.0, 'limb_scale': 1.0, 'facial_symmetry': 0.8
            },
            'colors': {
                'skin_primary': [0.8, 0.6, 0.4],
                'accent_color': [0.2, 0.4, 0.8]
            },
            'attributes': {'realistic': True},
            'generation_info': {
                'timestamp': datetime.now().isoformat(),
                'fallback_used': True
            }
        }
