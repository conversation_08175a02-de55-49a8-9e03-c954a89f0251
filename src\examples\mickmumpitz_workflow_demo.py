"""
Demo script for using mickmumpitz's ComfyUI workflows in our project.

This script demonstrates how to use mickmumpitz's workflows for:
1. 3D animation rendering
2. Consistent character generation
"""

import os
import sys
import argparse
import base64
from pathlib import Path

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)

# Import our modules
from src.integrations.comfyui.mickmumpitz_workflows import MickmumpitzWorkflows
from src.pipelines.image_to_3d import ImageTo3DPipeline

def setup_mickmumpitz_workflows():
    """Set up mickmumpitz's workflows."""
    print("Setting up mickmumpitz's workflows...")
    
    # Create the workflows directory
    workflows_dir = os.path.join(project_root, 'workflows', 'mickmumpitz')
    os.makedirs(workflows_dir, exist_ok=True)
    
    # Run the download script
    download_script = os.path.join(project_root, 'src', 'scripts', 'download_mickmumpitz_workflows.py')
    os.system(f"{sys.executable} {download_script}")
    
    # Initialize the workflows
    mickmumpitz = MickmumpitzWorkflows()
    
    # Set up required nodes
    print("\nDo you want to set up the required ComfyUI nodes? (y/n)")
    choice = input().lower()
    if choice == 'y':
        mickmumpitz.setup_required_nodes()
    
    print("\nSetup complete!")

def demo_3d_rendering():
    """Demonstrate 3D animation rendering with mickmumpitz's workflow."""
    print("Demonstrating 3D animation rendering with mickmumpitz's workflow...")
    
    # Initialize the workflows
    mickmumpitz = MickmumpitzWorkflows()
    
    # Check if workflows are available
    if not os.path.exists(os.path.join(project_root, 'workflows', 'mickmumpitz', '200524_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v3.json')):
        print("Workflow files not found. Please run setup first.")
        return
    
    # Create output directories
    blender_output_dir = os.path.join(project_root, 'output', 'blender_demo')
    os.makedirs(blender_output_dir, exist_ok=True)
    
    # Prepare Blender outputs
    print("\nPreparing Blender outputs...")
    mickmumpitz.prepare_blender_outputs(
        blender_scene_path='',  # No scene file, just instructions
        output_dir=blender_output_dir
    )
    
    # Render with mickmumpitz's workflow
    print("\nTo render with mickmumpitz's workflow:")
    print("1. Open ComfyUI")
    print("2. Load the workflow file: workflows/mickmumpitz/200524_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v3.json")
    print("3. Update the input paths to point to:")
    print(f"   - Depth maps: {os.path.join(blender_output_dir, 'depth')}")
    print(f"   - Mask maps: {os.path.join(blender_output_dir, 'mask')}")
    print(f"   - Outline maps: {os.path.join(blender_output_dir, 'outline')}")
    print("4. Run the workflow")

def demo_character_generation():
    """Demonstrate character generation with mickmumpitz's workflow."""
    print("Demonstrating character generation with mickmumpitz's workflow...")
    
    # Initialize the workflows
    mickmumpitz = MickmumpitzWorkflows()
    
    # Check if workflows are available
    if not os.path.exists(os.path.join(project_root, 'workflows', 'mickmumpitz', '241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json')):
        print("Workflow files not found. Please run setup first.")
        return
    
    # Create output directory
    output_dir = os.path.join(project_root, 'output', 'characters_demo')
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate a character sheet
    print("\nTo generate a character sheet:")
    print("1. Open ComfyUI")
    print("2. Load the workflow file: workflows/mickmumpitz/241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json")
    print("3. Update the character prompt to your desired character description")
    print(f"4. Update the output path to: {output_dir}")
    print("5. Run the workflow")

def demo_image_to_3d_pipeline():
    """Demonstrate the image to 3D pipeline with mickmumpitz's workflow."""
    print("Demonstrating the image to 3D pipeline with mickmumpitz's workflow...")
    
    # Check if an image file is provided
    print("\nPlease provide the path to an image file:")
    image_path = input().strip()
    
    if not os.path.exists(image_path):
        print(f"Image file not found: {image_path}")
        return
    
    # Initialize the pipeline
    pipeline = ImageTo3DPipeline()
    
    # Read the image file
    with open(image_path, 'rb') as f:
        image_data = base64.b64encode(f.read()).decode('utf-8')
    
    # Process the image
    print("\nProcessing the image...")
    try:
        result = pipeline.process(
            input_data=image_data,
            parameters={
                'use_mickmumpitz': True,
                'model': 'shap-e',
                'texture_quality': 'high',
                'optimize_mesh': True
            }
        )
        
        print("\nProcessing complete!")
        print(f"Model path: {result['model_path']}")
        if 'rendered_files' in result:
            print(f"Rendered files: {len(result['rendered_files'])}")
            for i, file in enumerate(result['rendered_files'][:5]):
                print(f"  {i+1}. {file}")
            if len(result['rendered_files']) > 5:
                print(f"  ... and {len(result['rendered_files']) - 5} more")
        
    except Exception as e:
        print(f"Error processing image: {str(e)}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Demo for mickmumpitz\'s ComfyUI workflows')
    parser.add_argument('--setup', action='store_true', help='Set up mickmumpitz\'s workflows')
    parser.add_argument('--render', action='store_true', help='Demonstrate 3D animation rendering')
    parser.add_argument('--character', action='store_true', help='Demonstrate character generation')
    parser.add_argument('--pipeline', action='store_true', help='Demonstrate the image to 3D pipeline')
    args = parser.parse_args()
    
    # If no arguments are provided, show help
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    # Run the requested demos
    if args.setup:
        setup_mickmumpitz_workflows()
    
    if args.render:
        demo_3d_rendering()
    
    if args.character:
        demo_character_generation()
    
    if args.pipeline:
        demo_image_to_3d_pipeline()

if __name__ == '__main__':
    main()
