#!/usr/bin/env python3
"""Start server with error handling"""

import sys
import os
sys.path.append('backend')

try:
    print("🚀 Starting 3D Character Generator Server")
    print("=" * 40)
    
    # Import and start the app
    from app import app, socketio
    
    print("✅ App imported successfully")
    print("🌐 Starting server on http://localhost:5000")
    
    # Start the server
    socketio.run(app, debug=True, host='0.0.0.0', port=5000, allow_unsafe_werkzeug=True)
    
except Exception as e:
    print(f"❌ Server startup failed: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
