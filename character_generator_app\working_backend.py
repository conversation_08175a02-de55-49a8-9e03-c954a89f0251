#!/usr/bin/env python3
"""
Working backend server for 3D Character Generator
"""

import sys
import os
import json
import uuid
import time
from datetime import datetime

# Add backend to path
sys.path.append('backend')

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Ensure output directories exist
os.makedirs('backend/outputs/models', exist_ok=True)

# Global variables
active_jobs = {}

@app.route('/')
def index():
    return jsonify({
        'message': '3D Character Generator API',
        'status': 'running',
        'version': '2.0.0'
    })

@app.route('/api/test')
def test():
    return jsonify({
        'test': 'success',
        'backend': 'working'
    })

@app.route('/api/generate', methods=['POST'])
def generate_character():
    try:
        data = request.get_json()
        text_prompt = data.get('prompt', 'A 3D character')
        
        print(f"🎯 Generating: {text_prompt}")
        
        # Import character generation (lazy import)
        try:
            from app import create_character_glb, analyze_character_prompt
            
            # Generate character
            character_type = analyze_character_prompt(text_prompt)
            glb_content = create_character_glb(text_prompt)
            
            # Save file
            job_id = str(uuid.uuid4())[:8]
            filename = f"character_{job_id}.glb"
            filepath = os.path.join('backend', 'outputs', 'models', filename)
            
            with open(filepath, 'wb') as f:
                f.write(glb_content)
            
            print(f"✅ Generated: {filename} ({len(glb_content)} bytes)")
            
            return jsonify({
                'success': True,
                'job_id': job_id,
                'filename': filename,
                'character_type': character_type['name'],
                'file_size': len(glb_content),
                'download_url': f'/api/download/{filename}'
            })
            
        except Exception as e:
            print(f"⚠️ Using simple fallback: {e}")
            
            # Simple fallback
            job_id = str(uuid.uuid4())[:8]
            filename = f"simple_character_{job_id}.txt"
            filepath = os.path.join('backend', 'outputs', 'models', filename)
            
            content = f"Character: {text_prompt}\nGenerated: {datetime.now()}\nType: Simple"
            
            with open(filepath, 'w') as f:
                f.write(content)
            
            return jsonify({
                'success': True,
                'job_id': job_id,
                'filename': filename,
                'character_type': 'simple',
                'file_size': len(content),
                'download_url': f'/api/download/{filename}',
                'note': 'Simple fallback used'
            })
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/download/<filename>')
def download_file(filename):
    try:
        filepath = os.path.join('backend', 'outputs', 'models', filename)
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True)
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🚀 Starting Character Generator Backend")
    print("📱 API: http://localhost:5000")
    print("🧪 Test: http://localhost:5000/api/test")
    
    app.run(host='0.0.0.0', port=5000, debug=False)
