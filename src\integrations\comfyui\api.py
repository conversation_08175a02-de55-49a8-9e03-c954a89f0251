"""
ComfyUI API integration module.
"""

import os
import json
import requests
import time
import base64
import tempfile
from PIL import Image
import io

class ComfyUIAPI:
    """
    API client for interacting with ComfyUI.
    
    ComfyUI is a powerful node-based UI for Stable Diffusion and other AI models.
    This class provides methods to interact with ComfyUI's API for various tasks.
    """
    
    def __init__(self, api_url=None):
        """
        Initialize the ComfyUI API client.
        
        Args:
            api_url: URL of the ComfyUI API (default: http://localhost:8188)
        """
        self.api_url = api_url or os.environ.get('COMFYUI_API_URL', 'http://localhost:8188')
    
    def _check_connection(self):
        """Check if ComfyUI is running and accessible."""
        try:
            response = requests.get(f"{self.api_url}/system_stats")
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
    
    def generate_image(self, prompt, negative_prompt="", width=512, height=512, steps=30, seed=None):
        """
        Generate an image using ComfyUI.
        
        Args:
            prompt: Text prompt for image generation
            negative_prompt: Negative prompt (default: "")
            width: Image width (default: 512)
            height: Image height (default: 512)
            steps: Number of sampling steps (default: 30)
            seed: Random seed (default: None, which means random)
            
        Returns:
            str: Path to the generated image
        """
        if not self._check_connection():
            raise ConnectionError("ComfyUI is not running or not accessible")
        
        # Create a basic workflow for text-to-image generation
        workflow = {
            "3": {
                "inputs": {
                    "seed": seed or int(time.time()) % 10000000,
                    "steps": steps,
                    "cfg": 7.5,
                    "sampler_name": "euler_ancestral",
                    "scheduler": "normal",
                    "denoise": 1.0,
                    "model": ["4", 0],
                    "positive": ["5", 0],
                    "negative": ["6", 0],
                    "latent_image": ["7", 0]
                },
                "class_type": "KSampler"
            },
            "4": {
                "inputs": {
                    "ckpt_name": "dreamshaper_8.safetensors"
                },
                "class_type": "CheckpointLoaderSimple"
            },
            "5": {
                "inputs": {
                    "text": prompt,
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "6": {
                "inputs": {
                    "text": negative_prompt,
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "7": {
                "inputs": {
                    "width": width,
                    "height": height,
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage"
            },
            "8": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode"
            },
            "9": {
                "inputs": {
                    "filename_prefix": "output",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage"
            }
        }
        
        # Queue the workflow
        response = requests.post(f"{self.api_url}/prompt", json={"prompt": workflow})
        data = response.json()
        prompt_id = data.get('prompt_id')
        
        # Wait for the workflow to complete
        while True:
            response = requests.get(f"{self.api_url}/history/{prompt_id}")
            if response.status_code == 200:
                history = response.json()
                if prompt_id in history:
                    if 'outputs' in history[prompt_id] and '9' in history[prompt_id]['outputs']:
                        # Get the image path
                        image_data = history[prompt_id]['outputs']['9']['images'][0]
                        image_path = os.path.join(os.environ.get('COMFYUI_OUTPUT_DIR', 'output'), image_data['filename'])
                        return image_path
            time.sleep(1)
    
    def generate_textures(self, mesh_path, reference_image_path, quality='medium'):
        """
        Generate textures for a 3D model using ComfyUI.
        
        Args:
            mesh_path: Path to the 3D mesh file
            reference_image_path: Path to the reference image
            quality: Texture quality (low, medium, high)
            
        Returns:
            list: Paths to the generated texture files
        """
        if not self._check_connection():
            raise ConnectionError("ComfyUI is not running or not accessible")
        
        # Quality settings
        quality_settings = {
            'low': {'resolution': 512, 'steps': 20},
            'medium': {'resolution': 1024, 'steps': 30},
            'high': {'resolution': 2048, 'steps': 40}
        }
        
        settings = quality_settings.get(quality, quality_settings['medium'])
        
        # TODO: Implement actual texture generation workflow
        # This is a placeholder that would need to be replaced with actual ComfyUI workflow
        
        # For now, return dummy texture paths
        texture_dir = os.path.dirname(mesh_path)
        return [
            os.path.join(texture_dir, "diffuse.png"),
            os.path.join(texture_dir, "normal.png"),
            os.path.join(texture_dir, "roughness.png")
        ]
