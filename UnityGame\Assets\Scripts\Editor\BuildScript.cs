using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

public class BuildScript : EditorWindow
{
    // Build parameters
    private string buildName = "3DAdventureGame";
    private string buildPath = "Builds";
    private bool buildWindows = true;
    private bool buildMac = false;
    private bool buildLinux = false;
    private bool buildWebGL = false;
    private bool developmentBuild = false;
    
    [MenuItem("Tools/Build Game")]
    public static void ShowWindow()
    {
        GetWindow<BuildScript>("Build Game");
    }
    
    void OnGUI()
    {
        GUILayout.Label("Build Game", EditorStyles.boldLabel);
        
        EditorGUILayout.Space();
        
        // Build parameters
        buildName = EditorGUILayout.TextField("Build Name", buildName);
        buildPath = EditorGUILayout.TextField("Build Path", buildPath);
        
        EditorGUILayout.Space();
        
        // Build targets
        GUILayout.Label("Build Targets", EditorStyles.boldLabel);
        buildWindows = EditorGUILayout.Toggle("Windows", buildWindows);
        buildMac = EditorGUILayout.Toggle("Mac", buildMac);
        buildLinux = EditorGUILayout.Toggle("Linux", buildLinux);
        buildWebGL = EditorGUILayout.Toggle("WebGL", buildWebGL);
        
        EditorGUILayout.Space();
        
        // Build options
        GUILayout.Label("Build Options", EditorStyles.boldLabel);
        developmentBuild = EditorGUILayout.Toggle("Development Build", developmentBuild);
        
        EditorGUILayout.Space();
        
        // Build button
        if (GUILayout.Button("Build"))
        {
            BuildGame();
        }
    }
    
    void BuildGame()
    {
        // Check if any build target is selected
        if (!buildWindows && !buildMac && !buildLinux && !buildWebGL)
        {
            Debug.LogError("No build target selected!");
            return;
        }
        
        // Get all scenes in the build settings
        string[] scenes = new string[EditorBuildSettings.scenes.Length];
        for (int i = 0; i < EditorBuildSettings.scenes.Length; i++)
        {
            scenes[i] = EditorBuildSettings.scenes[i].path;
        }
        
        // Check if there are any scenes in the build settings
        if (scenes.Length == 0)
        {
            Debug.LogError("No scenes in build settings!");
            return;
        }
        
        // Create build options
        BuildOptions options = BuildOptions.None;
        if (developmentBuild)
        {
            options |= BuildOptions.Development;
        }
        
        // Create build directory if it doesn't exist
        if (!Directory.Exists(buildPath))
        {
            Directory.CreateDirectory(buildPath);
        }
        
        // Build for each selected target
        if (buildWindows)
        {
            BuildWindows(scenes, options);
        }
        
        if (buildMac)
        {
            BuildMac(scenes, options);
        }
        
        if (buildLinux)
        {
            BuildLinux(scenes, options);
        }
        
        if (buildWebGL)
        {
            BuildWebGL(scenes, options);
        }
        
        Debug.Log("Build completed successfully!");
    }
    
    void BuildWindows(string[] scenes, BuildOptions options)
    {
        // Create build directory
        string windowsBuildPath = Path.Combine(buildPath, "Windows");
        if (!Directory.Exists(windowsBuildPath))
        {
            Directory.CreateDirectory(windowsBuildPath);
        }
        
        // Build for Windows
        string exePath = Path.Combine(windowsBuildPath, buildName + ".exe");
        BuildPipeline.BuildPlayer(scenes, exePath, BuildTarget.StandaloneWindows64, options);
        
        Debug.Log("Windows build completed: " + exePath);
    }
    
    void BuildMac(string[] scenes, BuildOptions options)
    {
        // Create build directory
        string macBuildPath = Path.Combine(buildPath, "Mac");
        if (!Directory.Exists(macBuildPath))
        {
            Directory.CreateDirectory(macBuildPath);
        }
        
        // Build for Mac
        string appPath = Path.Combine(macBuildPath, buildName + ".app");
        BuildPipeline.BuildPlayer(scenes, appPath, BuildTarget.StandaloneOSX, options);
        
        Debug.Log("Mac build completed: " + appPath);
    }
    
    void BuildLinux(string[] scenes, BuildOptions options)
    {
        // Create build directory
        string linuxBuildPath = Path.Combine(buildPath, "Linux");
        if (!Directory.Exists(linuxBuildPath))
        {
            Directory.CreateDirectory(linuxBuildPath);
        }
        
        // Build for Linux
        string exePath = Path.Combine(linuxBuildPath, buildName);
        BuildPipeline.BuildPlayer(scenes, exePath, BuildTarget.StandaloneLinux64, options);
        
        Debug.Log("Linux build completed: " + exePath);
    }
    
    void BuildWebGL(string[] scenes, BuildOptions options)
    {
        // Create build directory
        string webGLBuildPath = Path.Combine(buildPath, "WebGL");
        if (!Directory.Exists(webGLBuildPath))
        {
            Directory.CreateDirectory(webGLBuildPath);
        }
        
        // Build for WebGL
        BuildPipeline.BuildPlayer(scenes, webGLBuildPath, BuildTarget.WebGL, options);
        
        Debug.Log("WebGL build completed: " + webGLBuildPath);
    }
}
