/**
 * Main JavaScript file for the AI Content Generation Pipeline frontend.
 */

// API endpoint
const API_URL = 'http://localhost:5000/api';

// DOM elements
const dropzone = document.getElementById('dropzone');
const fileInput = document.getElementById('fileInput');
const textPrompt = document.getElementById('textPrompt');
const inputType = document.getElementById('inputType');
const outputType = document.getElementById('outputType');
const model = document.getElementById('model');
const quality = document.getElementById('quality');
const optimize = document.getElementById('optimize');
const generateBtn = document.getElementById('generateBtn');
const downloadBtn = document.getElementById('downloadBtn');
const viewBtn = document.getElementById('viewBtn');
const shareBtn = document.getElementById('shareBtn');
const previewContainer = document.querySelector('.preview-container');
const progressContainer = document.querySelector('.progress-container');
const progressBar = document.getElementById('progressBar');
const statusLabel = document.getElementById('statusLabel');
const jobHistory = document.getElementById('jobHistory');

// Current job state
let currentJobId = null;
let currentResult = null;
let uploadedFile = null;

// Initialize the application
function init() {
    // Set up event listeners
    dropzone.addEventListener('click', () => fileInput.click());
    dropzone.addEventListener('dragover', handleDragOver);
    dropzone.addEventListener('dragleave', handleDragLeave);
    dropzone.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
    generateBtn.addEventListener('click', handleGenerate);
    downloadBtn.addEventListener('click', handleDownload);
    viewBtn.addEventListener('click', handleView);
    shareBtn.addEventListener('click', handleShare);
    
    // Update UI based on input/output type selection
    inputType.addEventListener('change', updateUI);
    outputType.addEventListener('change', updateUI);
    
    // Initial UI update
    updateUI();
}

// Update UI based on selected input/output types
function updateUI() {
    const selectedInputType = inputType.value;
    const selectedOutputType = outputType.value;
    
    // Show/hide file upload or text prompt based on input type
    if (selectedInputType === 'text') {
        dropzone.style.display = 'none';
        textPrompt.parentElement.style.display = 'block';
    } else {
        dropzone.style.display = 'block';
        textPrompt.parentElement.style.display = selectedInputType === 'text' ? 'block' : 'none';
    }
    
    // Update model options based on input/output combination
    updateModelOptions(selectedInputType, selectedOutputType);
    
    // Update view button visibility based on output type
    viewBtn.style.display = selectedOutputType === '3d' ? 'inline-block' : 'none';
}

// Update model options based on input/output combination
function updateModelOptions(inputType, outputType) {
    // Clear existing options
    model.innerHTML = '';
    
    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = 'default';
    defaultOption.textContent = 'Default';
    model.appendChild(defaultOption);
    
    // Add specific options based on input/output combination
    if (inputType === 'image' && outputType === '3d') {
        addModelOption('shap-e', 'Shap-E');
        addModelOption('zero123', 'Zero123');
        addModelOption('syncdreamer', 'SyncDreamer');
    } else if (inputType === 'text' && outputType === '3d') {
        addModelOption('shap-e', 'Shap-E');
        addModelOption('dreamfusion', 'DreamFusion');
        addModelOption('magic3d', 'Magic3D');
    } else if (inputType === 'text' && outputType === 'image') {
        addModelOption('stable-diffusion', 'Stable Diffusion');
        addModelOption('dalle', 'DALL-E');
        addModelOption('midjourney', 'Midjourney');
    } else if (outputType === 'game') {
        addModelOption('unity', 'Unity');
        addModelOption('unreal', 'Unreal Engine');
    }
}

// Add a model option to the select element
function addModelOption(value, text) {
    const option = document.createElement('option');
    option.value = value;
    option.textContent = text;
    model.appendChild(option);
}

// Handle drag over event
function handleDragOver(e) {
    e.preventDefault();
    dropzone.classList.add('highlight');
}

// Handle drag leave event
function handleDragLeave(e) {
    e.preventDefault();
    dropzone.classList.remove('highlight');
}

// Handle drop event
function handleDrop(e) {
    e.preventDefault();
    dropzone.classList.remove('highlight');
    
    if (e.dataTransfer.files.length > 0) {
        handleFiles(e.dataTransfer.files);
    }
}

// Handle file select event
function handleFileSelect(e) {
    if (e.target.files.length > 0) {
        handleFiles(e.target.files);
    }
}

// Handle files
function handleFiles(files) {
    uploadedFile = files[0];
    
    // Show preview if it's an image
    if (uploadedFile.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewContainer.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
        };
        reader.readAsDataURL(uploadedFile);
    } else {
        // Show file info for non-image files
        previewContainer.innerHTML = `<div class="placeholder">
            <p>File: ${uploadedFile.name}</p>
            <p>Type: ${uploadedFile.type || 'Unknown'}</p>
            <p>Size: ${formatFileSize(uploadedFile.size)}</p>
        </div>`;
    }
    
    // Auto-detect input type based on file
    if (uploadedFile.type.startsWith('image/')) {
        inputType.value = 'image';
    } else if (uploadedFile.type.startsWith('video/')) {
        inputType.value = 'video';
    } else if (uploadedFile.name.endsWith('.glb') || uploadedFile.name.endsWith('.gltf') || uploadedFile.name.endsWith('.obj')) {
        inputType.value = '3d';
    }
    
    updateUI();
}

// Format file size
function formatFileSize(bytes) {
    if (bytes < 1024) {
        return bytes + ' bytes';
    } else if (bytes < 1024 * 1024) {
        return (bytes / 1024).toFixed(2) + ' KB';
    } else {
        return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    }
}

// Handle generate button click
async function handleGenerate() {
    // Validate input
    if (inputType.value === 'text' && !textPrompt.value.trim()) {
        alert('Please enter a text prompt');
        return;
    } else if (inputType.value !== 'text' && !uploadedFile) {
        alert('Please upload a file');
        return;
    }
    
    // Show progress
    progressContainer.style.display = 'block';
    progressBar.style.width = '0%';
    statusLabel.textContent = 'Processing...';
    
    // Disable generate button
    generateBtn.disabled = true;
    
    try {
        // Prepare input data
        let inputData;
        if (inputType.value === 'text') {
            inputData = textPrompt.value.trim();
        } else {
            // Convert file to base64
            inputData = await fileToBase64(uploadedFile);
        }
        
        // Prepare parameters
        const parameters = {
            model: model.value,
            quality: quality.value,
            optimize: optimize.checked
        };
        
        // Send generation request
        const response = await fetch(`${API_URL}/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                input_type: inputType.value,
                output_type: outputType.value,
                input_data: inputData,
                parameters: parameters
            })
        });
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        // Start polling for job status
        currentJobId = data.job_id;
        pollJobStatus(currentJobId);
        
        // Add job to history
        addJobToHistory(currentJobId, inputType.value, outputType.value);
        
    } catch (error) {
        console.error('Error:', error);
        statusLabel.textContent = `Error: ${error.message}`;
        progressBar.style.width = '0%';
        progressBar.classList.add('bg-danger');
        
        // Re-enable generate button
        generateBtn.disabled = false;
    }
}

// Convert file to base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

// Poll job status
async function pollJobStatus(jobId) {
    try {
        const response = await fetch(`${API_URL}/job/${jobId}`);
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        // Update progress
        updateProgress(data.status);
        
        // Check if job is completed or failed
        if (data.status === 'completed') {
            await getJobResult(jobId);
        } else if (data.status === 'failed') {
            statusLabel.textContent = `Error: ${data.error || 'Unknown error'}`;
            progressBar.style.width = '100%';
            progressBar.classList.add('bg-danger');
            generateBtn.disabled = false;
        } else {
            // Continue polling
            setTimeout(() => pollJobStatus(jobId), 1000);
        }
        
    } catch (error) {
        console.error('Error polling job status:', error);
        statusLabel.textContent = `Error: ${error.message}`;
        progressBar.style.width = '0%';
        progressBar.classList.add('bg-danger');
        generateBtn.disabled = false;
    }
}

// Update progress based on job status
function updateProgress(status) {
    let progress = 0;
    
    switch (status) {
        case 'pending':
            progress = 10;
            break;
        case 'processing':
            progress = 50;
            break;
        case 'completed':
            progress = 100;
            break;
        case 'failed':
            progress = 100;
            progressBar.classList.add('bg-danger');
            break;
    }
    
    progressBar.style.width = `${progress}%`;
    statusLabel.textContent = `Status: ${status}`;
}

// Get job result
async function getJobResult(jobId) {
    try {
        const response = await fetch(`${API_URL}/job/${jobId}/result`);
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        // Store result
        currentResult = data.result;
        
        // Update UI
        updateResultPreview(data.result);
        
        // Enable buttons
        downloadBtn.disabled = false;
        viewBtn.disabled = outputType.value === '3d' ? false : true;
        shareBtn.disabled = false;
        generateBtn.disabled = false;
        
    } catch (error) {
        console.error('Error getting job result:', error);
        statusLabel.textContent = `Error: ${error.message}`;
        generateBtn.disabled = false;
    }
}

// Update result preview
function updateResultPreview(result) {
    if (outputType.value === 'image') {
        // Show image preview
        previewContainer.innerHTML = `<img src="data:image/png;base64,${result.preview_image}" alt="Generated Image">`;
    } else if (outputType.value === '3d') {
        // Show 3D model preview
        previewContainer.innerHTML = `<img src="data:image/png;base64,${result.preview_image}" alt="3D Model Preview">`;
    } else if (outputType.value === 'video') {
        // Show video preview
        previewContainer.innerHTML = `<video controls><source src="data:video/mp4;base64,${result.preview_video}" type="video/mp4"></video>`;
    } else if (outputType.value === 'game') {
        // Show game preview
        previewContainer.innerHTML = `<div class="placeholder">
            <p>Game created successfully!</p>
            <p>Click Download to get the game files.</p>
        </div>`;
    }
}

// Add job to history
function addJobToHistory(jobId, inputType, outputType) {
    const item = document.createElement('li');
    item.className = 'list-group-item d-flex justify-content-between align-items-center';
    item.innerHTML = `
        <div>
            <span class="badge bg-primary">${inputType} → ${outputType}</span>
            <small class="text-muted ms-2">${new Date().toLocaleTimeString()}</small>
        </div>
        <button class="btn btn-sm btn-outline-secondary load-job" data-job-id="${jobId}">Load</button>
    `;
    
    // Add click handler for load button
    item.querySelector('.load-job').addEventListener('click', () => {
        currentJobId = jobId;
        getJobResult(jobId);
    });
    
    // Add to history
    jobHistory.prepend(item);
}

// Handle download button click
function handleDownload() {
    if (!currentResult) return;
    
    let downloadUrl, filename;
    
    if (outputType.value === 'image') {
        downloadUrl = `data:image/png;base64,${currentResult.preview_image}`;
        filename = 'generated_image.png';
    } else if (outputType.value === '3d') {
        // For 3D models, we would need to download the actual model file
        // This is a placeholder and would need to be implemented based on the actual API response
        alert('3D model download not implemented yet');
        return;
    } else if (outputType.value === 'video') {
        downloadUrl = `data:video/mp4;base64,${currentResult.preview_video}`;
        filename = 'generated_video.mp4';
    } else if (outputType.value === 'game') {
        // For games, we would need to download a zip file
        // This is a placeholder and would need to be implemented based on the actual API response
        alert('Game download not implemented yet');
        return;
    }
    
    // Create download link
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

// Handle view button click
function handleView() {
    if (!currentResult || outputType.value !== '3d') return;
    
    // Open 3D viewer in a new window
    // This is a placeholder and would need to be implemented based on the actual API response
    alert('3D viewer not implemented yet');
}

// Handle share button click
function handleShare() {
    if (!currentResult) return;
    
    // Share functionality
    // This is a placeholder and would need to be implemented based on the actual requirements
    alert('Share functionality not implemented yet');
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', init);
