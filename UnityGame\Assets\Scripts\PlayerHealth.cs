using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class PlayerHealth : MonoBehaviour
{
    // Health parameters
    public int maxHealth = 100;
    public int currentHealth;
    
    // UI elements
    public Slider healthSlider;
    public Image damageImage;
    public float flashSpeed = 5f;
    public Color flashColor = new Color(1f, 0f, 0f, 0.1f);
    
    // Component references
    private Animator animator;
    private PlayerController playerController;
    
    // Damage effect variables
    private bool damaged = false;
    
    // Start is called before the first frame update
    void Start()
    {
        // Get component references
        animator = GetComponent<Animator>();
        playerController = GetComponent<PlayerController>();
        
        // Initialize health
        currentHealth = maxHealth;
        
        // Initialize UI
        if (healthSlider != null)
        {
            healthSlider.maxValue = maxHealth;
            healthSlider.value = currentHealth;
        }
    }

    // Update is called once per frame
    void Update()
    {
        // Flash the damage image if damaged
        if (damaged)
        {
            if (damageImage != null)
            {
                damageImage.color = flashColor;
            }
        }
        else
        {
            if (damageImage != null)
            {
                damageImage.color = Color.Lerp(damageImage.color, Color.clear, flashSpeed * Time.deltaTime);
            }
        }
        
        // Reset the damaged flag
        damaged = false;
    }
    
    // Take damage
    public void TakeDamage(int amount)
    {
        // Set the damaged flag
        damaged = true;
        
        // Reduce health
        currentHealth -= amount;
        
        // Update UI
        if (healthSlider != null)
        {
            healthSlider.value = currentHealth;
        }
        
        // Play damage audio
        AudioSource audioSource = GetComponent<AudioSource>();
        if (audioSource != null)
        {
            audioSource.Play();
        }
        
        // Check if the player is dead
        if (currentHealth <= 0 && !IsDead())
        {
            Death();
        }
    }
    
    // Heal the player
    public void Heal(int amount)
    {
        // Increase health
        currentHealth += amount;
        
        // Clamp health to max
        currentHealth = Mathf.Clamp(currentHealth, 0, maxHealth);
        
        // Update UI
        if (healthSlider != null)
        {
            healthSlider.value = currentHealth;
        }
    }
    
    // Check if the player is dead
    public bool IsDead()
    {
        return currentHealth <= 0;
    }
    
    // Handle player death
    void Death()
    {
        // Set the death flag
        if (animator != null)
        {
            animator.SetTrigger("Die");
        }
        
        // Disable the player controller
        if (playerController != null)
        {
            playerController.enabled = false;
        }
        
        // Game over
        if (GameManager.instance != null)
        {
            GameManager.instance.GameOver();
        }
    }
}
