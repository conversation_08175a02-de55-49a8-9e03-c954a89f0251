"""
Direct Blender Editing Integration

This module provides functionality to directly edit 3D models in Blender
and integrate the results with the AI content generation pipeline.
"""

import os
import sys
import subprocess
import tempfile
import json
import time
from pathlib import Path

class BlenderDirectEditor:
    """
    Class for directly editing 3D models in Blender.
    """
    
    def __init__(self, blender_path=None):
        """
        Initialize the Blender direct editor.
        
        Args:
            blender_path: Path to the Blender executable (default: from environment variable)
        """
        self.blender_path = blender_path or os.environ.get('BLENDER_PATH', 'blender')
        
        # Create scripts directory if it doesn't exist
        self.scripts_dir = os.path.join(os.path.dirname(__file__), 'scripts')
        os.makedirs(self.scripts_dir, exist_ok=True)
        
        # Create the Blender scripts if they don't exist
        self._create_blender_scripts()
    
    def _create_blender_scripts(self):
        """Create Blender Python scripts for automation."""
        # Script for direct editing
        direct_edit_script = """
import bpy
import sys
import os
import json
import tempfile

# Get arguments
args = sys.argv[sys.argv.index('--') + 1:]
params = json.loads(args[0])

model_path = params.get('model_path', '')
edit_mode = params.get('edit_mode', 'sculpt')
output_path = params.get('output_path', '')
auto_save = params.get('auto_save', True)
auto_save_interval = params.get('auto_save_interval', 300)  # 5 minutes

# Clear default scene
bpy.ops.wm.read_factory_settings(use_empty=True)

# Import the model if provided
if model_path and os.path.exists(model_path):
    file_ext = os.path.splitext(model_path)[1].lower()
    if file_ext == '.obj':
        bpy.ops.import_scene.obj(filepath=model_path)
    elif file_ext == '.fbx':
        bpy.ops.import_scene.fbx(filepath=model_path)
    elif file_ext == '.glb' or file_ext == '.gltf':
        bpy.ops.import_scene.gltf(filepath=model_path)
    elif file_ext == '.blend':
        bpy.ops.wm.open_mainfile(filepath=model_path)
    else:
        print(f"Unsupported file format: {file_ext}")

# Set up auto-save if enabled
if auto_save:
    bpy.context.preferences.filepaths.use_auto_save = True
    bpy.context.preferences.filepaths.auto_save_time = auto_save_interval // 60  # Convert to minutes

# Set up the UI for the specified edit mode
if edit_mode == 'sculpt':
    # Switch to Sculpt mode
    if bpy.context.active_object and bpy.context.active_object.type == 'MESH':
        bpy.ops.object.mode_set(mode='SCULPT')
        
        # Set up sculpt tools
        bpy.context.tool_settings.sculpt.use_symmetry_x = True
        bpy.context.tool_settings.sculpt.detail_size = 8
        
        # Set up a basic sculpt brush
        brush = bpy.context.tool_settings.sculpt.brush
        if brush:
            brush.strength = 0.5
            brush.size = 35

elif edit_mode == 'edit':
    # Switch to Edit mode
    if bpy.context.active_object and bpy.context.active_object.type == 'MESH':
        bpy.ops.object.mode_set(mode='EDIT')
        
        # Set up edit tools
        bpy.context.tool_settings.mesh_select_mode = (True, True, True)  # Vertices, Edges, Faces

elif edit_mode == 'texture':
    # Switch to Texture Paint mode
    if bpy.context.active_object and bpy.context.active_object.type == 'MESH':
        bpy.ops.object.mode_set(mode='TEXTURE_PAINT')
        
        # Set up texture paint tools
        if not bpy.context.active_object.data.uv_layers:
            bpy.ops.mesh.uv_texture_add()

# Set up a handler to save the file when Blender closes
if output_path:
    def save_on_exit():
        # Export the model
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        file_ext = os.path.splitext(output_path)[1].lower()
        
        # Select all objects
        bpy.ops.object.select_all(action='SELECT')
        
        if file_ext == '.obj':
            bpy.ops.export_scene.obj(filepath=output_path, use_selection=True)
        elif file_ext == '.fbx':
            bpy.ops.export_scene.fbx(filepath=output_path, use_selection=True)
        elif file_ext == '.glb' or file_ext == '.gltf':
            bpy.ops.export_scene.gltf(filepath=output_path, use_selection=True)
        elif file_ext == '.blend':
            bpy.ops.wm.save_as_mainfile(filepath=output_path)
        else:
            # Default to GLB if extension not specified
            output_path_glb = os.path.splitext(output_path)[0] + '.glb'
            bpy.ops.export_scene.gltf(filepath=output_path_glb, use_selection=True)
    
    # Register the save handler
    bpy.app.handlers.save_pre.append(save_on_exit)

print("Blender direct editing setup complete.")
print(f"Model: {model_path}")
print(f"Edit Mode: {edit_mode}")
print(f"Output Path: {output_path}")
"""
        
        # Save the script
        with open(os.path.join(self.scripts_dir, 'direct_edit.py'), 'w') as f:
            f.write(direct_edit_script)
    
    def open_for_editing(self, model_path=None, edit_mode='sculpt', output_path=None, auto_save=True):
        """
        Open Blender for direct editing of a 3D model.
        
        Args:
            model_path: Path to the 3D model to edit (default: None, which creates a new model)
            edit_mode: Editing mode ('sculpt', 'edit', or 'texture') (default: 'sculpt')
            output_path: Path to save the edited model (default: None, which uses the input path)
            auto_save: Whether to enable auto-save (default: True)
            
        Returns:
            subprocess.Popen: The Blender process
        """
        # Set default output path if not provided
        if model_path and not output_path:
            output_path = model_path
        elif not output_path:
            output_path = os.path.join('output', '3d_models', f"edited_model_{int(time.time())}.glb")
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Prepare parameters
        params = {
            'model_path': model_path or '',
            'edit_mode': edit_mode,
            'output_path': output_path,
            'auto_save': auto_save,
            'auto_save_interval': 300  # 5 minutes
        }
        
        # Run Blender with the direct editing script
        script_path = os.path.join(self.scripts_dir, 'direct_edit.py')
        cmd = [
            self.blender_path,
            '--python', script_path,
            '--',
            json.dumps(params)
        ]
        
        try:
            process = subprocess.Popen(cmd)
            print(f"Blender opened for direct editing.")
            print(f"Model: {model_path or 'New Model'}")
            print(f"Edit Mode: {edit_mode}")
            print(f"Output Path: {output_path}")
            return process
        except Exception as e:
            print(f"Error opening Blender: {e}")
            raise
    
    def create_new_model(self, output_path=None, edit_mode='sculpt'):
        """
        Create a new 3D model in Blender.
        
        Args:
            output_path: Path to save the new model (default: None, which generates a path)
            edit_mode: Editing mode ('sculpt', 'edit', or 'texture') (default: 'sculpt')
            
        Returns:
            subprocess.Popen: The Blender process
        """
        return self.open_for_editing(
            model_path=None,
            edit_mode=edit_mode,
            output_path=output_path,
            auto_save=True
        )
    
    def edit_existing_model(self, model_path, edit_mode='sculpt', output_path=None):
        """
        Edit an existing 3D model in Blender.
        
        Args:
            model_path: Path to the 3D model to edit
            edit_mode: Editing mode ('sculpt', 'edit', or 'texture') (default: 'sculpt')
            output_path: Path to save the edited model (default: None, which uses the input path)
            
        Returns:
            subprocess.Popen: The Blender process
        """
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        return self.open_for_editing(
            model_path=model_path,
            edit_mode=edit_mode,
            output_path=output_path,
            auto_save=True
        )

# Example usage
if __name__ == "__main__":
    editor = BlenderDirectEditor()
    
    # Create a new model
    # editor.create_new_model()
    
    # Edit an existing model
    # editor.edit_existing_model("path/to/model.glb")
