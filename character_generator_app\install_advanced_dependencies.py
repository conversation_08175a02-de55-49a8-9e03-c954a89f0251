#!/usr/bin/env python3
"""
Install advanced dependencies for professional character generation
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        print(f"📦 Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def install_advanced_dependencies():
    """Install all advanced dependencies for realistic character generation"""
    print("🚀 Installing Advanced Character Generation Dependencies")
    print("=" * 55)
    
    # Core 3D and AI packages
    packages = [
        "mediapipe",           # Advanced face mesh detection
        "open3d",              # 3D mesh processing
        "face-recognition",    # Advanced face analysis
        "dlib",                # Facial landmark detection
        "trimesh",             # 3D mesh manipulation
        "pymeshlab",           # Professional mesh processing
        "scikit-image",        # Advanced image processing
        "scipy",               # Scientific computing
        "matplotlib",          # Visualization
        "plotly",              # 3D visualization
        "moderngl",            # GPU-accelerated rendering
        "pyrr",                # 3D math utilities
        "imageio",             # Image I/O
        "tqdm",                # Progress bars
        "requests-toolbelt",   # Advanced HTTP requests
        "Pillow>=9.0.0",       # Enhanced image processing
        "numpy>=1.21.0",       # Updated numpy
        "opencv-python>=4.5.0" # Updated OpenCV
    ]
    
    # Optional advanced packages
    advanced_packages = [
        "torch",               # PyTorch for neural networks
        "torchvision",         # Computer vision models
        "transformers",        # Hugging Face transformers
        "diffusers",           # Stable Diffusion models
        "accelerate",          # Model acceleration
        "xformers",            # Memory efficient transformers
    ]
    
    success_count = 0
    total_packages = len(packages)
    
    print(f"\n📋 Installing {total_packages} core packages...")
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 Core Installation Results:")
    print(f"✅ Successfully installed: {success_count}/{total_packages}")
    
    if success_count == total_packages:
        print("🎉 All core packages installed successfully!")
        
        # Try to install advanced packages
        print(f"\n🔬 Installing {len(advanced_packages)} advanced AI packages...")
        print("(These may take longer and require more disk space)")
        
        advanced_success = 0
        for package in advanced_packages:
            if install_package(package):
                advanced_success += 1
        
        print(f"\n📊 Advanced Installation Results:")
        print(f"✅ Successfully installed: {advanced_success}/{len(advanced_packages)}")
        
        return True
    else:
        print("⚠️ Some core packages failed to install")
        return False

def verify_installations():
    """Verify that key packages are working"""
    print("\n🔍 Verifying Installations...")
    
    tests = [
        ("mediapipe", "import mediapipe as mp; print(f'MediaPipe: {mp.__version__}')"),
        ("open3d", "import open3d as o3d; print(f'Open3D: {o3d.__version__}')"),
        ("face_recognition", "import face_recognition; print('Face Recognition: OK')"),
        ("trimesh", "import trimesh; print(f'Trimesh: {trimesh.__version__}')"),
        ("cv2", "import cv2; print(f'OpenCV: {cv2.__version__}')"),
        ("PIL", "from PIL import Image; print(f'Pillow: {Image.__version__}')"),
        ("numpy", "import numpy as np; print(f'NumPy: {np.__version__}')"),
    ]
    
    working_packages = 0
    
    for package_name, test_code in tests:
        try:
            exec(test_code)
            print(f"✅ {package_name}: Working")
            working_packages += 1
        except Exception as e:
            print(f"❌ {package_name}: {e}")
    
    print(f"\n📊 Verification Results: {working_packages}/{len(tests)} packages working")
    
    return working_packages == len(tests)

def setup_blender_integration():
    """Setup Blender integration"""
    print("\n🎨 Setting up Blender Integration...")
    
    # Check if Blender is installed
    blender_paths = [
        "C:\\Program Files\\Blender Foundation\\Blender 4.0\\blender.exe",
        "C:\\Program Files\\Blender Foundation\\Blender 3.6\\blender.exe",
        "C:\\Program Files\\Blender Foundation\\Blender 3.5\\blender.exe",
        "/usr/bin/blender",
        "/Applications/Blender.app/Contents/MacOS/Blender"
    ]
    
    blender_found = False
    for path in blender_paths:
        if os.path.exists(path):
            print(f"✅ Found Blender at: {path}")
            blender_found = True
            
            # Save Blender path to config
            with open("blender_config.txt", "w") as f:
                f.write(path)
            break
    
    if not blender_found:
        print("⚠️ Blender not found in standard locations")
        print("📥 Please install Blender from: https://www.blender.org/download/")
        print("🔧 Or specify custom path in blender_config.txt")
    
    # Try to install bpy (Blender as Python module)
    try:
        print("📦 Attempting to install Blender as Python module...")
        install_package("bpy")
        print("✅ Blender Python module installed")
        return True
    except:
        print("⚠️ Blender Python module installation failed")
        print("💡 Will use external Blender executable instead")
        return blender_found

def create_advanced_config():
    """Create configuration for advanced features"""
    print("\n⚙️ Creating Advanced Configuration...")
    
    config = {
        "face_detection": {
            "use_mediapipe": True,
            "use_dlib": True,
            "confidence_threshold": 0.7,
            "max_faces": 1
        },
        "mesh_generation": {
            "use_blender": True,
            "subdivision_levels": 2,
            "smooth_iterations": 3,
            "target_vertices": 10000
        },
        "texture_generation": {
            "resolution": 1024,
            "use_normal_maps": True,
            "use_displacement": True,
            "skin_subsurface": True
        },
        "ai_enhancement": {
            "use_stable_diffusion": True,
            "use_controlnet": True,
            "upscale_factor": 2,
            "style_transfer": True
        },
        "mickmumpitz_workflows": {
            "enabled": True,
            "character_sheet_workflow": "character_sheet.json",
            "3d_rendering_workflow": "3d_rendering_image.json",
            "flux_lora_workflow": "flux_lora.json"
        }
    }
    
    import json
    with open("advanced_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✅ Advanced configuration created: advanced_config.json")

def main():
    """Main installation function"""
    print("🚀 Advanced Character Generation System Setup")
    print("=" * 45)
    
    # Install dependencies
    deps_ok = install_advanced_dependencies()
    
    # Verify installations
    verify_ok = verify_installations()
    
    # Setup Blender
    blender_ok = setup_blender_integration()
    
    # Create config
    create_advanced_config()
    
    # Summary
    print(f"\n📋 Setup Summary")
    print("=" * 20)
    print(f"Dependencies: {'✅' if deps_ok else '❌'}")
    print(f"Verification: {'✅' if verify_ok else '❌'}")
    print(f"Blender Setup: {'✅' if blender_ok else '⚠️'}")
    
    if deps_ok and verify_ok:
        print(f"\n🎉 Advanced Character Generation System Ready!")
        print(f"🎯 Next Steps:")
        print(f"1. Run: python test_advanced_features.py")
        print(f"2. Test MediaPipe face detection")
        print(f"3. Generate high-quality characters")
        print(f"4. Use mickmumpitz workflows")
    else:
        print(f"\n⚠️ Some components need attention")
        print(f"💡 Check error messages above and retry")
    
    input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
