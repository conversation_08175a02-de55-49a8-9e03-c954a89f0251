import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';
import { toast } from 'react-toastify';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const GeneratorContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const Title = styled.h1`
  text-align: center;
  color: white;
  margin-bottom: 2rem;
  font-size: 2.5rem;
  font-weight: 700;
`;

const Subtitle = styled.p`
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 3rem;
  font-size: 1.1rem;
`;

const FormCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const DropzoneContainer = styled.div`
  border: 2px dashed ${props => props.isDragActive ? '#28a745' : '#667eea'};
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: ${props => props.isDragActive ? 'rgba(40, 167, 69, 0.1)' : 'rgba(102, 126, 234, 0.05)'};
  margin-bottom: 1.5rem;
  
  &:hover {
    border-color: #764ba2;
    background: rgba(102, 126, 234, 0.1);
  }
`;

const PreviewImage = styled.img`
  max-width: 100%;
  max-height: 200px;
  border-radius: 10px;
  margin: 1rem 0;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  resize: vertical;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const StyleOptions = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
`;

const StyleOption = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  
  input {
    margin: 0;
  }
`;

const GenerateButton = styled.button`
  width: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const ProgressCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  margin: 1rem 0;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  border-radius: 10px;
  width: ${props => props.progress}%;
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  background-color: ${props => {
    switch (props.status) {
      case 'queued': return '#ffc107';
      case 'processing': return '#17a2b8';
      case 'completed': return '#28a745';
      case 'error': return '#dc3545';
      default: return '#6c757d';
    }
  }};
  color: ${props => props.status === 'queued' ? '#212529' : 'white'};
`;

const CharacterGenerator = ({ onJobCreated, currentJob }) => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [textPrompt, setTextPrompt] = useState('');
  const [styleOptions, setStyleOptions] = useState({
    realistic: false,
    cartoon: true,
    anime: false,
    lowPoly: false
  });
  const [isGenerating, setIsGenerating] = useState(false);

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await axios.post('/api/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        setUploadedFile({
          file,
          preview: URL.createObjectURL(file),
          path: response.data.processed_path
        });

        toast.success('Image uploaded successfully!');
      } catch (error) {
        console.error('Upload error:', error);
        toast.error('Failed to upload image');
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp']
    },
    multiple: false
  });

  const handleStyleChange = (style) => {
    setStyleOptions(prev => ({
      ...prev,
      [style]: !prev[style]
    }));
  };

  const handleGenerate = async () => {
    if (!textPrompt && !uploadedFile) {
      toast.error('Please provide either a text prompt or upload an image');
      return;
    }

    setIsGenerating(true);

    try {
      const response = await axios.post('/api/generate', {
        text_prompt: textPrompt,
        image_path: uploadedFile?.path || '',
        style_options: styleOptions
      });

      onJobCreated(response.data);
      toast.success('Character generation started!');
    } catch (error) {
      console.error('Generation error:', error);
      toast.error('Failed to start character generation');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <GeneratorContainer>
      <Title>Create Your 3D Character</Title>
      <Subtitle>
        Upload an image or describe your character to generate a high-quality 3D model
      </Subtitle>

      <FormCard
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="form-group">
          <label className="form-label">Upload Reference Image (Optional)</label>
          <DropzoneContainer {...getRootProps()} isDragActive={isDragActive}>
            <input {...getInputProps()} />
            {uploadedFile ? (
              <div>
                <PreviewImage src={uploadedFile.preview} alt="Preview" />
                <p>Click or drag to replace image</p>
              </div>
            ) : (
              <div>
                <p>Drag & drop an image here, or click to select</p>
                <p style={{ fontSize: '0.9rem', color: '#666' }}>
                  Supports PNG, JPG, JPEG, GIF, BMP
                </p>
              </div>
            )}
          </DropzoneContainer>
        </div>

        <div className="form-group">
          <label className="form-label">Character Description</label>
          <TextArea
            value={textPrompt}
            onChange={(e) => setTextPrompt(e.target.value)}
            placeholder="Describe your character... (e.g., 'A brave knight with silver armor and a red cape')"
          />
        </div>

        <div className="form-group">
          <label className="form-label">Style Options</label>
          <StyleOptions>
            <StyleOption>
              <input
                type="checkbox"
                checked={styleOptions.realistic}
                onChange={() => handleStyleChange('realistic')}
              />
              Realistic
            </StyleOption>
            <StyleOption>
              <input
                type="checkbox"
                checked={styleOptions.cartoon}
                onChange={() => handleStyleChange('cartoon')}
              />
              Cartoon
            </StyleOption>
            <StyleOption>
              <input
                type="checkbox"
                checked={styleOptions.anime}
                onChange={() => handleStyleChange('anime')}
              />
              Anime
            </StyleOption>
            <StyleOption>
              <input
                type="checkbox"
                checked={styleOptions.lowPoly}
                onChange={() => handleStyleChange('lowPoly')}
              />
              Low Poly
            </StyleOption>
          </StyleOptions>
        </div>

        <GenerateButton
          onClick={handleGenerate}
          disabled={isGenerating || (!textPrompt && !uploadedFile)}
        >
          {isGenerating ? (
            <>
              <span className="loading-spinner"></span>
              Generating...
            </>
          ) : (
            'Generate 3D Character'
          )}
        </GenerateButton>
      </FormCard>

      {currentJob && (
        <ProgressCard
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h3>Generation Progress</h3>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <span>Job ID: {currentJob.id?.slice(0, 8) || currentJob.job_id?.slice(0, 8)}</span>
            <StatusBadge status={currentJob.status}>
              {currentJob.status}
            </StatusBadge>
          </div>
          
          <ProgressBar>
            <ProgressFill progress={currentJob.progress || 0} />
          </ProgressBar>
          
          <p style={{ margin: 0, fontSize: '0.9rem', color: '#666' }}>
            {currentJob.progress || 0}% complete
          </p>
          
          {currentJob.status === 'completed' && (
            <div style={{ marginTop: '1rem' }}>
              <p style={{ color: '#28a745', fontWeight: '600' }}>
                ✅ Character generation completed! 
              </p>
              <p>
                <a href="/viewer" style={{ color: '#667eea' }}>
                  View your 3D character →
                </a>
              </p>
            </div>
          )}
          
          {currentJob.status === 'error' && (
            <div style={{ marginTop: '1rem' }}>
              <p style={{ color: '#dc3545', fontWeight: '600' }}>
                ❌ Error: {currentJob.error}
              </p>
            </div>
          )}
        </ProgressCard>
      )}
    </GeneratorContainer>
  );
};

export default CharacterGenerator;
