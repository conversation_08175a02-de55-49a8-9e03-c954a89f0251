#!/usr/bin/env python3
"""
Fix ComfyUI CUDA PyTorch installation
"""

import subprocess
import sys
import os

def fix_comfyui_pytorch():
    """Install CUDA-enabled PyTorch for ComfyUI"""
    
    print("🔧 Fixing ComfyUI CUDA PyTorch Installation")
    print("=" * 45)
    
    # Path to ComfyUI's embedded Python
    comfyui_python = r"C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)\ComfyUI_windows_portable\python_embeded\python.exe"
    
    if not os.path.exists(comfyui_python):
        print(f"❌ ComfyUI Python not found at: {comfyui_python}")
        return False
    
    print(f"✅ Found ComfyUI Python: {comfyui_python}")
    
    # Check current PyTorch version
    print("\n🔍 Checking current PyTorch installation...")
    try:
        result = subprocess.run([
            comfyui_python, "-c", 
            "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}')"
        ], capture_output=True, text=True, timeout=30)
        
        print("Current status:")
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
            
    except Exception as e:
        print(f"⚠️ Could not check current PyTorch: {e}")
    
    # Uninstall current PyTorch
    print("\n🗑️ Uninstalling current PyTorch...")
    packages_to_remove = [
        "torch", "torchvision", "torchaudio"
    ]
    
    for package in packages_to_remove:
        try:
            print(f"Removing {package}...")
            result = subprocess.run([
                comfyui_python, "-m", "pip", "uninstall", package, "-y"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"✅ {package} removed")
            else:
                print(f"⚠️ {package} removal had issues (might not be installed)")
                
        except Exception as e:
            print(f"⚠️ Error removing {package}: {e}")
    
    # Install CUDA-enabled PyTorch
    print("\n📦 Installing CUDA-enabled PyTorch...")
    print("This may take several minutes...")
    
    # PyTorch with CUDA 12.1 (compatible with RTX 4060)
    pytorch_install_cmd = [
        comfyui_python, "-m", "pip", "install", 
        "torch", "torchvision", "torchaudio", 
        "--index-url", "https://download.pytorch.org/whl/cu121"
    ]
    
    try:
        result = subprocess.run(
            pytorch_install_cmd, 
            capture_output=True, 
            text=True, 
            timeout=600  # 10 minutes
        )
        
        if result.returncode == 0:
            print("✅ CUDA PyTorch installed successfully!")
        else:
            print("❌ PyTorch installation failed:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ PyTorch installation timed out (but may still be running)")
        print("Please wait for it to complete...")
        return False
    except Exception as e:
        print(f"❌ Error installing PyTorch: {e}")
        return False
    
    # Verify installation
    print("\n🧪 Verifying CUDA PyTorch installation...")
    try:
        result = subprocess.run([
            comfyui_python, "-c", 
            "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}'); print(f'GPU count: {torch.cuda.device_count()}')"
        ], capture_output=True, text=True, timeout=30)
        
        print("Verification results:")
        print(result.stdout)
        
        if "CUDA available: True" in result.stdout:
            print("🎉 CUDA PyTorch is working correctly!")
            return True
        else:
            print("❌ CUDA is still not available")
            return False
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def check_nvidia_driver():
    """Check NVIDIA driver installation"""
    print("\n🔍 Checking NVIDIA driver...")
    
    try:
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ NVIDIA driver is installed:")
            # Extract GPU info
            lines = result.stdout.split('\n')
            for line in lines:
                if "RTX" in line or "GeForce" in line:
                    print(f"   GPU: {line.strip()}")
                    break
            return True
        else:
            print("❌ NVIDIA driver not found or not working")
            return False
            
    except Exception as e:
        print(f"❌ Could not check NVIDIA driver: {e}")
        return False

def main():
    print("🎮 ComfyUI CUDA Fix Tool")
    print("=" * 25)
    
    # Check NVIDIA driver first
    driver_ok = check_nvidia_driver()
    
    if not driver_ok:
        print("\n⚠️ NVIDIA driver issue detected!")
        print("Please install/update your NVIDIA drivers first:")
        print("https://www.nvidia.com/Download/index.aspx")
        input("Press Enter to continue anyway...")
    
    # Fix PyTorch
    success = fix_comfyui_pytorch()
    
    if success:
        print("\n🎉 ComfyUI CUDA fix completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Restart ComfyUI")
        print("2. Check that it starts without CUDA errors")
        print("3. Verify 3D Pack nodes are available")
    else:
        print("\n⚠️ Fix completed with some issues")
        print("ComfyUI may still work in CPU mode")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
