"""
Hugging Face API Client for 3D Character Generation
Handles AI-powered image generation, 3D generation, and text processing
"""

import os
import requests
import json
import base64
from typing import Dict, Any, Optional, List
from PIL import Image
import io

class HuggingFaceClient:
    """Client for Hugging Face API services."""
    
    def __init__(self, api_token: Optional[str] = None):
        self.api_token = api_token or os.getenv('HUGGINGFACE_API_TOKEN')
        self.base_url = "https://api-inference.huggingface.co/models"
        self.headers = {
            "Authorization": f"Bearer {self.api_token}" if self.api_token else "",
            "Content-Type": "application/json"
        }
    
    def set_api_token(self, token: str):
        """Set the API token."""
        self.api_token = token
        self.headers["Authorization"] = f"Bearer {token}"
    
    def text_to_image(self, prompt: str, model: str = "stabilityai/stable-diffusion-xl-base-1.0") -> Optional[bytes]:
        """Generate image from text prompt."""
        try:
            url = f"{self.base_url}/{model}"
            
            payload = {
                "inputs": prompt,
                "parameters": {
                    "num_inference_steps": 30,
                    "guidance_scale": 7.5,
                    "width": 512,
                    "height": 512
                }
            }
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=60)
            
            if response.status_code == 200:
                return response.content
            else:
                print(f"Text-to-image failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Error in text-to-image: {e}")
            return None
    
    def image_to_3d(self, image_bytes: bytes, model: str = "stabilityai/stable-fast-3d") -> Optional[bytes]:
        """Generate 3D model from image (if available)."""
        try:
            # Note: This is a placeholder as Stable Fast 3D might not be available via API
            # We'll implement this when the model becomes available
            print("Image-to-3D generation not yet available via Hugging Face API")
            return None
            
        except Exception as e:
            print(f"Error in image-to-3D: {e}")
            return None
    
    def enhance_prompt(self, prompt: str, model: str = "microsoft/DialoGPT-medium") -> str:
        """Enhance a character description prompt."""
        try:
            url = f"{self.base_url}/{model}"
            
            enhancement_prompt = f"Enhance this character description for 3D generation: {prompt}"
            
            payload = {
                "inputs": enhancement_prompt,
                "parameters": {
                    "max_length": 200,
                    "temperature": 0.7,
                    "do_sample": True
                }
            }
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    return result[0].get('generated_text', prompt)
                return prompt
            else:
                print(f"Prompt enhancement failed: {response.status_code}")
                return prompt
                
        except Exception as e:
            print(f"Error enhancing prompt: {e}")
            return prompt
    
    def analyze_image(self, image_bytes: bytes, model: str = "Salesforce/blip-image-captioning-base") -> str:
        """Analyze an image and generate a description."""
        try:
            url = f"{self.base_url}/{model}"
            
            # Convert image bytes to base64
            image_b64 = base64.b64encode(image_bytes).decode()
            
            payload = {
                "inputs": image_b64
            }
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    return result[0].get('generated_text', 'A character')
                return 'A character'
            else:
                print(f"Image analysis failed: {response.status_code}")
                return 'A character'
                
        except Exception as e:
            print(f"Error analyzing image: {e}")
            return 'A character'
    
    def generate_character_variations(self, base_prompt: str, count: int = 3) -> List[str]:
        """Generate variations of a character prompt."""
        try:
            variations = []
            
            style_modifiers = [
                "in a cartoon style",
                "in a realistic style", 
                "in an anime style",
                "in a low-poly style",
                "with vibrant colors",
                "with detailed textures"
            ]
            
            for i in range(min(count, len(style_modifiers))):
                variation = f"{base_prompt} {style_modifiers[i]}"
                variations.append(variation)
            
            return variations
            
        except Exception as e:
            print(f"Error generating variations: {e}")
            return [base_prompt]
    
    def classify_character_type(self, prompt: str, model: str = "facebook/bart-large-mnli") -> Dict[str, float]:
        """Classify the type of character from the prompt."""
        try:
            url = f"{self.base_url}/{model}"
            
            character_types = [
                "human character",
                "robot character", 
                "alien character",
                "fantasy character",
                "animal character",
                "cartoon character"
            ]
            
            payload = {
                "inputs": prompt,
                "parameters": {
                    "candidate_labels": character_types
                }
            }
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                # Convert to dictionary
                classification = {}
                labels = result.get('labels', [])
                scores = result.get('scores', [])
                
                for label, score in zip(labels, scores):
                    classification[label] = score
                
                return classification
            else:
                print(f"Character classification failed: {response.status_code}")
                return {"human character": 1.0}
                
        except Exception as e:
            print(f"Error classifying character: {e}")
            return {"human character": 1.0}
    
    def test_connection(self) -> bool:
        """Test connection to Hugging Face API."""
        try:
            # Test with a simple model
            url = f"{self.base_url}/gpt2"
            
            payload = {
                "inputs": "Hello world",
                "parameters": {"max_length": 10}
            }
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=10)
            
            if response.status_code == 200:
                print("✅ Hugging Face API connection successful")
                return True
            elif response.status_code == 401:
                print("❌ Hugging Face API authentication failed - check your API token")
                return False
            else:
                print(f"❌ Hugging Face API connection failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Hugging Face API connection test failed: {e}")
            return False

def test_huggingface_connection(api_token: Optional[str] = None):
    """Test connection to Hugging Face API."""
    client = HuggingFaceClient(api_token)
    return client.test_connection()

# Available 3D Generation Models (for reference)
AVAILABLE_3D_MODELS = {
    "stable_fast_3d": {
        "name": "Stable Fast 3D",
        "description": "Fast 3D generation from single images",
        "input": "image",
        "output": "3d_model",
        "available": False  # Not yet available via API
    },
    "instant_mesh": {
        "name": "InstantMesh", 
        "description": "High-quality 3D mesh generation",
        "input": "image",
        "output": "3d_mesh",
        "available": False  # Not yet available via API
    },
    "hunyuan3d": {
        "name": "Hunyuan3D",
        "description": "Text and image to 3D generation",
        "input": "text_or_image", 
        "output": "3d_model",
        "available": False  # Not yet available via API
    }
}

if __name__ == "__main__":
    # Test the connection
    test_huggingface_connection()
