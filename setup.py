"""
Setup script for the AI Content Generation Pipeline.
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        sys.exit(1)
    print(f"Python version: {sys.version}")

def check_dependencies():
    """Check if required external tools are installed."""
    dependencies = {
        'blender': os.environ.get('BLENDER_PATH', 'blender'),
        'unity': os.environ.get('UNITY_PATH', 'Unity.exe'),
        'unreal': os.environ.get('UNREAL_PATH', 'UE4Editor.exe')
    }
    
    for name, path in dependencies.items():
        try:
            if name == 'blender':
                # Check Blender version
                result = subprocess.run([path, '--version'], capture_output=True, text=True)
                print(f"Blender: {result.stdout.strip() if result.stdout else 'Not found'}")
            elif name == 'unity':
                # Unity doesn't have a simple version check command
                print(f"Unity: {'Found at ' + path if os.path.exists(path) else 'Not found'}")
            elif name == 'unreal':
                # Unreal doesn't have a simple version check command
                print(f"Unreal Engine: {'Found at ' + path if os.path.exists(path) else 'Not found'}")
        except Exception as e:
            print(f"{name}: Not found ({e})")

def setup_environment():
    """Set up the environment."""
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        shutil.copy('.env.example', '.env')
        print("Created .env file from .env.example. Please edit it with your API keys and paths.")
    
    # Create output directories
    os.makedirs('output/images', exist_ok=True)
    os.makedirs('output/3d_models', exist_ok=True)
    os.makedirs('output/videos', exist_ok=True)
    os.makedirs('output/games', exist_ok=True)
    print("Created output directories.")

def install_python_dependencies():
    """Install Python dependencies."""
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)
        print("Installed Python dependencies.")
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        sys.exit(1)

def setup_comfyui():
    """Set up ComfyUI."""
    if not os.path.exists('external/ComfyUI'):
        os.makedirs('external', exist_ok=True)
        try:
            print("Cloning ComfyUI repository...")
            subprocess.run(['git', 'clone', 'https://github.com/comfyanonymous/ComfyUI.git', 'external/ComfyUI'], check=True)
            
            # Install ComfyUI dependencies
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'external/ComfyUI/requirements.txt'], check=True)
            print("ComfyUI setup complete.")
        except subprocess.CalledProcessError as e:
            print(f"Error setting up ComfyUI: {e}")
    else:
        print("ComfyUI already exists.")

def create_unity_project():
    """Create a Unity project if needed."""
    unity_path = os.environ.get('UNITY_PATH')
    unity_project_path = os.environ.get('UNITY_PROJECT_PATH')
    
    if not unity_path or not unity_project_path:
        print("Unity path or project path not set in .env file. Skipping Unity project creation.")
        return
    
    if not os.path.exists(unity_project_path):
        try:
            print(f"Creating Unity project at {unity_project_path}...")
            os.makedirs(os.path.dirname(unity_project_path), exist_ok=True)
            
            # Create Unity project
            subprocess.run([
                unity_path,
                '-createProject', unity_project_path,
                '-quit'
            ], check=True)
            
            print("Unity project created.")
        except subprocess.CalledProcessError as e:
            print(f"Error creating Unity project: {e}")
    else:
        print("Unity project already exists.")

def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description='Setup script for AI Content Generation Pipeline')
    parser.add_argument('--skip-comfyui', action='store_true', help='Skip ComfyUI setup')
    parser.add_argument('--skip-unity', action='store_true', help='Skip Unity project creation')
    args = parser.parse_args()
    
    print("Setting up AI Content Generation Pipeline...")
    
    # Check Python version
    check_python_version()
    
    # Install Python dependencies
    install_python_dependencies()
    
    # Set up environment
    setup_environment()
    
    # Check external dependencies
    check_dependencies()
    
    # Set up ComfyUI
    if not args.skip_comfyui:
        setup_comfyui()
    
    # Create Unity project
    if not args.skip_unity:
        create_unity_project()
    
    print("\nSetup complete! You can now run the application with:")
    print("  python src/app.py")

if __name__ == '__main__':
    main()
