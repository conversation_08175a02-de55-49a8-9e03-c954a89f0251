"""
ComfyUI Workflow Templates for 3D Character Generation
Contains pre-built workflows for different types of 3D generation
"""

def get_text_to_3d_workflow_enhanced(text_prompt: str, style: str = "realistic") -> dict:
    """
    Get a workflow for text-to-3D generation.
    This assumes you have 3D generation nodes installed in ComfyUI.
    """

    workflow = {
        "1": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": text_prompt,
                "clip": ["4", 1]
            }
        },
        "2": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": "low quality, blurry, distorted, bad anatomy",
                "clip": ["4", 1]
            }
        },
        "3": {
            "class_type": "KSampler",
            "inputs": {
                "seed": 42,
                "steps": 30,
                "cfg": 8.0,
                "sampler_name": "euler_ancestral",
                "scheduler": "karras",
                "denoise": 1.0,
                "model": ["4", 0],
                "positive": ["1", 0],
                "negative": ["2", 0],
                "latent_image": ["5", 0]
            }
        },
        "4": {
            "class_type": "CheckpointLoaderSimple",
            "inputs": {
                "ckpt_name": "sd_xl_base_1.0.safetensors"
            }
        },
        "5": {
            "class_type": "EmptyLatentImage",
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            }
        },
        "6": {
            "class_type": "VAEDecode",
            "inputs": {
                "samples": ["3", 0],
                "vae": ["4", 2]
            }
        },
        "7": {
            "class_type": "SaveImage",
            "inputs": {
                "filename_prefix": "character_2d",
                "images": ["6", 0]
            }
        }
    }

    # Add 3D generation nodes if available
    # Note: These would need to be installed as custom nodes
    if style == "3d":
        workflow.update({
            "8": {
                "class_type": "StableFast3D",  # Hypothetical node
                "inputs": {
                    "image": ["6", 0],
                    "steps": 20,
                    "guidance_scale": 3.0
                }
            },
            "9": {
                "class_type": "Save3DModel",  # Hypothetical node
                "inputs": {
                    "model": ["8", 0],
                    "filename_prefix": "character_3d"
                }
            }
        })

    return workflow

def get_text_to_3d_workflow(text_prompt: str, style: str = "realistic") -> dict:
    """Fallback workflow for compatibility."""
    return get_text_to_3d_workflow_enhanced(text_prompt, style)

def get_stable_fast_3d_workflow(image_filename: str) -> dict:
    """
    Enhanced workflow using Stable Fast 3D from ComfyUI-3D-Pack.
    """

    workflow = {
        "1": {
            "class_type": "LoadImage",
            "inputs": {
                "image": image_filename
            }
        },
        "2": {
            "class_type": "ImageResize",
            "inputs": {
                "image": ["1", 0],
                "width": 512,
                "height": 512,
                "interpolation": "lanczos"
            }
        },
        "3": {
            "class_type": "StableFast3D",
            "inputs": {
                "image": ["2", 0],
                "foreground_ratio": 0.85,
                "force_cuda_malloc": False,
                "texture_resolution": 1024
            }
        },
        "4": {
            "class_type": "Save3D",
            "inputs": {
                "mesh": ["3", 0],
                "filename_prefix": "sf3d_character",
                "format": "glb"
            }
        }
    }

    return workflow

def get_instant_mesh_workflow(image_filename: str) -> dict:
    """
    Enhanced workflow using InstantMesh from ComfyUI-3D-Pack.
    """

    workflow = {
        "1": {
            "class_type": "LoadImage",
            "inputs": {
                "image": image_filename
            }
        },
        "2": {
            "class_type": "ImageResize",
            "inputs": {
                "image": ["1", 0],
                "width": 320,
                "height": 320,
                "interpolation": "lanczos"
            }
        },
        "3": {
            "class_type": "InstantMesh",
            "inputs": {
                "image": ["2", 0],
                "sample_steps": 75,
                "sample_seed": 42
            }
        },
        "4": {
            "class_type": "Save3D",
            "inputs": {
                "mesh": ["3", 0],
                "filename_prefix": "instant_mesh_character",
                "format": "glb"
            }
        }
    }

    return workflow

def get_image_to_3d_workflow(image_filename: str) -> dict:
    """
    Get a workflow for image-to-3D generation.
    """

    workflow = {
        "1": {
            "class_type": "LoadImage",
            "inputs": {
                "image": image_filename
            }
        },
        "2": {
            "class_type": "ImageResize",
            "inputs": {
                "image": ["1", 0],
                "width": 512,
                "height": 512,
                "interpolation": "lanczos"
            }
        }
    }

    # Add 3D generation nodes (these would be custom nodes)
    # Note: You would need to install ComfyUI-3D-Pack or similar
    workflow.update({
        "3": {
            "class_type": "StableFast3D",  # From ComfyUI-3D-Pack
            "inputs": {
                "image": ["2", 0],
                "steps": 20,
                "guidance_scale": 3.0,
                "seed": 42
            }
        },
        "4": {
            "class_type": "Save3DModel",
            "inputs": {
                "model": ["3", 0],
                "filename_prefix": "character_from_image",
                "format": "glb"
            }
        }
    })

    return workflow

def get_character_enhancement_workflow(image_filename: str, text_prompt: str) -> dict:
    """
    Get a workflow that enhances a character image and generates 3D.
    """

    workflow = {
        "1": {
            "class_type": "LoadImage",
            "inputs": {
                "image": image_filename
            }
        },
        "2": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": f"enhance this character: {text_prompt}",
                "clip": ["6", 1]
            }
        },
        "3": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": "low quality, blurry, distorted",
                "clip": ["6", 1]
            }
        },
        "4": {
            "class_type": "VAEEncode",
            "inputs": {
                "pixels": ["1", 0],
                "vae": ["6", 2]
            }
        },
        "5": {
            "class_type": "KSampler",
            "inputs": {
                "seed": 42,
                "steps": 20,
                "cfg": 7.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 0.7,  # Partial denoising to enhance
                "model": ["6", 0],
                "positive": ["2", 0],
                "negative": ["3", 0],
                "latent_image": ["4", 0]
            }
        },
        "6": {
            "class_type": "CheckpointLoaderSimple",
            "inputs": {
                "ckpt_name": "sd_xl_base_1.0.safetensors"
            }
        },
        "7": {
            "class_type": "VAEDecode",
            "inputs": {
                "samples": ["5", 0],
                "vae": ["6", 2]
            }
        },
        "8": {
            "class_type": "SaveImage",
            "inputs": {
                "filename_prefix": "enhanced_character",
                "images": ["7", 0]
            }
        }
    }

    # Add 3D generation from enhanced image
    workflow.update({
        "9": {
            "class_type": "StableFast3D",
            "inputs": {
                "image": ["7", 0],
                "steps": 25,
                "guidance_scale": 3.5
            }
        },
        "10": {
            "class_type": "Save3DModel",
            "inputs": {
                "model": ["9", 0],
                "filename_prefix": "enhanced_character_3d",
                "format": "glb"
            }
        }
    })

    return workflow

def get_multi_view_3d_workflow(text_prompt: str) -> dict:
    """
    Get a workflow that generates multiple views and creates 3D.
    """

    base_workflow = {
        "1": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": f"{text_prompt}, front view",
                "clip": ["5", 1]
            }
        },
        "2": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": f"{text_prompt}, side view",
                "clip": ["5", 1]
            }
        },
        "3": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": f"{text_prompt}, back view",
                "clip": ["5", 1]
            }
        },
        "4": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": "low quality, blurry",
                "clip": ["5", 1]
            }
        },
        "5": {
            "class_type": "CheckpointLoaderSimple",
            "inputs": {
                "ckpt_name": "sd_xl_base_1.0.safetensors"
            }
        },
        "6": {
            "class_type": "EmptyLatentImage",
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            }
        }
    }

    # Generate multiple views
    for i, view in enumerate(["front", "side", "back"], 7):
        base_workflow.update({
            str(i): {
                "class_type": "KSampler",
                "inputs": {
                    "seed": 42 + i,
                    "steps": 25,
                    "cfg": 8.0,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 1.0,
                    "model": ["5", 0],
                    "positive": [str(i-6), 0],  # Reference to text encode nodes
                    "negative": ["4", 0],
                    "latent_image": ["6", 0]
                }
            },
            str(i+3): {
                "class_type": "VAEDecode",
                "inputs": {
                    "samples": [str(i), 0],
                    "vae": ["5", 2]
                }
            },
            str(i+6): {
                "class_type": "SaveImage",
                "inputs": {
                    "filename_prefix": f"character_{view}_view",
                    "images": [str(i+3), 0]
                }
            }
        })

    # Multi-view 3D generation (hypothetical node)
    base_workflow.update({
        "16": {
            "class_type": "MultiView3D",
            "inputs": {
                "front_image": ["10", 0],
                "side_image": ["11", 0],
                "back_image": ["12", 0],
                "steps": 30
            }
        },
        "17": {
            "class_type": "Save3DModel",
            "inputs": {
                "model": ["16", 0],
                "filename_prefix": "multiview_character_3d",
                "format": "glb"
            }
        }
    })

    return base_workflow

# Workflow configurations for different character types
CHARACTER_WORKFLOWS = {
    "simple_3d": get_text_to_3d_workflow,
    "image_to_3d": get_image_to_3d_workflow,
    "enhanced_3d": get_character_enhancement_workflow,
    "multiview_3d": get_multi_view_3d_workflow,
    "stable_fast_3d": get_stable_fast_3d_workflow,
    "instant_mesh": get_instant_mesh_workflow
}

def get_workflow_by_type(workflow_type: str, **kwargs) -> dict:
    """Get a workflow by type with parameters."""
    if workflow_type in CHARACTER_WORKFLOWS:
        return CHARACTER_WORKFLOWS[workflow_type](**kwargs)
    else:
        # Default to simple text-to-3D
        return get_text_to_3d_workflow(kwargs.get('text_prompt', 'A character'))

# Required ComfyUI Custom Nodes for 3D Generation
REQUIRED_CUSTOM_NODES = [
    {
        "name": "ComfyUI-3D-Pack",
        "url": "https://github.com/MrForExample/ComfyUI-3D-Pack",
        "description": "3D generation nodes including Stable Fast 3D, InstantMesh"
    },
    {
        "name": "ComfyUI-Manager",
        "url": "https://github.com/ltdrdata/ComfyUI-Manager",
        "description": "Node manager for easy installation"
    }
]
