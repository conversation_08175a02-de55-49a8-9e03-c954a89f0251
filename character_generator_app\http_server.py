#!/usr/bin/env python3
"""
Simple HTTP server for character generation
"""

import http.server
import socketserver
import json
import os
import uuid
import struct
from urllib.parse import urlparse, parse_qs
import threading

PORT = 5000

class CharacterHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(self.get_main_page().encode())
            
        elif parsed_path.path == '/api/status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                'status': 'running',
                'version': '2.0.0',
                'message': 'AI-Powered 3D Character Generator'
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif parsed_path.path.startswith('/api/download/'):
            filename = parsed_path.path.split('/')[-1]
            filepath = os.path.join('backend', 'outputs', 'models', filename)
            
            if os.path.exists(filepath):
                self.send_response(200)
                self.send_header('Content-type', 'application/octet-stream')
                self.send_header('Content-Disposition', f'attachment; filename="{filename}"')
                self.end_headers()
                
                with open(filepath, 'rb') as f:
                    self.wfile.write(f.read())
            else:
                self.send_response(404)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'error': 'File not found'}).encode())
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        """Handle POST requests"""
        if self.path == '/api/generate':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode())
                
                prompt = data.get('prompt', 'A 3D character')
                style = data.get('style', 'realistic')
                
                print(f"🎯 Generating character: {prompt}")
                print(f"   Style: {style}")
                
                # Create simple GLB
                glb_content = self.create_simple_glb(prompt)
                
                # Save file
                job_id = str(uuid.uuid4())[:8]
                filename = f"character_{job_id}.glb"
                
                output_dir = os.path.join('backend', 'outputs', 'models')
                os.makedirs(output_dir, exist_ok=True)
                
                filepath = os.path.join(output_dir, filename)
                with open(filepath, 'wb') as f:
                    f.write(glb_content)
                
                print(f"✅ Generated: {filename}")
                
                response = {
                    'success': True,
                    'job_id': job_id,
                    'filename': filename,
                    'character_type': 'Basic Character',
                    'file_size': len(glb_content),
                    'vertex_count': 8,
                    'face_count': 12,
                    'download_url': f"/api/download/{filename}"
                }
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode())
                
            except Exception as e:
                print(f"❌ Generation error: {e}")
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                response = {'success': False, 'error': str(e)}
                self.wfile.write(json.dumps(response).encode())
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def create_simple_glb(self, prompt):
        """Create a simple GLB file"""
        try:
            # Simple cube vertices
            vertices = [
                -1.0, -1.0, -1.0,  1.0, -1.0, -1.0,  1.0,  1.0, -1.0, -1.0,  1.0, -1.0,
                -1.0, -1.0,  1.0,  1.0, -1.0,  1.0,  1.0,  1.0,  1.0, -1.0,  1.0,  1.0
            ]
            
            indices = [
                0, 1, 2, 0, 2, 3,  4, 7, 6, 4, 6, 5,  0, 4, 5, 0, 5, 1,
                2, 6, 7, 2, 7, 3,  0, 3, 7, 0, 7, 4,  1, 5, 6, 1, 6, 2
            ]
            
            # Create minimal GLTF
            gltf_data = {
                "asset": {"version": "2.0"},
                "scene": 0,
                "scenes": [{"nodes": [0]}],
                "nodes": [{"mesh": 0}],
                "meshes": [{"primitives": [{"attributes": {"POSITION": 0}, "indices": 1}]}],
                "accessors": [
                    {"bufferView": 0, "componentType": 5126, "count": 8, "type": "VEC3"},
                    {"bufferView": 1, "componentType": 5123, "count": 36, "type": "SCALAR"}
                ],
                "bufferViews": [
                    {"buffer": 0, "byteOffset": 0, "byteLength": 96},
                    {"buffer": 0, "byteOffset": 96, "byteLength": 72}
                ],
                "buffers": [{"byteLength": 168}]
            }
            
            # Pack binary data
            binary_data = b''
            for vertex in vertices:
                binary_data += struct.pack('<f', vertex)
            for index in indices:
                binary_data += struct.pack('<H', index)
            
            # Create GLB
            json_data = json.dumps(gltf_data).encode('utf-8')
            json_length = len(json_data)
            json_padding = (4 - (json_length % 4)) % 4
            json_data += b' ' * json_padding
            json_length += json_padding
            
            binary_length = len(binary_data)
            binary_padding = (4 - (binary_length % 4)) % 4
            binary_data += b'\x00' * binary_padding
            binary_length += binary_padding
            
            total_length = 12 + 8 + json_length + 8 + binary_length
            
            glb_content = struct.pack('<III', 0x46546C67, 2, total_length)
            glb_content += struct.pack('<II', json_length, 0x4E4F534A) + json_data
            glb_content += struct.pack('<II', binary_length, 0x004E4942) + binary_data
            
            return glb_content
            
        except Exception as e:
            print(f"GLB creation failed: {e}")
            return b''
    
    def get_main_page(self):
        """Return the main HTML page"""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 AI Character Generator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            display: none;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .download-btn {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            margin-top: 15px;
            padding: 10px 20px;
            font-size: 16px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #ffd700;
        }
        .status {
            background: rgba(40, 167, 69, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 AI-Powered 3D Character Generator</h1>
        
        <div class="status">
            <h3>✅ System Status: ONLINE</h3>
            <p>Server is running and ready to generate characters!</p>
        </div>
        
        <form id="characterForm">
            <div class="form-group">
                <label for="prompt">Character Description:</label>
                <textarea id="prompt" placeholder="A brave medieval knight with silver armor and red cape"></textarea>
            </div>
            
            <div class="form-group">
                <label for="style">Art Style:</label>
                <select id="style">
                    <option value="realistic">Realistic</option>
                    <option value="anime">Anime</option>
                    <option value="cartoon">Cartoon</option>
                    <option value="fantasy">Fantasy</option>
                    <option value="cyberpunk">Cyberpunk</option>
                    <option value="stylized">Stylized</option>
                </select>
            </div>
            
            <button type="submit" id="generateBtn">🚀 Generate Character</button>
        </form>
        
        <div id="result" class="result">
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>Generating your character... This may take a moment.</p>
            </div>
            
            <div id="success" style="display: none;">
                <h3>✅ Character Generated Successfully!</h3>
                <div class="stats">
                    <div class="stat">
                        <div class="stat-value" id="fileSize">-</div>
                        <div>File Size</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value" id="vertices">-</div>
                        <div>Vertices</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value" id="faces">-</div>
                        <div>Faces</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value" id="characterType">-</div>
                        <div>Type</div>
                    </div>
                </div>
                <button id="downloadBtn" class="download-btn">📥 Download GLB File</button>
            </div>
            
            <div id="error" style="display: none;">
                <h3>❌ Generation Failed</h3>
                <p id="errorMessage"></p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('characterForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const prompt = document.getElementById('prompt').value;
            const style = document.getElementById('style').value;
            
            if (!prompt.trim()) {
                alert('Please enter a character description!');
                return;
            }
            
            // Show loading
            document.getElementById('result').style.display = 'block';
            document.getElementById('loading').style.display = 'block';
            document.getElementById('success').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            document.getElementById('generateBtn').disabled = true;
            
            try {
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        style: style
                    })
                });
                
                const result = await response.json();
                
                document.getElementById('loading').style.display = 'none';
                
                if (result.success) {
                    // Show success
                    document.getElementById('success').style.display = 'block';
                    document.getElementById('fileSize').textContent = (result.file_size / 1024).toFixed(1) + ' KB';
                    document.getElementById('vertices').textContent = result.vertex_count || 'N/A';
                    document.getElementById('faces').textContent = result.face_count || 'N/A';
                    document.getElementById('characterType').textContent = result.character_type || 'Character';
                    
                    // Setup download
                    document.getElementById('downloadBtn').onclick = function() {
                        window.open(result.download_url, '_blank');
                    };
                } else {
                    // Show error
                    document.getElementById('error').style.display = 'block';
                    document.getElementById('errorMessage').textContent = result.error || 'Unknown error occurred';
                }
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('errorMessage').textContent = 'Network error: ' + error.message;
            }
            
            document.getElementById('generateBtn').disabled = false;
        });
    </script>
</body>
</html>
        """

def start_server():
    """Start the HTTP server"""
    print("🚀 STARTING AI-POWERED 3D CHARACTER GENERATOR")
    print("=" * 50)
    print("🌐 Web Interface: http://localhost:5000")
    print("📱 API Status: http://localhost:5000/api/status")
    print("🎭 Ready to generate characters!")
    print()
    
    # Ensure output directory exists
    os.makedirs('backend/outputs/models', exist_ok=True)
    
    try:
        with socketserver.TCPServer(("", PORT), CharacterHandler) as httpd:
            print(f"✅ Server started successfully on port {PORT}")
            print("🔗 Open http://localhost:5000 in your browser")
            print("⚠️  Press Ctrl+C to stop the server")
            print()
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        input("Press Enter to exit...")

if __name__ == '__main__':
    start_server()
