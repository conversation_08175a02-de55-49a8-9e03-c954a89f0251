#!/usr/bin/env python3
"""Test core character generation without AI dependencies"""

import sys
import os
import json
sys.path.append('backend')

def test_procedural_generation():
    """Test the procedural character generation functions"""
    print("🧪 Testing Procedural Character Generation")
    print("=" * 40)
    
    try:
        # Import the core functions from app.py
        from app import (
            analyze_character_prompt,
            generate_character_geometry, 
            create_character_glb,
            create_character_materials
        )
        print("✅ Core generation functions imported")
        
        # Test character analysis
        test_prompt = "A blue robot warrior with silver armor"
        character_type = analyze_character_prompt(test_prompt)
        print(f"✅ Character analysis: {character_type['name']}")
        
        # Test geometry generation
        vertices, indices, normals = generate_character_geometry(character_type)
        print(f"✅ Geometry generated: {len(vertices)//3} vertices, {len(indices)//3} triangles")
        
        # Test materials
        materials = create_character_materials(character_type)
        print(f"✅ Materials created: {len(materials)} materials")
        
        # Test full GLB creation
        glb_content = create_character_glb(test_prompt)
        print(f"✅ GLB file created: {len(glb_content)} bytes")
        
        # Save test file
        test_file = "test_procedural_character.glb"
        with open(test_file, 'wb') as f:
            f.write(glb_content)
        
        if os.path.exists(test_file):
            print(f"✅ Test GLB file saved: {test_file}")
            return True
        else:
            print("❌ Failed to save test GLB file")
            return False
            
    except Exception as e:
        print(f"❌ Procedural generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_app_creation():
    """Test Flask app creation without running server"""
    print("\n🌐 Testing Flask App Creation")
    print("=" * 30)
    
    try:
        # Import app without running it
        import app
        print("✅ App module imported")
        
        # Check if Flask app exists
        if hasattr(app, 'app'):
            print("✅ Flask app object exists")
            
            # Check routes
            routes = [rule.rule for rule in app.app.url_map.iter_rules()]
            print(f"✅ Routes found: {len(routes)}")
            for route in routes[:5]:  # Show first 5 routes
                print(f"   - {route}")
            
            return True
        else:
            print("❌ Flask app object not found")
            return False
            
    except Exception as e:
        print(f"❌ Flask app test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Test procedural generation
    gen_ok = test_procedural_generation()
    
    # Test Flask app
    app_ok = test_flask_app_creation()
    
    print(f"\n📊 Core Test Results:")
    print("=" * 25)
    print(f"Procedural generation: {'✅' if gen_ok else '❌'}")
    print(f"Flask app creation: {'✅' if app_ok else '❌'}")
    
    if gen_ok and app_ok:
        print("\n🎉 Core functionality is working!")
    else:
        print("\n⚠️ Some core tests failed")
