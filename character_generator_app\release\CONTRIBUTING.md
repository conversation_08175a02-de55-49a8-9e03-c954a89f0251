# Contributing to AI-Powered 3D Character Generator

Thank you for your interest in contributing to this project! We welcome contributions from the community.

## How to Contribute

### Reporting Issues
- Use the GitHub issue tracker to report bugs
- Include detailed steps to reproduce the issue
- Provide system information (OS, Python version, GPU)
- Include relevant log files

### Feature Requests
- Open an issue with the "enhancement" label
- Describe the feature and its use case
- Explain how it would benefit users

### Code Contributions

#### Development Setup
1. Fork the repository
2. Clone your fork locally
3. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   venv\Scripts\activate.bat  # Windows
   ```
4. Install development dependencies:
   ```bash
   pip install -r requirements-dev.txt
   ```

#### Making Changes
1. Create a new branch for your feature:
   ```bash
   git checkout -b feature/your-feature-name
   ```
2. Make your changes
3. Add tests for new functionality
4. Run the test suite:
   ```bash
   python -m pytest tests/
   ```
5. Ensure code follows style guidelines:
   ```bash
   black .
   flake8 .
   ```

#### Submitting Changes
1. Commit your changes with clear messages
2. Push to your fork
3. Create a pull request
4. Describe your changes in the PR description
5. Link any related issues

## Code Style

- Follow PEP 8 for Python code
- Use meaningful variable and function names
- Add docstrings to functions and classes
- Keep functions focused and small
- Use type hints where appropriate

## Testing

- Write tests for new features
- Ensure existing tests pass
- Test on multiple platforms if possible
- Include integration tests for AI workflows

## Documentation

- Update README.md for new features
- Add docstrings to new functions
- Update configuration examples
- Include usage examples

## Community Guidelines

- Be respectful and inclusive
- Help others learn and grow
- Focus on constructive feedback
- Follow the code of conduct

## Questions?

Feel free to open an issue for questions or join our community discussions.

Thank you for contributing!
