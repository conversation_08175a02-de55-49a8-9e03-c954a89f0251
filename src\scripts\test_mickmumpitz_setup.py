"""
Test script to verify that mi<PERSON><PERSON><PERSON><PERSON>'s workflows are set up correctly.
"""

import os
import sys
import json
from pathlib import Path

def check_workflow_files():
    """Check if workflow files exist and are valid JSON."""
    workflows_dir = Path('workflows/mickmumpitz')
    
    # List of required workflow files
    required_files = [
        '201002_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v4.json',
        '201118_Mickmumpitz_3D-RENDERING_VIDEO_SD15LCM_SIMPLE_v4.json',
        '241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json',
        '241007_MICKMUMPITZ_FLUX+LORA.json'
    ]
    
    # Check if files exist
    missing_files = []
    for file in required_files:
        if not (workflows_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ The following workflow files are missing:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    # Check if files are valid JSON
    invalid_files = []
    for file in required_files:
        try:
            with open(workflows_dir / file, 'r', encoding='utf-8') as f:
                json.load(f)
        except json.JSONDecodeError:
            invalid_files.append(file)
    
    if invalid_files:
        print("❌ The following workflow files are not valid JSON:")
        for file in invalid_files:
            print(f"  - {file}")
        return False
    
    print("✅ All workflow files exist and are valid JSON.")
    return True

def check_comfyui_nodes():
    """Check if required ComfyUI nodes are installed."""
    # Get ComfyUI path from environment variable or use default
    comfyui_path = os.environ.get('COMFYUI_PATH', 'C:/Users/<USER>/OneDrive/Desktop/ComfyUI_windows_portable_nvidia (1)/ComfyUI_windows_portable/ComfyUI')
    
    # List of required node directories
    required_nodes = [
        'comfyui_controlnet_aux',
        'ComfyUI-AnimateDiff-Evolved',
        'ComfyUI-VideoHelperSuite',
        'ComfyUI_IPAdapter_plus',
        'ComfyUI_UltimateSDUpscale',
        'ComfyUI-Manager',
        'rgthree-comfy',
        'ComfyQR',
        'ComfyUI-KJNodes',
        'was-node-suite-comfyui',
        'ComfyUI-Advanced-ControlNet',
        'comfyui-inpaint-nodes',
        'ComfyUI-Frame-Interpolation'
    ]
    
    # Alternative nodes that might be installed instead
    alternative_nodes = {
        'ComfyUI-FluxNodes': ['x-flux-comfyui', 'ComfyUI-PuLID-Flux'],
        'comfyui-reactor-node': ['ComfyUI-ReActor'],
        'ComfyUI-Flowty-LDSR': ['ComfyUI-Flowty-LDSR']
    }
    
    # Check if custom_nodes directory exists
    custom_nodes_dir = os.path.join(comfyui_path, 'custom_nodes')
    if not os.path.exists(custom_nodes_dir):
        print(f"❌ Custom nodes directory not found: {custom_nodes_dir}")
        return False
    
    # Check if required nodes are installed
    missing_nodes = []
    for node in required_nodes:
        if not os.path.exists(os.path.join(custom_nodes_dir, node)):
            missing_nodes.append(node)
    
    # Check if alternative nodes are installed
    missing_alternatives = []
    for node, alternatives in alternative_nodes.items():
        if not os.path.exists(os.path.join(custom_nodes_dir, node)):
            # Check if any alternative is installed
            if not any(os.path.exists(os.path.join(custom_nodes_dir, alt)) for alt in alternatives):
                missing_alternatives.append(node)
    
    if missing_nodes:
        print("❌ The following required nodes are missing:")
        for node in missing_nodes:
            print(f"  - {node}")
    
    if missing_alternatives:
        print("❌ The following nodes (or their alternatives) are missing:")
        for node in missing_alternatives:
            print(f"  - {node} (alternatives: {', '.join(alternative_nodes[node])})")
    
    if not missing_nodes and not missing_alternatives:
        print("✅ All required ComfyUI nodes are installed.")
        return True
    
    return False

def main():
    """Main function."""
    print("Testing mickmumpitz's workflows setup...")
    print("\nChecking workflow files:")
    workflows_ok = check_workflow_files()
    
    print("\nChecking ComfyUI nodes:")
    nodes_ok = check_comfyui_nodes()
    
    if workflows_ok and nodes_ok:
        print("\n✅ Everything is set up correctly!")
        print("\nYou can now use mickmumpitz's workflows in ComfyUI:")
        print("1. Start ComfyUI")
        print("2. Load one of the workflow files from the workflows/mickmumpitz directory")
        print("3. Follow mickmumpitz's guides for using the workflows")
    else:
        print("\n❌ There are some issues with the setup.")
        print("Please fix the issues mentioned above and run this script again.")

if __name__ == '__main__':
    main()
