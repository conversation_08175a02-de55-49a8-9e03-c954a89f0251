"""
Setup script for AI Integration (ComfyUI + Hugging Face)
Installs dependencies and configures the system
"""

import subprocess
import sys
import os
import requests
import json

def install_requirements():
    """Install Python requirements."""
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", 
            "backend/requirements.txt"
        ])
        print("✅ Python dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_comfyui_installation():
    """Check if ComfyUI is properly installed and running."""
    print("🔍 Checking ComfyUI installation...")
    
    # Check if ComfyUI directory exists
    comfyui_path = r"C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)"
    
    if os.path.exists(comfyui_path):
        print(f"✅ ComfyUI found at: {comfyui_path}")
        
        # Check if ComfyUI is running
        try:
            response = requests.get("http://127.0.0.1:8188/system_stats", timeout=5)
            if response.status_code == 200:
                print("✅ ComfyUI is running and accessible")
                return True
            else:
                print("⚠️  ComfyUI directory found but not running")
                print("   Please start ComfyUI before proceeding")
                return False
        except requests.exceptions.RequestException:
            print("⚠️  ComfyUI directory found but not running")
            print("   Please start ComfyUI before proceeding")
            return False
    else:
        print("❌ ComfyUI not found at expected location")
        print(f"   Expected: {comfyui_path}")
        print("   Please verify your ComfyUI installation path")
        return False

def install_comfyui_custom_nodes():
    """Install required custom nodes for ComfyUI."""
    print("🔧 Installing ComfyUI custom nodes...")
    
    comfyui_path = r"C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)"
    custom_nodes_path = os.path.join(comfyui_path, "ComfyUI", "custom_nodes")
    
    if not os.path.exists(custom_nodes_path):
        print(f"❌ Custom nodes directory not found: {custom_nodes_path}")
        return False
    
    # List of required custom nodes
    required_nodes = [
        {
            "name": "ComfyUI-Manager",
            "url": "https://github.com/ltdrdata/ComfyUI-Manager.git",
            "description": "Node manager for easy installation"
        },
        {
            "name": "ComfyUI-3D-Pack", 
            "url": "https://github.com/MrForExample/ComfyUI-3D-Pack.git",
            "description": "3D generation nodes"
        }
    ]
    
    for node in required_nodes:
        node_path = os.path.join(custom_nodes_path, node["name"])
        
        if os.path.exists(node_path):
            print(f"✅ {node['name']} already installed")
        else:
            print(f"📥 Installing {node['name']}...")
            try:
                subprocess.check_call([
                    "git", "clone", node["url"], node_path
                ])
                print(f"✅ {node['name']} installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install {node['name']}: {e}")
                print("   You may need to install this manually")
    
    print("\n⚠️  After installing custom nodes, please restart ComfyUI")
    return True

def setup_environment_variables():
    """Set up environment variables."""
    print("🔧 Setting up environment variables...")
    
    # Create .env file if it doesn't exist
    env_file = ".env"
    
    env_content = """# 3D Character Generator Environment Variables

# Hugging Face API Token
# Get from: https://huggingface.co/settings/tokens
HUGGINGFACE_API_TOKEN=your_token_here

# ComfyUI Configuration
COMFYUI_HOST=127.0.0.1
COMFYUI_PORT=8188

# Optional: OpenAI API Key
OPENAI_API_KEY=your_openai_key_here

# ComfyUI Paths (update these to match your installation)
COMFYUI_INPUT_FOLDER=C:/Users/<USER>/OneDrive/Desktop/ComfyUI_windows_portable_nvidia (1)/ComfyUI/input
COMFYUI_OUTPUT_FOLDER=C:/Users/<USER>/OneDrive/Desktop/ComfyUI_windows_portable_nvidia (1)/ComfyUI/output
"""
    
    if not os.path.exists(env_file):
        with open(env_file, 'w') as f:
            f.write(env_content)
        print(f"✅ Created {env_file} file")
        print("   Please edit this file with your actual API tokens")
    else:
        print(f"✅ {env_file} file already exists")
    
    return True

def test_integrations():
    """Test all integrations."""
    print("🧪 Testing integrations...")
    
    # Test imports
    try:
        sys.path.append('backend')
        from comfyui_client import test_comfyui_connection
        from huggingface_client import test_huggingface_connection
        from config import validate_setup
        
        print("✅ All modules imported successfully")
        
        # Run validation
        results = validate_setup()
        
        print("\n📊 Integration Test Results:")
        for service, status in results.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {service.title()}")
        
        return all(results.values())
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def create_startup_script():
    """Create a startup script for the enhanced system."""
    print("📝 Creating startup script...")
    
    startup_content = '''@echo off
echo Starting 3D Character Generator with AI Integration
echo ================================================

echo.
echo 1. Starting backend server...
cd backend
start "Backend Server" python app.py

echo.
echo 2. Opening web interface...
timeout /t 3 /nobreak > nul
start "" "file:///%CD%/../simple_frontend.html"

echo.
echo 3. System ready!
echo   - Backend: http://localhost:5000
echo   - Frontend: file:///simple_frontend.html
echo   - ComfyUI: http://localhost:8188 (if running)
echo.
echo Press any key to exit...
pause > nul
'''
    
    with open("start_ai_system.bat", 'w') as f:
        f.write(startup_content)
    
    print("✅ Created start_ai_system.bat")
    return True

def main():
    """Main setup function."""
    print("🚀 3D Character Generator - AI Integration Setup")
    print("=" * 50)
    
    steps = [
        ("Installing Python dependencies", install_requirements),
        ("Checking ComfyUI installation", check_comfyui_installation),
        ("Installing ComfyUI custom nodes", install_comfyui_custom_nodes),
        ("Setting up environment variables", setup_environment_variables),
        ("Testing integrations", test_integrations),
        ("Creating startup script", create_startup_script)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        try:
            result = step_func()
            results.append(result)
            if result:
                print(f"✅ {step_name} completed")
            else:
                print(f"⚠️  {step_name} completed with warnings")
        except Exception as e:
            print(f"❌ {step_name} failed: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Setup Summary")
    print("=" * 50)
    
    success_count = sum(results)
    total_count = len(results)
    
    for i, (step_name, _) in enumerate(steps):
        status = "✅" if results[i] else "❌"
        print(f"{status} {step_name}")
    
    print(f"\n📊 {success_count}/{total_count} steps completed successfully")
    
    if success_count == total_count:
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file with your API tokens")
        print("2. Start ComfyUI if not already running")
        print("3. Run: python backend/config.py (for interactive setup)")
        print("4. Use start_ai_system.bat to launch everything")
    else:
        print("\n⚠️  Setup completed with some issues")
        print("Please review the errors above and fix them manually")
    
    input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
