#!/usr/bin/env python3
"""Test generating multiple character types"""

import sys
import os
sys.path.append('backend')

def test_character_variety():
    """Test generating different character types"""
    print("🎮 Testing Multiple Character Generation")
    print("=" * 40)
    
    try:
        from app import create_character_glb, analyze_character_prompt
        
        # Test different character prompts
        test_prompts = [
            "A blue robot warrior with silver armor",
            "A mystical alien wizard with purple robes", 
            "A fierce dragon knight with golden scales",
            "A cute cartoon cat character",
            "A cyberpunk hacker with neon implants"
        ]
        
        results = []
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n🎯 Test {i+1}: {prompt}")
            
            # Analyze character
            character_type = analyze_character_prompt(prompt)
            print(f"   Character type: {character_type['name']}")
            print(f"   Primary color: {character_type['colors']['primary']}")
            
            # Generate GLB
            glb_content = create_character_glb(prompt)
            
            # Save file
            filename = f"test_character_{i+1}_{character_type['name']}.glb"
            with open(filename, 'wb') as f:
                f.write(glb_content)
            
            file_size = len(glb_content)
            print(f"   ✅ Generated: {filename} ({file_size:,} bytes)")
            
            results.append({
                'prompt': prompt,
                'type': character_type['name'],
                'filename': filename,
                'size': file_size
            })
        
        # Summary
        print(f"\n📊 Generation Summary:")
        print("=" * 25)
        total_size = sum(r['size'] for r in results)
        print(f"Characters generated: {len(results)}")
        print(f"Total file size: {total_size:,} bytes")
        print(f"Average file size: {total_size//len(results):,} bytes")
        
        print(f"\n📁 Generated Files:")
        for result in results:
            print(f"   {result['filename']} - {result['type']} ({result['size']:,} bytes)")
        
        return True
        
    except Exception as e:
        print(f"❌ Character generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_character_variety()
    
    if success:
        print("\n🎉 Multiple character generation successful!")
        print("All GLB files are ready for use in games, Blender, Unity, etc.")
    else:
        print("\n⚠️ Some tests failed")
    
    input("\nPress Enter to exit...")
