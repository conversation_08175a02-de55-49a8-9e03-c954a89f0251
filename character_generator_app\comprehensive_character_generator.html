<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 Comprehensive 3D Character Generator</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .main-container {
            display: grid;
            grid-template-columns: 350px 1fr 350px;
            grid-template-rows: 80px 1fr;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        .header {
            grid-column: 1 / -1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            font-size: 2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .status-indicators {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            font-size: 14px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #dc3545;
        }

        .status-dot.connected { background: #28a745; }
        .status-dot.connecting { background: #ffc107; animation: pulse 1s infinite; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .left-panel, .right-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            padding: 20px;
            overflow-y: auto;
        }

        .center-panel {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .panel-title {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #ffd700;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
        }

        textarea.form-control { height: 80px; resize: vertical; }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            opacity: 0.6;
        }

        .btn.secondary {
            background: linear-gradient(45deg, #17a2b8, #138496);
        }

        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .image-upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .image-upload-area:hover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
        }

        .image-upload-area.dragover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.2);
        }

        .uploaded-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .progress-section {
            margin-top: 20px;
        }

        .step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #666;
            transition: all 0.3s ease;
        }

        .step.pending { border-left-color: #ffc107; background: rgba(255, 193, 7, 0.1); }
        .step.running { border-left-color: #17a2b8; background: rgba(23, 162, 184, 0.1); animation: pulse 2s infinite; }
        .step.success { border-left-color: #28a745; background: rgba(40, 167, 69, 0.1); }
        .step.error { border-left-color: #dc3545; background: rgba(220, 53, 69, 0.1); }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .step-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
            font-size: 10px;
        }

        .step.pending .step-icon { background: #ffc107; color: #333; }
        .step.running .step-icon { background: #17a2b8; color: white; }
        .step.success .step-icon { background: #28a745; color: white; }
        .step.error .step-icon { background: #dc3545; color: white; }

        .step-title { font-weight: 600; font-size: 14px; }
        .step-description { font-size: 12px; opacity: 0.8; }

        .model-viewer {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .viewer-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 100;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .viewer-btn {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .viewer-btn:hover {
            background: rgba(0, 0, 0, 0.9);
        }

        .agentic-seek-section {
            background: rgba(138, 43, 226, 0.2);
            border: 1px solid rgba(138, 43, 226, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .agentic-seek-title {
            color: #da70d6;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-area {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 10px;
            height: 150px;
            overflow-y: auto;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .chat-input {
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }

        .chat-input button {
            padding: 8px 15px;
            background: linear-gradient(45deg, #8a2be2, #9932cc);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .integration-status {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .integration-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-size: 12px;
        }

        .integration-item.connected {
            border: 1px solid #28a745;
            color: #28a745;
        }

        .integration-item.disconnected {
            border: 1px solid #dc3545;
            color: #dc3545;
        }

        .integration-item.connecting {
            border: 1px solid #ffc107;
            color: #ffc107;
        }

        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: 80px auto auto auto;
            }

            .center-panel {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <h1>🎭 Comprehensive 3D Character Generator</h1>
            <div class="status-indicators">
                <div class="status-indicator">
                    <div class="status-dot" id="comfyuiStatus"></div>
                    <span>ComfyUI</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="agenticSeekStatus"></div>
                    <span>AgenticSeek</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="huggingfaceStatus"></div>
                    <span>HuggingFace</span>
                </div>
            </div>
        </div>

        <!-- Left Panel: Input & Configuration -->
        <div class="left-panel">
            <div class="panel-title">📝 Character Input</div>

            <!-- Image Upload -->
            <div class="form-group">
                <label class="form-label">Upload Reference Image:</label>
                <div class="image-upload-area" id="imageUploadArea">
                    <div>📸 Click or drag image here</div>
                    <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;">
                        Supports: JPG, PNG, WebP
                    </div>
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                    <img id="uploadedImage" class="uploaded-image" style="display: none;">
                </div>
            </div>

            <!-- Text Description -->
            <div class="form-group">
                <label class="form-label" for="characterDescription">Character Description:</label>
                <textarea
                    id="characterDescription"
                    class="form-control"
                    placeholder="A brave medieval knight with silver armor and red cape"
                ></textarea>
            </div>

            <!-- Style Selection -->
            <div class="form-group">
                <label class="form-label" for="artStyle">Art Style:</label>
                <select id="artStyle" class="form-control">
                    <option value="realistic">Realistic</option>
                    <option value="anime">Anime</option>
                    <option value="cartoon">Cartoon</option>
                    <option value="fantasy">Fantasy</option>
                    <option value="cyberpunk">Cyberpunk</option>
                    <option value="stylized">Stylized</option>
                </select>
            </div>

            <!-- Quality Settings -->
            <div class="form-group">
                <label class="form-label" for="qualityLevel">Quality Level:</label>
                <select id="qualityLevel" class="form-control">
                    <option value="low">Low (Fast)</option>
                    <option value="medium" selected>Medium (Balanced)</option>
                    <option value="high">High (Detailed)</option>
                    <option value="ultra">Ultra (Maximum)</option>
                </select>
            </div>

            <!-- Workflow Selection -->
            <div class="form-group">
                <label class="form-label" for="workflowType">Workflow Type:</label>
                <select id="workflowType" class="form-control">
                    <option value="standard">Standard Pipeline</option>
                    <option value="mickmumpitz">Mickmumpitz Enhanced</option>
                    <option value="triposr">TripoSR (Image to 3D)</option>
                    <option value="wonder3d">Wonder3D (Multi-view)</option>
                </select>
            </div>

            <!-- Generation Buttons -->
            <button id="generateBtn" class="btn">🚀 Generate Character</button>
            <button id="enhanceBtn" class="btn secondary" disabled>✨ Enhance with AI</button>
            <button id="exportBtn" class="btn success" disabled>📥 Export Model</button>

            <!-- Integration Status -->
            <div class="integration-status">
                <div class="integration-item disconnected" id="comfyuiIntegration">
                    <div>ComfyUI</div>
                    <div id="comfyuiIntegrationStatus">Disconnected</div>
                </div>
                <div class="integration-item disconnected" id="hfIntegration">
                    <div>HuggingFace</div>
                    <div id="hfIntegrationStatus">Disconnected</div>
                </div>
            </div>

            <!-- AgenticSeek Integration -->
            <div class="agentic-seek-section">
                <div class="agentic-seek-title">
                    🤖 AgenticSeek Assistant
                </div>
                <div class="chat-area" id="agenticChatArea">
                    <div style="color: #da70d6;">[AgenticSeek] Ready to assist with character generation...</div>
                </div>
                <div class="chat-input">
                    <input type="text" id="agenticInput" placeholder="Ask AgenticSeek for help...">
                    <button id="agenticSendBtn">Send</button>
                </div>
            </div>
        </div>

        <!-- Center Panel: 3D Model Viewer -->
        <div class="center-panel">
            <div class="model-viewer" id="modelViewer">
                <div class="viewer-controls">
                    <button class="viewer-btn" id="resetViewBtn">🔄 Reset View</button>
                    <button class="viewer-btn" id="wireframeBtn">📐 Wireframe</button>
                    <button class="viewer-btn" id="animateBtn">▶️ Animate</button>
                    <button class="viewer-btn" id="fullscreenBtn">🔍 Fullscreen</button>
                </div>
                <div id="threeJsContainer" style="width: 100%; height: 100%;"></div>
            </div>
        </div>

        <!-- Right Panel: Progress & Results -->
        <div class="right-panel">
            <div class="panel-title">📊 Generation Progress</div>

            <!-- Progress Steps -->
            <div class="progress-section" id="progressSection">
                <!-- Steps will be dynamically added here -->
            </div>

            <!-- Results Section -->
            <div id="resultsSection" style="display: none;">
                <div class="panel-title">✅ Generation Complete</div>

                <div class="form-group">
                    <label class="form-label">Model Statistics:</label>
                    <div style="background: rgba(0, 0, 0, 0.3); padding: 10px; border-radius: 5px; font-size: 12px;">
                        <div>Vertices: <span id="vertexCount">-</span></div>
                        <div>Faces: <span id="faceCount">-</span></div>
                        <div>File Size: <span id="fileSize">-</span></div>
                        <div>Generation Time: <span id="generationTime">-</span></div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Export Options:</label>
                    <button class="btn secondary" id="downloadGLBBtn">📥 Download GLB</button>
                    <button class="btn secondary" id="downloadFBXBtn">📥 Download FBX</button>
                    <button class="btn secondary" id="downloadOBJBtn">📥 Download OBJ</button>
                </div>

                <div class="form-group">
                    <label class="form-label">Game Engine Export:</label>
                    <button class="btn" id="exportUnityBtn">🎮 Export to Unity</button>
                    <button class="btn" id="exportUnrealBtn">🎮 Export to Unreal</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Comprehensive Character Generator System
        class ComprehensiveCharacterGenerator {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.currentModel = null;
                this.mixer = null;
                this.clock = new THREE.Clock();

                this.services = {
                    comfyui: { connected: false, url: 'http://127.0.0.1:8188' },
                    agenticSeek: { connected: false, url: 'http://127.0.0.1:8000' },
                    huggingface: { connected: false, token: null }
                };

                this.generationSteps = [
                    { id: 'validation', title: 'Input Validation', description: 'Validating inputs and checking services' },
                    { id: 'preprocessing', title: 'Image Processing', description: 'Processing uploaded image and extracting features' },
                    { id: 'workflow', title: 'Workflow Execution', description: 'Running ComfyUI workflow for character generation' },
                    { id: 'enhancement', title: 'AI Enhancement', description: 'Enhancing model with AI-powered improvements' },
                    { id: 'optimization', title: 'Model Optimization', description: 'Optimizing mesh and textures for performance' },
                    { id: 'export', title: 'Model Export', description: 'Generating final 3D model files' }
                ];

                this.init();
            }

            init() {
                this.initThreeJS();
                this.initEventListeners();
                this.checkServiceConnections();
                this.initProgressSteps();
                this.initAgenticSeek();
            }

            initThreeJS() {
                const container = document.getElementById('threeJsContainer');

                // Scene
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x2a2a2a);

                // Camera
                this.camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
                this.camera.position.set(0, 1, 3);

                // Renderer
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(container.clientWidth, container.clientHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                container.appendChild(this.renderer.domElement);

                // Controls
                this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;

                // Lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                this.scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                this.scene.add(directionalLight);

                // Add default cube
                this.addDefaultCube();

                // Start render loop
                this.animate();

                // Handle resize
                window.addEventListener('resize', () => this.onWindowResize());
            }

            addDefaultCube() {
                const geometry = new THREE.BoxGeometry(1, 1, 1);
                const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
                const cube = new THREE.Mesh(geometry, material);
                cube.castShadow = true;
                cube.receiveShadow = true;
                this.scene.add(cube);
                this.currentModel = cube;
            }

            initEventListeners() {
                // Image upload
                const imageUploadArea = document.getElementById('imageUploadArea');
                const imageInput = document.getElementById('imageInput');

                imageUploadArea.addEventListener('click', () => imageInput.click());
                imageUploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    imageUploadArea.classList.add('dragover');
                });
                imageUploadArea.addEventListener('dragleave', () => {
                    imageUploadArea.classList.remove('dragover');
                });
                imageUploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    imageUploadArea.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.handleImageUpload(files[0]);
                    }
                });

                imageInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.handleImageUpload(e.target.files[0]);
                    }
                });

                // Generation buttons
                document.getElementById('generateBtn').addEventListener('click', () => this.startGeneration());
                document.getElementById('enhanceBtn').addEventListener('click', () => this.enhanceModel());
                document.getElementById('exportBtn').addEventListener('click', () => this.exportModel());

                // Viewer controls
                document.getElementById('resetViewBtn').addEventListener('click', () => this.resetView());
                document.getElementById('wireframeBtn').addEventListener('click', () => this.toggleWireframe());
                document.getElementById('animateBtn').addEventListener('click', () => this.toggleAnimation());
                document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());

                // AgenticSeek
                document.getElementById('agenticSendBtn').addEventListener('click', () => this.sendAgenticMessage());
                document.getElementById('agenticInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendAgenticMessage();
                });

                // Export buttons
                document.getElementById('downloadGLBBtn').addEventListener('click', () => this.downloadModel('glb'));
                document.getElementById('downloadFBXBtn').addEventListener('click', () => this.downloadModel('fbx'));
                document.getElementById('downloadOBJBtn').addEventListener('click', () => this.downloadModel('obj'));
                document.getElementById('exportUnityBtn').addEventListener('click', () => this.exportToGameEngine('unity'));
                document.getElementById('exportUnrealBtn').addEventListener('click', () => this.exportToGameEngine('unreal'));
            }

            handleImageUpload(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const uploadedImage = document.getElementById('uploadedImage');
                    uploadedImage.src = e.target.result;
                    uploadedImage.style.display = 'block';

                    // Update upload area
                    const uploadArea = document.getElementById('imageUploadArea');
                    uploadArea.innerHTML = '';
                    uploadArea.appendChild(uploadedImage);

                    this.log('Image uploaded successfully', 'success');
                };
                reader.readAsDataURL(file);
            }

            async checkServiceConnections() {
                this.updateServiceStatus('comfyui', 'connecting');
                this.updateServiceStatus('agenticSeek', 'connecting');
                this.updateServiceStatus('huggingface', 'connecting');

                // Check all services through our backend API
                try {
                    const response = await fetch('http://127.0.0.1:8080/services/status');
                    if (response.ok) {
                        const services = await response.json();

                        // Update ComfyUI status
                        if (services.comfyui?.connected) {
                            this.services.comfyui.connected = true;
                            this.updateServiceStatus('comfyui', 'connected');
                            this.updateIntegrationStatus('comfyuiIntegration', 'connected', 'Connected');
                            this.log('ComfyUI connection verified', 'success');
                        } else {
                            this.updateServiceStatus('comfyui', 'disconnected');
                            this.updateIntegrationStatus('comfyuiIntegration', 'disconnected', 'Disconnected');
                        }

                        // Update AgenticSeek status
                        if (services.agentic_seek?.connected) {
                            this.services.agenticSeek.connected = true;
                            this.updateServiceStatus('agenticSeek', 'connected');
                            this.log('AgenticSeek connection verified', 'success');
                        } else {
                            this.updateServiceStatus('agenticSeek', 'disconnected');
                        }

                        // Update HuggingFace status
                        if (services.huggingface?.connected) {
                            this.services.huggingface.connected = true;
                            this.updateServiceStatus('huggingface', 'connected');
                            this.updateIntegrationStatus('hfIntegration', 'connected', 'Connected');
                            this.log('HuggingFace connection verified', 'success');
                        } else {
                            this.updateServiceStatus('huggingface', 'disconnected');
                            this.updateIntegrationStatus('hfIntegration', 'disconnected', 'Disconnected');
                        }

                    } else {
                        // Fallback to individual checks
                        await this.checkIndividualServices();
                    }
                } catch (error) {
                    this.log('Backend service status check failed, using fallback', 'warning');
                    await this.checkIndividualServices();
                }
            }

            async checkIndividualServices() {
                // Check ComfyUI directly
                try {
                    const response = await fetch(`${this.services.comfyui.url}/system_stats`);
                    if (response.ok) {
                        this.services.comfyui.connected = true;
                        this.updateServiceStatus('comfyui', 'connected');
                        this.updateIntegrationStatus('comfyuiIntegration', 'connected', 'Connected');
                    }
                } catch (error) {
                    this.updateServiceStatus('comfyui', 'disconnected');
                    this.updateIntegrationStatus('comfyuiIntegration', 'disconnected', 'Disconnected');
                }

                // Check AgenticSeek directly
                try {
                    const response = await fetch(`${this.services.agenticSeek.url}/health`);
                    if (response.ok) {
                        this.services.agenticSeek.connected = true;
                        this.updateServiceStatus('agenticSeek', 'connected');
                    }
                } catch (error) {
                    this.updateServiceStatus('agenticSeek', 'disconnected');
                }

                // HuggingFace is checked through backend only
                this.updateServiceStatus('huggingface', 'disconnected');
                this.updateIntegrationStatus('hfIntegration', 'disconnected', 'Backend Only');
            }

            updateServiceStatus(service, status) {
                const statusDot = document.getElementById(`${service}Status`);
                statusDot.className = `status-dot ${status}`;
            }

            updateIntegrationStatus(elementId, status, text) {
                const element = document.getElementById(elementId);
                element.className = `integration-item ${status}`;
                document.getElementById(`${elementId.replace('Integration', 'IntegrationStatus')}`).textContent = text;
            }

            initProgressSteps() {
                const progressSection = document.getElementById('progressSection');
                progressSection.innerHTML = '';

                this.generationSteps.forEach(step => {
                    const stepElement = document.createElement('div');
                    stepElement.className = 'step pending';
                    stepElement.id = `step-${step.id}`;

                    stepElement.innerHTML = `
                        <div class="step-header">
                            <div class="step-icon">${this.generationSteps.indexOf(step) + 1}</div>
                            <div class="step-title">${step.title}</div>
                        </div>
                        <div class="step-description">${step.description}</div>
                    `;

                    progressSection.appendChild(stepElement);
                });
            }

            initAgenticSeek() {
                this.addAgenticMessage('AgenticSeek', 'Hello! I\'m ready to help you create amazing 3D characters. You can ask me about:', 'system');
                this.addAgenticMessage('AgenticSeek', '• Character design suggestions', 'system');
                this.addAgenticMessage('AgenticSeek', '• Workflow optimization tips', 'system');
                this.addAgenticMessage('AgenticSeek', '• Technical troubleshooting', 'system');
                this.addAgenticMessage('AgenticSeek', '• Best practices for 3D modeling', 'system');
            }

            async startGeneration() {
                const generateBtn = document.getElementById('generateBtn');
                generateBtn.disabled = true;
                generateBtn.textContent = '🔄 Generating...';

                try {
                    // Reset progress
                    this.resetProgressSteps();
                    document.getElementById('resultsSection').style.display = 'none';

                    // Execute generation steps
                    for (const step of this.generationSteps) {
                        await this.executeStep(step);
                    }

                    // Show results
                    this.showResults();
                    this.log('Character generation completed successfully!', 'success');

                } catch (error) {
                    this.log(`Generation failed: ${error.message}`, 'error');
                    this.addAgenticMessage('System', `Generation failed: ${error.message}`, 'error');
                } finally {
                    generateBtn.disabled = false;
                    generateBtn.textContent = '🚀 Generate Character';
                }
            }

            async executeStep(step) {
                const stepElement = document.getElementById(`step-${step.id}`);
                const stepIcon = stepElement.querySelector('.step-icon');

                // Mark as running
                stepElement.className = 'step running';
                stepIcon.innerHTML = '⏳';

                try {
                    // Simulate step execution with actual logic
                    await this.executeStepLogic(step.id);

                    // Mark as success
                    stepElement.className = 'step success';
                    stepIcon.innerHTML = '✓';

                    this.log(`Completed: ${step.title}`, 'success');

                } catch (error) {
                    // Mark as error
                    stepElement.className = 'step error';
                    stepIcon.innerHTML = '✗';

                    this.log(`Failed: ${step.title} - ${error.message}`, 'error');
                    throw error;
                }
            }

            async executeStepLogic(stepId) {
                const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

                switch (stepId) {
                    case 'validation':
                        await delay(1000);
                        if (!this.validateInputs()) {
                            throw new Error('Invalid inputs provided');
                        }
                        break;

                    case 'preprocessing':
                        await delay(2000);
                        await this.preprocessImage();
                        break;

                    case 'workflow':
                        await delay(5000);
                        await this.executeWorkflow();
                        break;

                    case 'enhancement':
                        await delay(3000);
                        await this.enhanceWithAI();
                        break;

                    case 'optimization':
                        await delay(2000);
                        await this.optimizeModel();
                        break;

                    case 'export':
                        await delay(1500);
                        await this.generateFinalModel();
                        break;
                }
            }

            validateInputs() {
                const description = document.getElementById('characterDescription').value.trim();
                const uploadedImage = document.getElementById('uploadedImage');

                if (!description && uploadedImage.style.display === 'none') {
                    return false;
                }

                return true;
            }

            async preprocessImage() {
                // Simulate image preprocessing
                this.log('Processing uploaded image...', 'info');
                // In real implementation, this would call face detection, feature extraction, etc.
            }

            async executeWorkflow() {
                const workflowType = document.getElementById('workflowType').value;
                this.log(`Executing ${workflowType} workflow...`, 'info');

                if (this.services.comfyui.connected) {
                    // In real implementation, this would call ComfyUI API
                    await this.callComfyUIWorkflow(workflowType);
                } else {
                    // Fallback to procedural generation
                    await this.proceduralGeneration();
                }
            }

            async callComfyUIWorkflow(workflowType) {
                try {
                    // Get uploaded image if available
                    const uploadedImage = document.getElementById('uploadedImage');
                    let imageData = null;

                    if (uploadedImage.style.display !== 'none') {
                        // Convert image to base64
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        canvas.width = uploadedImage.naturalWidth;
                        canvas.height = uploadedImage.naturalHeight;
                        ctx.drawImage(uploadedImage, 0, 0);
                        imageData = canvas.toDataURL('image/png');
                    }

                    // Prepare workflow data
                    const workflowData = {
                        workflow_type: workflowType,
                        prompt: document.getElementById('characterDescription').value,
                        style: document.getElementById('artStyle').value,
                        quality: document.getElementById('qualityLevel').value,
                        image_data: imageData
                    };

                    // Call backend API instead of direct ComfyUI
                    const response = await fetch('http://127.0.0.1:8080/generate/character', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(workflowData)
                    });

                    if (!response.ok) {
                        throw new Error('Character generation API failed');
                    }

                    const result = await response.json();
                    this.log(`Generation started with task ID: ${result.task_id}`, 'success');

                    // Monitor generation progress
                    await this.monitorGenerationProgress(result.task_id);

                } catch (error) {
                    this.log('API unavailable, using fallback generation', 'warning');
                    await this.proceduralGeneration();
                }
            }

            async monitorGenerationProgress(taskId) {
                // Simulate monitoring ComfyUI progress
                const steps = [
                    'Loading workflow...',
                    'Processing input image...',
                    'Generating character mesh...',
                    'Creating textures...',
                    'Optimizing model...',
                    'Finalizing output...'
                ];

                for (let i = 0; i < steps.length; i++) {
                    await this.sleep(2000);
                    this.log(steps[i], 'info');

                    // Update progress in UI
                    this.updateWorkflowProgress(i + 1, steps.length, steps[i]);
                }

                // Check for generated files
                await this.checkGeneratedFiles(taskId);
            }

            updateWorkflowProgress(current, total, message) {
                const progressPercent = (current / total) * 100;
                this.addAgenticMessage('ComfyUI', `Progress: ${progressPercent.toFixed(0)}% - ${message}`, 'system');
            }

            async checkGeneratedFiles(taskId) {
                try {
                    const response = await fetch('http://127.0.0.1:8080/models/list');
                    if (response.ok) {
                        const data = await response.json();
                        const generatedModel = data.models.find(model => model.name.includes(taskId));

                        if (generatedModel) {
                            this.log(`Generated model found: ${generatedModel.filename}`, 'success');
                            await this.loadGeneratedModel(generatedModel.url);
                        } else {
                            this.log('No generated model found, using procedural fallback', 'warning');
                        }
                    }
                } catch (error) {
                    this.log('Error checking generated files', 'error');
                }
            }

            async loadGeneratedModel(modelUrl) {
                try {
                    const loader = new THREE.GLTFLoader();

                    loader.load(
                        `http://127.0.0.1:8080${modelUrl}`,
                        (gltf) => {
                            // Remove existing model
                            if (this.currentModel) {
                                this.scene.remove(this.currentModel);
                            }

                            // Add new model
                            this.currentModel = gltf.scene;
                            this.scene.add(this.currentModel);

                            // Center and scale model
                            const box = new THREE.Box3().setFromObject(this.currentModel);
                            const center = box.getCenter(new THREE.Vector3());
                            this.currentModel.position.sub(center);

                            const size = box.getSize(new THREE.Vector3());
                            const maxDim = Math.max(size.x, size.y, size.z);
                            const scale = 2 / maxDim;
                            this.currentModel.scale.setScalar(scale);

                            // Setup animations if available
                            if (gltf.animations && gltf.animations.length > 0) {
                                this.mixer = new THREE.AnimationMixer(this.currentModel);
                                const action = this.mixer.clipAction(gltf.animations[0]);
                                action.play();
                            }

                            this.log('ComfyUI generated model loaded successfully!', 'success');
                            this.addAgenticMessage('System', 'ComfyUI workflow completed! Your character has been generated and is now displayed in the 3D viewer.', 'system');
                        },
                        (progress) => {
                            const percent = (progress.loaded / progress.total) * 100;
                            this.log(`Loading model: ${percent.toFixed(0)}%`, 'info');
                        },
                        (error) => {
                            this.log(`Error loading generated model: ${error.message}`, 'error');
                            this.proceduralGeneration();
                        }
                    );
                } catch (error) {
                    this.log(`Model loading failed: ${error.message}`, 'error');
                    this.proceduralGeneration();
                }
            }

            async proceduralGeneration() {
                // Create a more complex procedural model
                this.createProceduralCharacter();
                this.log('Procedural character generated', 'success');
            }

            createProceduralCharacter() {
                // Remove existing model
                if (this.currentModel) {
                    this.scene.remove(this.currentModel);
                }

                // Create character group
                const character = new THREE.Group();

                // Body (cylinder)
                const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.5, 8);
                const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 });
                const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
                body.position.y = 0.75;
                character.add(body);

                // Head (sphere)
                const headGeometry = new THREE.SphereGeometry(0.25, 16, 16);
                const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });
                const head = new THREE.Mesh(headGeometry, headMaterial);
                head.position.y = 1.75;
                character.add(head);

                // Arms
                const armGeometry = new THREE.CylinderGeometry(0.08, 0.1, 1, 6);
                const armMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });

                const leftArm = new THREE.Mesh(armGeometry, armMaterial);
                leftArm.position.set(-0.5, 1, 0);
                leftArm.rotation.z = Math.PI / 6;
                character.add(leftArm);

                const rightArm = new THREE.Mesh(armGeometry, armMaterial);
                rightArm.position.set(0.5, 1, 0);
                rightArm.rotation.z = -Math.PI / 6;
                character.add(rightArm);

                // Legs
                const legGeometry = new THREE.CylinderGeometry(0.1, 0.12, 1.2, 6);
                const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2c3e50 });

                const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
                leftLeg.position.set(-0.2, -0.6, 0);
                character.add(leftLeg);

                const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
                rightLeg.position.set(0.2, -0.6, 0);
                character.add(rightLeg);

                // Add to scene
                character.castShadow = true;
                character.receiveShadow = true;
                this.scene.add(character);
                this.currentModel = character;

                // Add simple animation
                this.addCharacterAnimation(character);
            }

            addCharacterAnimation(character) {
                // Simple bobbing animation
                const animate = () => {
                    const time = Date.now() * 0.001;
                    character.position.y = Math.sin(time * 2) * 0.1;
                    character.rotation.y = Math.sin(time) * 0.1;
                };

                this.characterAnimation = animate;
            }

            async enhanceWithAI() {
                this.log('Enhancing model with AI...', 'info');
                // Simulate AI enhancement
            }

            async optimizeModel() {
                this.log('Optimizing model for performance...', 'info');
                // Simulate model optimization
            }

            async generateFinalModel() {
                this.log('Generating final model files...', 'info');
                // Simulate final model generation
            }

            showResults() {
                document.getElementById('resultsSection').style.display = 'block';
                document.getElementById('enhanceBtn').disabled = false;
                document.getElementById('exportBtn').disabled = false;

                // Update statistics
                document.getElementById('vertexCount').textContent = '1,234';
                document.getElementById('faceCount').textContent = '2,468';
                document.getElementById('fileSize').textContent = '2.3 MB';
                document.getElementById('generationTime').textContent = '15.2s';
            }

            resetProgressSteps() {
                this.generationSteps.forEach((step, index) => {
                    const stepElement = document.getElementById(`step-${step.id}`);
                    stepElement.className = 'step pending';
                    stepElement.querySelector('.step-icon').innerHTML = index + 1;
                });
            }

            // AgenticSeek Integration
            async sendAgenticMessage() {
                const input = document.getElementById('agenticInput');
                const message = input.value.trim();

                if (!message) return;

                // Add user message
                this.addAgenticMessage('You', message, 'user');
                input.value = '';

                // Simulate AgenticSeek response
                setTimeout(() => {
                    const response = this.generateAgenticResponse(message);
                    this.addAgenticMessage('AgenticSeek', response, 'assistant');
                }, 1000);
            }

            generateAgenticResponse(message) {
                const responses = {
                    'help': 'I can help you with character generation, workflow optimization, and troubleshooting. What specific area would you like assistance with?',
                    'workflow': 'For best results, I recommend using the Mickmumpitz workflow for high-quality character generation. Make sure your input image has good lighting and clear facial features.',
                    'optimize': 'To optimize your model: 1) Use medium quality for balanced results, 2) Ensure your reference image is high resolution, 3) Choose the appropriate art style for your use case.',
                    'error': 'If you\'re experiencing errors, check that ComfyUI is running and all required models are installed. I can help diagnose specific issues.',
                    'export': 'You can export your character in multiple formats: GLB for web use, FBX for game engines, or OBJ for general 3D software. Each format has specific advantages.',
                    'default': 'I understand you\'re asking about character generation. Could you be more specific about what you need help with? I can assist with workflows, optimization, troubleshooting, or export options.'
                };

                const lowerMessage = message.toLowerCase();
                for (const [key, response] of Object.entries(responses)) {
                    if (lowerMessage.includes(key)) {
                        return response;
                    }
                }

                return responses.default;
            }

            addAgenticMessage(sender, message, type) {
                const chatArea = document.getElementById('agenticChatArea');
                const messageDiv = document.createElement('div');
                messageDiv.style.marginBottom = '8px';
                messageDiv.style.padding = '5px';
                messageDiv.style.borderRadius = '5px';

                const timestamp = new Date().toLocaleTimeString();

                switch (type) {
                    case 'user':
                        messageDiv.style.background = 'rgba(100, 149, 237, 0.3)';
                        messageDiv.style.color = '#87ceeb';
                        break;
                    case 'assistant':
                        messageDiv.style.background = 'rgba(218, 112, 214, 0.3)';
                        messageDiv.style.color = '#da70d6';
                        break;
                    case 'system':
                        messageDiv.style.background = 'rgba(128, 128, 128, 0.3)';
                        messageDiv.style.color = '#c0c0c0';
                        break;
                    case 'error':
                        messageDiv.style.background = 'rgba(220, 53, 69, 0.3)';
                        messageDiv.style.color = '#ff6b6b';
                        break;
                }

                messageDiv.innerHTML = `<strong>[${timestamp}] ${sender}:</strong> ${message}`;
                chatArea.appendChild(messageDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
            }

            // 3D Viewer Controls
            resetView() {
                this.camera.position.set(0, 1, 3);
                this.controls.reset();
            }

            toggleWireframe() {
                if (this.currentModel) {
                    this.currentModel.traverse((child) => {
                        if (child.isMesh) {
                            child.material.wireframe = !child.material.wireframe;
                        }
                    });
                }
            }

            toggleAnimation() {
                const btn = document.getElementById('animateBtn');
                if (this.animationEnabled) {
                    this.animationEnabled = false;
                    btn.textContent = '▶️ Animate';
                } else {
                    this.animationEnabled = true;
                    btn.textContent = '⏸️ Pause';
                }
            }

            toggleFullscreen() {
                const container = document.querySelector('.center-panel');
                if (!document.fullscreenElement) {
                    container.requestFullscreen();
                } else {
                    document.exitFullscreen();
                }
            }

            // Model Export Functions
            async downloadModel(format) {
                this.log(`Downloading model in ${format.toUpperCase()} format...`, 'info');

                // Simulate file generation and download
                setTimeout(() => {
                    const blob = new Blob(['// Simulated model data'], { type: 'application/octet-stream' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `character.${format}`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    this.log(`${format.toUpperCase()} file downloaded successfully`, 'success');
                }, 1000);
            }

            async exportToGameEngine(engine) {
                this.log(`Exporting to ${engine}...`, 'info');

                // Simulate game engine export
                setTimeout(() => {
                    this.log(`${engine} export package created successfully`, 'success');
                    this.addAgenticMessage('System', `Your character has been exported for ${engine}. The package includes optimized meshes, textures, and animation files.`, 'system');
                }, 2000);
            }

            // Animation and Rendering
            animate() {
                requestAnimationFrame(() => this.animate());

                if (this.animationEnabled && this.characterAnimation) {
                    this.characterAnimation();
                }

                this.controls.update();
                this.renderer.render(this.scene, this.camera);
            }

            onWindowResize() {
                const container = document.getElementById('threeJsContainer');
                this.camera.aspect = container.clientWidth / container.clientHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(container.clientWidth, container.clientHeight);
            }

            // Utility Functions
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logMessage = `[${timestamp}] ${message}`;

                console.log(logMessage);

                // Also add to AgenticSeek chat for important messages
                if (type === 'error' || type === 'success') {
                    this.addAgenticMessage('System', message, type === 'error' ? 'error' : 'system');
                }
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            window.characterGenerator = new ComprehensiveCharacterGenerator();
        });
    </script>
</body>
</html>
