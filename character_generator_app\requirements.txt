# Core Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
jinja2==3.1.2

# AI and Machine Learning
torch==2.1.0
torchvision==0.16.0
torchaudio==2.1.0
transformers==4.35.2
diffusers==0.24.0
accelerate==0.24.1
safetensors==0.4.0

# ComfyUI Dependencies
aiohttp==3.9.1
aiofiles==23.2.1
websockets==12.0
pillow==10.1.0
numpy==1.24.3
opencv-python==********
scipy==1.11.4

# Hugging Face Integration
huggingface-hub==0.19.4
datasets==2.14.6

# 3D Processing
trimesh==4.0.5
pygltflib==1.16.1
pymeshlab==2022.2.post4

# Face Processing
mediapipe==0.10.8
face-recognition==1.3.0
dlib==19.24.2

# Image Processing
imageio==2.31.6
scikit-image==0.22.0
matplotlib==3.8.2

# Database
sqlite3

# Utilities
requests==2.31.0
pydantic==2.5.0
python-dotenv==1.0.0
tqdm==4.66.1
psutil==5.9.6

# Development Tools
pytest==7.4.3
black==23.11.0
flake8==6.1.0

# Optional GPU Support (CUDA)
# Uncomment if you have NVIDIA GPU
# torch==2.1.0+cu118 --index-url https://download.pytorch.org/whl/cu118
# torchvision==0.16.0+cu118 --index-url https://download.pytorch.org/whl/cu118
# torchaudio==2.1.0+cu118 --index-url https://download.pytorch.org/whl/cu118
