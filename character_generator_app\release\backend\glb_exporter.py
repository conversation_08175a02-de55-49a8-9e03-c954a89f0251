#!/usr/bin/env python3
"""
GLB file export module for 3D characters
"""

import json
import struct
import base64

class GLBExporter:
    """Export 3D character data to GLB format"""
    
    def __init__(self):
        self.gltf_version = "2.0"
        self.generator = "Enhanced 3D Character Generator v2.0"
    
    def export_character_glb(self, vertices, indices, normals, materials, metadata=None):
        """Export character data to GLB format"""
        try:
            print("📦 Exporting character to GLB format...")
            
            # Validate input data
            if not self.validate_mesh_data(vertices, indices, normals):
                raise ValueError("Invalid mesh data")
            
            # Create GLTF structure
            gltf_data = self.create_gltf_structure(vertices, indices, normals, materials, metadata)
            
            # Pack binary data
            binary_data = self.pack_binary_data(vertices, indices, normals)
            
            # Create GLB file
            glb_content = self.create_glb_file(gltf_data, binary_data)
            
            print(f"✅ GLB exported: {len(glb_content):,} bytes")
            
            return glb_content
            
        except Exception as e:
            print(f"❌ GLB export failed: {e}")
            return self.create_fallback_glb(vertices, indices, normals)
    
    def validate_mesh_data(self, vertices, indices, normals):
        """Validate mesh data before export"""
        try:
            # Check if data exists
            if not vertices or not indices:
                print("⚠️ Missing vertices or indices")
                return False
            
            # Check data consistency
            vertex_count = len(vertices) // 3
            normal_count = len(normals) // 3 if normals else 0
            
            if normals and normal_count != vertex_count:
                print(f"⚠️ Vertex/normal count mismatch: {vertex_count} vs {normal_count}")
                return False
            
            # Check indices validity
            max_index = max(indices) if indices else 0
            if max_index >= vertex_count:
                print(f"⚠️ Invalid index: {max_index} >= {vertex_count}")
                return False
            
            # Check for degenerate triangles
            triangle_count = len(indices) // 3
            if triangle_count == 0:
                print("⚠️ No triangles found")
                return False
            
            print(f"✅ Mesh validation passed: {vertex_count} vertices, {triangle_count} triangles")
            return True
            
        except Exception as e:
            print(f"Mesh validation error: {e}")
            return False
    
    def create_gltf_structure(self, vertices, indices, normals, materials, metadata):
        """Create GLTF JSON structure"""
        try:
            vertex_count = len(vertices) // 3
            
            # Calculate bounding box
            min_pos = [min(vertices[i::3]) for i in range(3)]
            max_pos = [max(vertices[i::3]) for i in range(3)]
            
            # Base GLTF structure
            gltf_data = {
                "asset": {
                    "version": self.gltf_version,
                    "generator": self.generator
                },
                "scene": 0,
                "scenes": [{"nodes": [0]}],
                "nodes": [{"mesh": 0}],
                "meshes": [{
                    "primitives": [{
                        "attributes": {
                            "POSITION": 0
                        },
                        "indices": 2 if normals else 1,
                        "material": 0
                    }]
                }],
                "materials": materials if materials else [self.create_default_material()],
                "accessors": [
                    {
                        "bufferView": 0,
                        "componentType": 5126,  # FLOAT
                        "count": vertex_count,
                        "type": "VEC3",
                        "max": max_pos,
                        "min": min_pos
                    }
                ],
                "bufferViews": [
                    {
                        "buffer": 0,
                        "byteOffset": 0,
                        "byteLength": len(vertices) * 4
                    }
                ],
                "buffers": [{
                    "byteLength": len(vertices) * 4
                }]
            }
            
            # Add normals if available
            if normals:
                gltf_data["meshes"][0]["primitives"][0]["attributes"]["NORMAL"] = 1
                
                gltf_data["accessors"].append({
                    "bufferView": 1,
                    "componentType": 5126,  # FLOAT
                    "count": vertex_count,
                    "type": "VEC3"
                })
                
                gltf_data["bufferViews"].append({
                    "buffer": 0,
                    "byteOffset": len(vertices) * 4,
                    "byteLength": len(normals) * 4
                })
                
                gltf_data["buffers"][0]["byteLength"] += len(normals) * 4
            
            # Add indices
            indices_accessor_index = len(gltf_data["accessors"])
            gltf_data["accessors"].append({
                "bufferView": len(gltf_data["bufferViews"]),
                "componentType": 5123,  # UNSIGNED_SHORT
                "count": len(indices),
                "type": "SCALAR"
            })
            
            indices_offset = gltf_data["buffers"][0]["byteLength"]
            gltf_data["bufferViews"].append({
                "buffer": 0,
                "byteOffset": indices_offset,
                "byteLength": len(indices) * 2
            })
            
            gltf_data["buffers"][0]["byteLength"] += len(indices) * 2
            gltf_data["meshes"][0]["primitives"][0]["indices"] = indices_accessor_index
            
            # Add metadata if provided
            if metadata:
                gltf_data["extras"] = {
                    "character_metadata": metadata,
                    "generation_info": {
                        "vertex_count": vertex_count,
                        "triangle_count": len(indices) // 3,
                        "material_count": len(materials) if materials else 1,
                        "has_normals": bool(normals)
                    }
                }
            
            return gltf_data
            
        except Exception as e:
            print(f"GLTF structure creation error: {e}")
            return self.create_minimal_gltf_structure(vertices, indices)
    
    def pack_binary_data(self, vertices, indices, normals=None):
        """Pack mesh data into binary format"""
        try:
            binary_data = b''
            
            # Pack vertices
            for vertex in vertices:
                binary_data += struct.pack('<f', float(vertex))
            
            # Pack normals if available
            if normals:
                for normal in normals:
                    binary_data += struct.pack('<f', float(normal))
            
            # Pack indices
            for index in indices:
                binary_data += struct.pack('<H', int(index))
            
            return binary_data
            
        except Exception as e:
            print(f"Binary data packing error: {e}")
            return b''
    
    def create_glb_file(self, gltf_data, binary_data):
        """Create final GLB file"""
        try:
            # Convert GLTF to JSON
            json_data = json.dumps(gltf_data, separators=(',', ':')).encode('utf-8')
            json_length = len(json_data)
            
            # Pad JSON to 4-byte boundary
            json_padding = (4 - (json_length % 4)) % 4
            json_data += b' ' * json_padding
            json_length += json_padding
            
            # Pad binary data to 4-byte boundary
            binary_length = len(binary_data)
            binary_padding = (4 - (binary_length % 4)) % 4
            binary_data += b'\x00' * binary_padding
            binary_length += binary_padding
            
            # Calculate total length
            total_length = 12 + 8 + json_length + 8 + binary_length
            
            # Create GLB header
            glb_header = struct.pack('<III', 
                0x46546C67,  # Magic number "glTF"
                2,           # Version
                total_length # Total length
            )
            
            # Create JSON chunk
            json_chunk_header = struct.pack('<II', json_length, 0x4E4F534A)  # "JSON"
            json_chunk = json_chunk_header + json_data
            
            # Create binary chunk
            binary_chunk_header = struct.pack('<II', binary_length, 0x004E4942)  # "BIN\0"
            binary_chunk = binary_chunk_header + binary_data
            
            # Combine all parts
            glb_content = glb_header + json_chunk + binary_chunk
            
            return glb_content
            
        except Exception as e:
            print(f"GLB file creation error: {e}")
            return b''
    
    def create_default_material(self):
        """Create default material"""
        return {
            "name": "default_material",
            "pbrMetallicRoughness": {
                "baseColorFactor": [0.8, 0.8, 0.8, 1.0],
                "metallicFactor": 0.0,
                "roughnessFactor": 0.5
            },
            "emissiveFactor": [0.0, 0.0, 0.0]
        }
    
    def create_minimal_gltf_structure(self, vertices, indices):
        """Create minimal GLTF structure for fallback"""
        vertex_count = len(vertices) // 3
        
        return {
            "asset": {"version": "2.0"},
            "scene": 0,
            "scenes": [{"nodes": [0]}],
            "nodes": [{"mesh": 0}],
            "meshes": [{"primitives": [{"attributes": {"POSITION": 0}, "indices": 1}]}],
            "materials": [self.create_default_material()],
            "accessors": [
                {
                    "bufferView": 0,
                    "componentType": 5126,
                    "count": vertex_count,
                    "type": "VEC3"
                },
                {
                    "bufferView": 1,
                    "componentType": 5123,
                    "count": len(indices),
                    "type": "SCALAR"
                }
            ],
            "bufferViews": [
                {"buffer": 0, "byteOffset": 0, "byteLength": len(vertices) * 4},
                {"buffer": 0, "byteOffset": len(vertices) * 4, "byteLength": len(indices) * 2}
            ],
            "buffers": [{"byteLength": len(vertices) * 4 + len(indices) * 2}]
        }
    
    def create_fallback_glb(self, vertices, indices, normals):
        """Create fallback GLB when main export fails"""
        try:
            print("⚠️ Creating fallback GLB")
            
            # Use minimal data
            if not vertices:
                vertices = [0, 0, 0, 1, 0, 0, 0, 1, 0]  # Simple triangle
            if not indices:
                indices = [0, 1, 2]
            
            # Create minimal GLTF
            gltf_data = self.create_minimal_gltf_structure(vertices, indices)
            
            # Pack minimal binary data
            binary_data = b''
            for vertex in vertices:
                binary_data += struct.pack('<f', float(vertex))
            for index in indices:
                binary_data += struct.pack('<H', int(index))
            
            # Create GLB
            return self.create_glb_file(gltf_data, binary_data)
            
        except Exception as e:
            print(f"Fallback GLB creation failed: {e}")
            return b''
    
    def validate_glb_output(self, glb_content):
        """Validate GLB output"""
        try:
            if len(glb_content) < 20:
                return False, "GLB too small"
            
            # Check GLB magic number
            magic = struct.unpack('<I', glb_content[:4])[0]
            if magic != 0x46546C67:
                return False, "Invalid GLB magic number"
            
            # Check version
            version = struct.unpack('<I', glb_content[4:8])[0]
            if version != 2:
                return False, f"Unsupported GLB version: {version}"
            
            # Check length
            length = struct.unpack('<I', glb_content[8:12])[0]
            if length != len(glb_content):
                return False, f"Length mismatch: {length} vs {len(glb_content)}"
            
            return True, "Valid GLB"
            
        except Exception as e:
            return False, f"Validation error: {e}"
    
    def get_glb_info(self, glb_content):
        """Get information about GLB file"""
        try:
            info = {
                'file_size': len(glb_content),
                'valid': False,
                'version': None,
                'chunks': []
            }
            
            if len(glb_content) < 12:
                return info
            
            # Read header
            magic, version, length = struct.unpack('<III', glb_content[:12])
            
            info['valid'] = magic == 0x46546C67
            info['version'] = version
            
            # Read chunks
            offset = 12
            while offset < len(glb_content):
                if offset + 8 > len(glb_content):
                    break
                
                chunk_length, chunk_type = struct.unpack('<II', glb_content[offset:offset+8])
                chunk_info = {
                    'type': chunk_type,
                    'length': chunk_length,
                    'offset': offset + 8
                }
                
                if chunk_type == 0x4E4F534A:  # JSON
                    chunk_info['type_name'] = 'JSON'
                elif chunk_type == 0x004E4942:  # BIN
                    chunk_info['type_name'] = 'BIN'
                else:
                    chunk_info['type_name'] = 'UNKNOWN'
                
                info['chunks'].append(chunk_info)
                offset += 8 + chunk_length
            
            return info
            
        except Exception as e:
            print(f"GLB info extraction error: {e}")
            return {'file_size': len(glb_content), 'valid': False, 'error': str(e)}
