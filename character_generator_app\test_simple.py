#!/usr/bin/env python3
"""Simple test without hanging imports"""

import sys
import os
sys.path.append('backend')

print("🧪 Simple Import Test")
print("=" * 20)

# Test 1: Basic Flask
try:
    from flask import Flask
    print("✅ Flask imported")
except Exception as e:
    print(f"❌ Flask failed: {e}")

# Test 2: Requests
try:
    import requests
    print("✅ Requests imported")
except Exception as e:
    print(f"❌ Requests failed: {e}")

# Test 3: Check if websocket-client is available
try:
    import websocket
    print("✅ WebSocket client imported")
except Exception as e:
    print(f"❌ WebSocket client failed: {e}")

# Test 4: Check if aiohttp is available
try:
    import aiohttp
    print("✅ aiohttp imported")
except Exception as e:
    print(f"❌ aiohttp failed: {e}")

print("\n🔍 Checking file existence:")
files_to_check = [
    'backend/comfyui_client.py',
    'backend/huggingface_client.py', 
    'backend/comfyui_workflows.py',
    'backend/config.py',
    'backend/app.py'
]

for file_path in files_to_check:
    if os.path.exists(file_path):
        print(f"✅ {file_path} exists")
    else:
        print(f"❌ {file_path} missing")

print("\n✅ Simple test complete")
