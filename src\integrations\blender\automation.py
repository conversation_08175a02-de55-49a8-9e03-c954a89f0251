"""
Blender automation module for 3D model processing.
"""

import os
import subprocess
import tempfile
import time
import json
import base64

class BlenderAutomation:
    """
    Automation class for Blender operations.
    
    This class provides methods to automate various Blender operations
    for 3D model processing, such as:
    - Importing and exporting models
    - Applying textures
    - Optimizing meshes
    - Rendering previews
    """
    
    def __init__(self, blender_path=None):
        """
        Initialize the Blender automation.
        
        Args:
            blender_path: Path to the Blender executable (default: from environment variable)
        """
        self.blender_path = blender_path or os.environ.get('BLENDER_PATH', 'blender')
        
        # Create scripts directory if it doesn't exist
        self.scripts_dir = os.path.join(os.path.dirname(__file__), 'scripts')
        os.makedirs(self.scripts_dir, exist_ok=True)
        
        # Create the Blender scripts if they don't exist
        self._create_blender_scripts()
    
    def _create_blender_scripts(self):
        """Create Blender Python scripts for automation."""
        # Script for processing a model
        process_script = """
import bpy
import sys
import os
import json

# Get arguments
args = sys.argv[sys.argv.index('--') + 1:]
params = json.loads(args[0])

mesh_path = params['mesh_path']
texture_paths = params['texture_paths']
output_path = params['output_path']
optimize = params['optimize']

# Clear default scene
bpy.ops.wm.read_factory_settings(use_empty=True)

# Import the model
file_ext = os.path.splitext(mesh_path)[1].lower()
if file_ext == '.obj':
    bpy.ops.import_scene.obj(filepath=mesh_path)
elif file_ext == '.fbx':
    bpy.ops.import_scene.fbx(filepath=mesh_path)
elif file_ext == '.glb' or file_ext == '.gltf':
    bpy.ops.import_scene.gltf(filepath=mesh_path)
else:
    print(f"Unsupported file format: {file_ext}")
    sys.exit(1)

# Get the imported object
obj = bpy.context.selected_objects[0]
bpy.context.view_layer.objects.active = obj

# Apply textures if provided
if texture_paths:
    # Create a new material
    mat = bpy.data.materials.new(name="Material")
    mat.use_nodes = True
    nodes = mat.node_tree.nodes
    
    # Clear default nodes
    for node in nodes:
        nodes.remove(node)
    
    # Create PBR shader nodes
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Link principled shader to output
    links = mat.node_tree.links
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Load textures
    for texture_path in texture_paths:
        texture_name = os.path.basename(texture_path).split('.')[0].lower()
        tex_image = nodes.new(type='ShaderNodeTexImage')
        tex_image.image = bpy.data.images.load(texture_path)
        
        # Connect texture to appropriate input
        if 'diffuse' in texture_name or 'albedo' in texture_name:
            links.new(tex_image.outputs['Color'], principled.inputs['Base Color'])
        elif 'normal' in texture_name:
            normal_map = nodes.new(type='ShaderNodeNormalMap')
            links.new(tex_image.outputs['Color'], normal_map.inputs['Color'])
            links.new(normal_map.outputs['Normal'], principled.inputs['Normal'])
        elif 'roughness' in texture_name:
            links.new(tex_image.outputs['Color'], principled.inputs['Roughness'])
        elif 'metallic' in texture_name:
            links.new(tex_image.outputs['Color'], principled.inputs['Metallic'])
    
    # Assign material to object
    if obj.data.materials:
        obj.data.materials[0] = mat
    else:
        obj.data.materials.append(mat)

# Optimize mesh if requested
if optimize:
    # Select the object
    bpy.ops.object.select_all(action='DESELECT')
    obj.select_set(True)
    bpy.context.view_layer.objects.active = obj
    
    # Enter edit mode
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Remove doubles
    bpy.ops.mesh.remove_doubles(threshold=0.001)
    
    # Recalculate normals
    bpy.ops.mesh.normals_make_consistent(inside=False)
    
    # Return to object mode
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Add a decimate modifier to reduce polygon count
    decimate = obj.modifiers.new(name="Decimate", type='DECIMATE')
    decimate.ratio = 0.5  # Reduce to 50% of original poly count
    bpy.ops.object.modifier_apply(modifier="Decimate")

# Export the processed model
os.makedirs(os.path.dirname(output_path), exist_ok=True)
file_ext = os.path.splitext(output_path)[1].lower()
if file_ext == '.obj':
    bpy.ops.export_scene.obj(filepath=output_path, use_selection=True)
elif file_ext == '.fbx':
    bpy.ops.export_scene.fbx(filepath=output_path, use_selection=True)
elif file_ext == '.glb' or file_ext == '.gltf':
    bpy.ops.export_scene.gltf(filepath=output_path, use_selection=True)
else:
    # Default to GLB if extension not specified
    output_path = os.path.splitext(output_path)[0] + '.glb'
    bpy.ops.export_scene.gltf(filepath=output_path, use_selection=True)

print(f"Model processed and saved to: {output_path}")
"""
        
        # Script for rendering a preview
        render_script = """
import bpy
import sys
import os
import json
import base64

# Get arguments
args = sys.argv[sys.argv.index('--') + 1:]
model_path = args[0]
output_path = args[1] if len(args) > 1 else None

# Clear default scene
bpy.ops.wm.read_factory_settings(use_empty=True)

# Import the model
file_ext = os.path.splitext(model_path)[1].lower()
if file_ext == '.obj':
    bpy.ops.import_scene.obj(filepath=model_path)
elif file_ext == '.fbx':
    bpy.ops.import_scene.fbx(filepath=model_path)
elif file_ext == '.glb' or file_ext == '.gltf':
    bpy.ops.import_scene.gltf(filepath=model_path)
else:
    print(f"Unsupported file format: {file_ext}")
    sys.exit(1)

# Set up scene for rendering
scene = bpy.context.scene
scene.render.engine = 'CYCLES'
scene.render.film_transparent = True
scene.render.resolution_x = 800
scene.render.resolution_y = 800
scene.render.image_settings.file_format = 'PNG'

# Add lighting
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 5))
sun = bpy.context.object
sun.data.energy = 2.0

# Set up camera
bpy.ops.object.camera_add(location=(2, -2, 2))
camera = bpy.context.object
camera.rotation_euler = (0.9, 0, 0.8)
scene.camera = camera

# Frame the object
for obj in bpy.context.visible_objects:
    if obj.type == 'MESH':
        bpy.context.view_layer.objects.active = obj
        break

bpy.ops.object.select_all(action='DESELECT')
bpy.context.view_layer.objects.active.select_set(True)
bpy.ops.view3d.camera_to_view_selected()

# Render the image
if output_path:
    scene.render.filepath = output_path
    bpy.ops.render.render(write_still=True)
    print(f"Preview rendered to: {output_path}")
else:
    # Render to a temporary file
    temp_file = os.path.join(os.environ.get('TEMP', '/tmp'), 'preview.png')
    scene.render.filepath = temp_file
    bpy.ops.render.render(write_still=True)
    
    # Read the file and encode as base64
    with open(temp_file, 'rb') as f:
        image_data = f.read()
    
    # Print base64 encoded image
    print(base64.b64encode(image_data).decode('utf-8'))
    
    # Clean up
    os.unlink(temp_file)
"""
        
        # Save the scripts
        with open(os.path.join(self.scripts_dir, 'process_model.py'), 'w') as f:
            f.write(process_script)
        
        with open(os.path.join(self.scripts_dir, 'render_preview.py'), 'w') as f:
            f.write(render_script)
    
    def process_model(self, mesh_path, texture_paths=None, output_path=None, optimize=True):
        """
        Process a 3D model in Blender.
        
        Args:
            mesh_path: Path to the input mesh file
            texture_paths: List of paths to texture files (default: None)
            output_path: Path to save the processed model (default: auto-generated)
            optimize: Whether to optimize the mesh (default: True)
            
        Returns:
            str: Path to the processed model file
        """
        # Generate output path if not provided
        if output_path is None:
            output_dir = os.path.dirname(mesh_path)
            filename = f"processed_{os.path.basename(mesh_path)}"
            output_path = os.path.join(output_dir, filename)
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Prepare parameters
        params = {
            'mesh_path': mesh_path,
            'texture_paths': texture_paths or [],
            'output_path': output_path,
            'optimize': optimize
        }
        
        # Run Blender with the processing script
        script_path = os.path.join(self.scripts_dir, 'process_model.py')
        cmd = [
            self.blender_path,
            '--background',
            '--python', script_path,
            '--',
            json.dumps(params)
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(result.stdout)
            return output_path
        except subprocess.CalledProcessError as e:
            print(f"Error processing model: {e}")
            print(f"Blender output: {e.stdout}")
            print(f"Blender error: {e.stderr}")
            raise
    
    def render_preview(self, model_path, output_path=None):
        """
        Render a preview image of a 3D model.
        
        Args:
            model_path: Path to the 3D model file
            output_path: Path to save the preview image (default: None, returns base64)
            
        Returns:
            str: Path to the preview image or base64 encoded image data
        """
        # Run Blender with the rendering script
        script_path = os.path.join(self.scripts_dir, 'render_preview.py')
        cmd = [
            self.blender_path,
            '--background',
            '--python', script_path,
            '--',
            model_path
        ]
        
        if output_path:
            cmd.append(output_path)
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            
            if output_path:
                return output_path
            else:
                # Extract base64 encoded image from output
                return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"Error rendering preview: {e}")
            print(f"Blender output: {e.stdout}")
            print(f"Blender error: {e.stderr}")
            raise
