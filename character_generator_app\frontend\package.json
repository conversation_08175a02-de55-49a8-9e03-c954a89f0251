{"name": "character-generator-frontend", "version": "1.0.0", "description": "3D Character Generator Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.3.0", "axios": "^1.4.0", "socket.io-client": "^4.7.2", "three": "^0.155.0", "@react-three/fiber": "^8.13.5", "@react-three/drei": "^9.80.0", "react-dropzone": "^14.2.3", "react-toastify": "^9.1.3", "styled-components": "^6.0.7", "framer-motion": "^10.16.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}