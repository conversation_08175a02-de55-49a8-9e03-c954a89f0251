
import bpy
import sys
import os
import json
import tempfile

# Get arguments
args = sys.argv[sys.argv.index('--') + 1:]
params = json.loads(args[0])

model_path = params.get('model_path', '')
edit_mode = params.get('edit_mode', 'sculpt')
output_path = params.get('output_path', '')
auto_save = params.get('auto_save', True)
auto_save_interval = params.get('auto_save_interval', 300)  # 5 minutes

# Clear default scene
bpy.ops.wm.read_factory_settings(use_empty=True)

# Import the model if provided
if model_path and os.path.exists(model_path):
    file_ext = os.path.splitext(model_path)[1].lower()
    if file_ext == '.obj':
        bpy.ops.import_scene.obj(filepath=model_path)
    elif file_ext == '.fbx':
        bpy.ops.import_scene.fbx(filepath=model_path)
    elif file_ext == '.glb' or file_ext == '.gltf':
        bpy.ops.import_scene.gltf(filepath=model_path)
    elif file_ext == '.blend':
        bpy.ops.wm.open_mainfile(filepath=model_path)
    else:
        print(f"Unsupported file format: {file_ext}")

# Set up auto-save if enabled
if auto_save:
    bpy.context.preferences.filepaths.use_auto_save = True
    bpy.context.preferences.filepaths.auto_save_time = auto_save_interval // 60  # Convert to minutes

# Set up the UI for the specified edit mode
if edit_mode == 'sculpt':
    # Switch to Sculpt mode
    if bpy.context.active_object and bpy.context.active_object.type == 'MESH':
        bpy.ops.object.mode_set(mode='SCULPT')
        
        # Set up sculpt tools
        bpy.context.tool_settings.sculpt.use_symmetry_x = True
        bpy.context.tool_settings.sculpt.detail_size = 8
        
        # Set up a basic sculpt brush
        brush = bpy.context.tool_settings.sculpt.brush
        if brush:
            brush.strength = 0.5
            brush.size = 35

elif edit_mode == 'edit':
    # Switch to Edit mode
    if bpy.context.active_object and bpy.context.active_object.type == 'MESH':
        bpy.ops.object.mode_set(mode='EDIT')
        
        # Set up edit tools
        bpy.context.tool_settings.mesh_select_mode = (True, True, True)  # Vertices, Edges, Faces

elif edit_mode == 'texture':
    # Switch to Texture Paint mode
    if bpy.context.active_object and bpy.context.active_object.type == 'MESH':
        bpy.ops.object.mode_set(mode='TEXTURE_PAINT')
        
        # Set up texture paint tools
        if not bpy.context.active_object.data.uv_layers:
            bpy.ops.mesh.uv_texture_add()

# Set up a handler to save the file when Blender closes
if output_path:
    def save_on_exit():
        # Export the model
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        file_ext = os.path.splitext(output_path)[1].lower()
        
        # Select all objects
        bpy.ops.object.select_all(action='SELECT')
        
        if file_ext == '.obj':
            bpy.ops.export_scene.obj(filepath=output_path, use_selection=True)
        elif file_ext == '.fbx':
            bpy.ops.export_scene.fbx(filepath=output_path, use_selection=True)
        elif file_ext == '.glb' or file_ext == '.gltf':
            bpy.ops.export_scene.gltf(filepath=output_path, use_selection=True)
        elif file_ext == '.blend':
            bpy.ops.wm.save_as_mainfile(filepath=output_path)
        else:
            # Default to GLB if extension not specified
            output_path_glb = os.path.splitext(output_path)[0] + '.glb'
            bpy.ops.export_scene.gltf(filepath=output_path_glb, use_selection=True)
    
    # Register the save handler
    bpy.app.handlers.save_pre.append(save_on_exit)

print("Blender direct editing setup complete.")
print(f"Model: {model_path}")
print(f"Edit Mode: {edit_mode}")
print(f"Output Path: {output_path}")
