"""
Script to download mickmumpitz's ComfyUI workflows.

This script provides instructions and assistance for downloading
mickmumpitz's ComfyUI workflows from Patreon.
"""

import os
import sys
import webbrowser
import argparse
import json
from pathlib import Path

def create_workflows_directory():
    """Create the workflows directory if it doesn't exist."""
    workflows_dir = Path('workflows/mickmumpitz')
    workflows_dir.mkdir(parents=True, exist_ok=True)
    return workflows_dir

def open_patreon_pages():
    """Open mickmumpitz's Patreon pages with the workflows."""
    print("Opening mickmumpitz's Patreon pages with the workflows...")
    
    # 3D Animation Rendering workflow
    webbrowser.open('https://www.patreon.com/posts/free-workflows-104795004')
    
    # Character Generation workflow
    webbrowser.open('https://www.patreon.com/posts/free-workflows-113743435')
    
    print("\nPlease download the following workflow files from these pages:")
    print("1. 200524_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v3.json")
    print("2. 200524_Mickmumpitz_3D-RENDERING_VIDEO_SD15LCM_SIMPLE_v3.json")
    print("3. 241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json")
    print("4. 241007_MICKMUMPITZ_FLUX+LORA.json")

def open_installation_guides():
    """Open mickmumpitz's installation guides."""
    print("Opening mickmumpitz's installation guides...")
    
    # 3D Animation Rendering guide
    webbrowser.open('https://docs.google.com/document/d/1lemaWcaSAxn1YsRSkRcCXAT9Nho-tIObcoijPrlDfSs/edit?usp=sharing')
    
    # Character Generation guide
    webbrowser.open('https://docs.google.com/document/d/1PHYMpXqfNKj9dQIMVpXAg7R4FjNFIINok09L9heQTnM/edit?usp=sharing')

def check_workflow_files(workflows_dir):
    """Check if workflow files exist in the workflows directory."""
    expected_files = [
        '200524_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v3.json',
        '200524_Mickmumpitz_3D-RENDERING_VIDEO_SD15LCM_SIMPLE_v3.json',
        '241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json',
        '241007_MICKMUMPITZ_FLUX+LORA.json'
    ]
    
    missing_files = []
    for file in expected_files:
        if not (workflows_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("\nThe following workflow files are missing:")
        for file in missing_files:
            print(f"- {file}")
        print("\nPlease download these files from mickmumpitz's Patreon and place them in:")
        print(f"{workflows_dir.absolute()}")
        return False
    
    print("\nAll workflow files are present!")
    return True

def create_workflow_info_file(workflows_dir):
    """Create a JSON file with information about the workflows."""
    info = {
        "workflows": [
            {
                "name": "3D Rendering Image",
                "file": "200524_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v3.json",
                "description": "Renders 3D scenes as images using SDXL",
                "source": "https://www.patreon.com/posts/free-workflows-104795004",
                "guide": "https://docs.google.com/document/d/1lemaWcaSAxn1YsRSkRcCXAT9Nho-tIObcoijPrlDfSs/edit?usp=sharing"
            },
            {
                "name": "3D Rendering Video",
                "file": "200524_Mickmumpitz_3D-RENDERING_VIDEO_SD15LCM_SIMPLE_v3.json",
                "description": "Renders 3D animations as videos using SD 1.5 LCM",
                "source": "https://www.patreon.com/posts/free-workflows-104795004",
                "guide": "https://docs.google.com/document/d/1lemaWcaSAxn1YsRSkRcCXAT9Nho-tIObcoijPrlDfSs/edit?usp=sharing"
            },
            {
                "name": "Character Sheet",
                "file": "241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json",
                "description": "Generates consistent character sheets using FLUX",
                "source": "https://www.patreon.com/posts/free-workflows-113743435",
                "guide": "https://docs.google.com/document/d/1PHYMpXqfNKj9dQIMVpXAg7R4FjNFIINok09L9heQTnM/edit?usp=sharing"
            },
            {
                "name": "FLUX + LoRA",
                "file": "241007_MICKMUMPITZ_FLUX+LORA.json",
                "description": "Trains custom LoRAs for character consistency",
                "source": "https://www.patreon.com/posts/free-workflows-113743435",
                "guide": "https://docs.google.com/document/d/1PHYMpXqfNKj9dQIMVpXAg7R4FjNFIINok09L9heQTnM/edit?usp=sharing"
            }
        ],
        "author": "mickmumpitz",
        "author_url": "https://www.patreon.com/Mickmumpitz",
        "youtube": "https://www.youtube.com/@mickmumpitz"
    }
    
    with open(workflows_dir / 'workflow_info.json', 'w') as f:
        json.dump(info, f, indent=4)
    
    print(f"\nCreated workflow info file: {workflows_dir / 'workflow_info.json'}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Download mickmumpitz\'s ComfyUI workflows')
    parser.add_argument('--check-only', action='store_true', help='Only check if workflow files exist')
    parser.add_argument('--open-guides', action='store_true', help='Open installation guides')
    args = parser.parse_args()
    
    # Create workflows directory
    workflows_dir = create_workflows_directory()
    
    # Check if workflow files exist
    files_exist = check_workflow_files(workflows_dir)
    
    if not files_exist and not args.check_only:
        # Open Patreon pages to download workflows
        open_patreon_pages()
    
    if args.open_guides or not files_exist:
        # Open installation guides
        open_installation_guides()
    
    # Create workflow info file
    create_workflow_info_file(workflows_dir)
    
    print("\nNext steps:")
    print("1. Download the workflow files from mickmumpitz's Patreon")
    print("2. Place them in the workflows/mickmumpitz directory")
    print("3. Follow the installation guides to set up the required ComfyUI nodes")
    print("4. Use the workflows in your project")

if __name__ == '__main__':
    main()
