@echo off
echo 🎮 3D Character Generator - System Startup
echo ================================================

echo.
echo 🔍 Checking system status...

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found
    pause
    exit /b 1
)
echo ✅ Python found

REM Check if backend directory exists
if not exist "backend\app.py" (
    echo ❌ Backend not found
    pause
    exit /b 1
)
echo ✅ Backend found

REM Check if frontend exists
if not exist "simple_frontend.html" (
    echo ❌ Frontend not found
    pause
    exit /b 1
)
echo ✅ Frontend found

echo.
echo 🚀 Starting backend server...
cd backend
start "3D Character Generator Backend" python app.py

echo.
echo ⏳ Waiting for server to start...
timeout /t 5 /nobreak > nul

echo.
echo 🌐 Opening frontend...
cd ..
start "" "simple_frontend.html"

echo.
echo 🎉 System started successfully!
echo ================================
echo 📱 Frontend: simple_frontend.html
echo 🔧 Backend: http://localhost:5000
echo 🎮 ComfyUI: http://localhost:8188 (if running)
echo.
echo 💡 Tips:
echo - Start ComfyUI for AI-powered generation
echo - System works without ComfyUI (procedural mode)
echo - Check browser console for any errors
echo.
echo Press any key to exit...
pause > nul
