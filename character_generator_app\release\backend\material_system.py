#!/usr/bin/env python3
"""
Material system for realistic character rendering
"""

import numpy as np

class MaterialSystem:
    """Create and manage materials for 3D characters"""
    
    def __init__(self):
        self.material_presets = {
            'realistic_skin': {
                'base_roughness': 0.6,
                'metallic_factor': 0.0,
                'subsurface_factor': 0.3,
                'subsurface_color': [0.9, 0.7, 0.6]
            },
            'cartoon_skin': {
                'base_roughness': 0.8,
                'metallic_factor': 0.0,
                'subsurface_factor': 0.1,
                'subsurface_color': [1.0, 0.8, 0.7]
            },
            'fabric': {
                'base_roughness': 0.9,
                'metallic_factor': 0.0,
                'subsurface_factor': 0.0
            },
            'metal': {
                'base_roughness': 0.2,
                'metallic_factor': 0.9,
                'subsurface_factor': 0.0
            },
            'leather': {
                'base_roughness': 0.7,
                'metallic_factor': 0.1,
                'subsurface_factor': 0.0
            }
        }
    
    def create_character_materials(self, face_analysis, character_params, style='realistic'):
        """Create materials based on face analysis and character parameters"""
        try:
            materials = []
            
            # Primary skin material
            skin_material = self.create_skin_material(face_analysis, style)
            materials.append(skin_material)
            
            # Secondary materials (clothing, accessories)
            clothing_material = self.create_clothing_material(character_params, style)
            materials.append(clothing_material)
            
            # Hair material (if needed)
            hair_material = self.create_hair_material(face_analysis, style)
            materials.append(hair_material)
            
            # Eye material (if needed)
            eye_material = self.create_eye_material(face_analysis, style)
            materials.append(eye_material)
            
            return materials
            
        except Exception as e:
            print(f"Material creation error: {e}")
            return self.create_fallback_materials()
    
    def create_skin_material(self, face_analysis, style='realistic'):
        """Create skin material based on face analysis"""
        try:
            # Extract skin color from face analysis
            skin_tone = face_analysis.get('skin_tone', {})
            
            if skin_tone.get('rgb'):
                # Convert RGB (0-255) to normalized (0-1)
                base_color = [c/255.0 for c in skin_tone['rgb']] + [1.0]
            else:
                # Default skin color
                base_color = [0.8, 0.6, 0.4, 1.0]
            
            # Get material preset
            preset = self.material_presets.get(f'{style}_skin', self.material_presets['realistic_skin'])
            
            # Create skin material
            material = {
                "name": f"{style}_skin",
                "pbrMetallicRoughness": {
                    "baseColorFactor": base_color,
                    "metallicFactor": preset['metallic_factor'],
                    "roughnessFactor": preset['base_roughness']
                },
                "emissiveFactor": [0.0, 0.0, 0.0]
            }
            
            # Add subsurface scattering for realistic skin
            if style == 'realistic' and preset.get('subsurface_factor', 0) > 0:
                material["extensions"] = {
                    "KHR_materials_subsurface": {
                        "subsurfaceFactor": preset['subsurface_factor'],
                        "subsurfaceColorFactor": preset['subsurface_color'] + [1.0]
                    }
                }
            
            return material
            
        except Exception as e:
            print(f"Skin material creation error: {e}")
            return self.create_default_skin_material()
    
    def create_clothing_material(self, character_params, style='realistic'):
        """Create clothing material"""
        try:
            # Get accent color from character parameters
            colors = character_params.get('colors', {})
            accent_color = colors.get('accent_color', [0.2, 0.4, 0.8])
            
            # Normalize color if needed
            if max(accent_color) > 1.0:
                accent_color = [c/255.0 for c in accent_color]
            
            base_color = accent_color + [1.0]
            
            # Determine material type based on character type
            character_name = character_params.get('name', 'humanoid')
            
            if 'robot' in character_name.lower():
                preset = self.material_presets['metal']
                material_name = f"{style}_metal"
            elif 'warrior' in character_name.lower():
                preset = self.material_presets['leather']
                material_name = f"{style}_leather"
            else:
                preset = self.material_presets['fabric']
                material_name = f"{style}_fabric"
            
            material = {
                "name": material_name,
                "pbrMetallicRoughness": {
                    "baseColorFactor": base_color,
                    "metallicFactor": preset['metallic_factor'],
                    "roughnessFactor": preset['base_roughness']
                },
                "emissiveFactor": [0.0, 0.0, 0.0]
            }
            
            return material
            
        except Exception as e:
            print(f"Clothing material creation error: {e}")
            return self.create_default_clothing_material()
    
    def create_hair_material(self, face_analysis, style='realistic'):
        """Create hair material"""
        try:
            # Try to extract hair color from color palette
            color_palette = face_analysis.get('color_palette', {})
            dominant_colors = color_palette.get('dominant_colors', [])
            
            # Use darkest color as hair color
            if dominant_colors:
                hair_color = min(dominant_colors, key=lambda c: sum(c))
                if max(hair_color) > 1.0:
                    hair_color = [c/255.0 for c in hair_color]
            else:
                hair_color = [0.3, 0.2, 0.1]  # Default brown hair
            
            base_color = hair_color + [1.0]
            
            material = {
                "name": f"{style}_hair",
                "pbrMetallicRoughness": {
                    "baseColorFactor": base_color,
                    "metallicFactor": 0.1,
                    "roughnessFactor": 0.8
                },
                "emissiveFactor": [0.0, 0.0, 0.0]
            }
            
            return material
            
        except Exception as e:
            print(f"Hair material creation error: {e}")
            return self.create_default_hair_material()
    
    def create_eye_material(self, face_analysis, style='realistic'):
        """Create eye material"""
        try:
            # Default eye color
            eye_color = [0.2, 0.3, 0.4]  # Default blue-gray
            
            # Try to extract eye color if available
            # (This would require more advanced eye color detection)
            
            base_color = eye_color + [1.0]
            
            material = {
                "name": f"{style}_eyes",
                "pbrMetallicRoughness": {
                    "baseColorFactor": base_color,
                    "metallicFactor": 0.0,
                    "roughnessFactor": 0.1  # Eyes are quite shiny
                },
                "emissiveFactor": [0.0, 0.0, 0.0]
            }
            
            return material
            
        except Exception as e:
            print(f"Eye material creation error: {e}")
            return self.create_default_eye_material()
    
    def create_default_skin_material(self):
        """Create default skin material"""
        return {
            "name": "default_skin",
            "pbrMetallicRoughness": {
                "baseColorFactor": [0.8, 0.6, 0.4, 1.0],
                "metallicFactor": 0.0,
                "roughnessFactor": 0.6
            },
            "emissiveFactor": [0.0, 0.0, 0.0]
        }
    
    def create_default_clothing_material(self):
        """Create default clothing material"""
        return {
            "name": "default_clothing",
            "pbrMetallicRoughness": {
                "baseColorFactor": [0.2, 0.4, 0.8, 1.0],
                "metallicFactor": 0.1,
                "roughnessFactor": 0.8
            },
            "emissiveFactor": [0.0, 0.0, 0.0]
        }
    
    def create_default_hair_material(self):
        """Create default hair material"""
        return {
            "name": "default_hair",
            "pbrMetallicRoughness": {
                "baseColorFactor": [0.3, 0.2, 0.1, 1.0],
                "metallicFactor": 0.1,
                "roughnessFactor": 0.8
            },
            "emissiveFactor": [0.0, 0.0, 0.0]
        }
    
    def create_default_eye_material(self):
        """Create default eye material"""
        return {
            "name": "default_eyes",
            "pbrMetallicRoughness": {
                "baseColorFactor": [0.2, 0.3, 0.4, 1.0],
                "metallicFactor": 0.0,
                "roughnessFactor": 0.1
            },
            "emissiveFactor": [0.0, 0.0, 0.0]
        }
    
    def create_fallback_materials(self):
        """Create fallback materials when everything fails"""
        return [
            self.create_default_skin_material(),
            self.create_default_clothing_material()
        ]
    
    def adjust_material_for_lighting(self, material, lighting_type='studio'):
        """Adjust material properties for different lighting conditions"""
        try:
            adjusted_material = material.copy()
            
            if lighting_type == 'outdoor':
                # Increase roughness for outdoor lighting
                pbr = adjusted_material.get('pbrMetallicRoughness', {})
                current_roughness = pbr.get('roughnessFactor', 0.5)
                pbr['roughnessFactor'] = min(1.0, current_roughness * 1.2)
                
            elif lighting_type == 'indoor':
                # Decrease roughness for indoor lighting
                pbr = adjusted_material.get('pbrMetallicRoughness', {})
                current_roughness = pbr.get('roughnessFactor', 0.5)
                pbr['roughnessFactor'] = max(0.1, current_roughness * 0.8)
                
            elif lighting_type == 'dramatic':
                # Increase contrast for dramatic lighting
                pbr = adjusted_material.get('pbrMetallicRoughness', {})
                current_metallic = pbr.get('metallicFactor', 0.0)
                pbr['metallicFactor'] = min(1.0, current_metallic * 1.5)
            
            return adjusted_material
            
        except Exception as e:
            print(f"Material adjustment error: {e}")
            return material
    
    def create_material_variants(self, base_material, variant_count=3):
        """Create color variants of a base material"""
        try:
            variants = []
            base_color = base_material.get('pbrMetallicRoughness', {}).get('baseColorFactor', [0.5, 0.5, 0.5, 1.0])
            
            for i in range(variant_count):
                variant = base_material.copy()
                
                # Create color variations
                hue_shift = (i * 120) % 360  # Shift hue by 120 degrees
                new_color = self.shift_hue(base_color[:3], hue_shift) + [base_color[3]]
                
                variant['pbrMetallicRoughness']['baseColorFactor'] = new_color
                variant['name'] = f"{base_material['name']}_variant_{i+1}"
                
                variants.append(variant)
            
            return variants
            
        except Exception as e:
            print(f"Material variant creation error: {e}")
            return [base_material]
    
    def shift_hue(self, rgb, hue_shift_degrees):
        """Shift the hue of an RGB color"""
        try:
            # Convert RGB to HSV
            r, g, b = rgb
            max_val = max(r, g, b)
            min_val = min(r, g, b)
            diff = max_val - min_val
            
            # Calculate hue
            if diff == 0:
                h = 0
            elif max_val == r:
                h = (60 * ((g - b) / diff) + 360) % 360
            elif max_val == g:
                h = (60 * ((b - r) / diff) + 120) % 360
            else:
                h = (60 * ((r - g) / diff) + 240) % 360
            
            # Calculate saturation
            s = 0 if max_val == 0 else diff / max_val
            
            # Value
            v = max_val
            
            # Shift hue
            h = (h + hue_shift_degrees) % 360
            
            # Convert back to RGB
            c = v * s
            x = c * (1 - abs((h / 60) % 2 - 1))
            m = v - c
            
            if 0 <= h < 60:
                r_prime, g_prime, b_prime = c, x, 0
            elif 60 <= h < 120:
                r_prime, g_prime, b_prime = x, c, 0
            elif 120 <= h < 180:
                r_prime, g_prime, b_prime = 0, c, x
            elif 180 <= h < 240:
                r_prime, g_prime, b_prime = 0, x, c
            elif 240 <= h < 300:
                r_prime, g_prime, b_prime = x, 0, c
            else:
                r_prime, g_prime, b_prime = c, 0, x
            
            return [r_prime + m, g_prime + m, b_prime + m]
            
        except Exception as e:
            print(f"Hue shift error: {e}")
            return rgb
    
    def get_material_summary(self, materials):
        """Get summary of materials"""
        summary = {
            'material_count': len(materials),
            'material_types': [],
            'has_subsurface': False,
            'has_metallic': False
        }
        
        for material in materials:
            summary['material_types'].append(material.get('name', 'unknown'))
            
            # Check for subsurface scattering
            if 'extensions' in material and 'KHR_materials_subsurface' in material['extensions']:
                summary['has_subsurface'] = True
            
            # Check for metallic materials
            pbr = material.get('pbrMetallicRoughness', {})
            if pbr.get('metallicFactor', 0) > 0.5:
                summary['has_metallic'] = True
        
        return summary
