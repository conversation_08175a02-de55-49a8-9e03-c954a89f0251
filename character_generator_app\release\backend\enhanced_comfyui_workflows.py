#!/usr/bin/env python3
"""
Enhanced ComfyUI workflow creation for mickmumpitz integration
"""

import os
import json

class EnhancedWorkflowCreator:
    """Create enhanced ComfyUI workflows for character generation"""
    
    def __init__(self, comfyui_path):
        self.comfyui_path = comfyui_path
        self.workflows_dir = os.path.join(comfyui_path, "workflows")
        os.makedirs(self.workflows_dir, exist_ok=True)
    
    def create_all_workflows(self):
        """Create all enhanced workflows"""
        print("🎨 Creating enhanced mickmumpitz workflows...")
        
        workflows_created = 0
        
        try:
            # 1. Character Sheet Workflow
            if self.create_character_sheet_workflow():
                workflows_created += 1
            
            # 2. 3D Rendering Workflow
            if self.create_3d_rendering_workflow():
                workflows_created += 1
            
            # 3. Face Enhancement Workflow
            if self.create_face_enhancement_workflow():
                workflows_created += 1
            
            # 4. Style Transfer Workflow
            if self.create_style_transfer_workflow():
                workflows_created += 1
            
            # 5. Upscale Enhancement Workflow
            if self.create_upscale_workflow():
                workflows_created += 1
            
            # 6. Character Variations Workflow
            if self.create_variations_workflow():
                workflows_created += 1
            
            print(f"✅ Created {workflows_created} enhanced workflows")
            return workflows_created
            
        except Exception as e:
            print(f"❌ Workflow creation error: {e}")
            return 0
    
    def create_character_sheet_workflow(self):
        """Create enhanced character sheet workflow"""
        try:
            workflow = {
                "workflow_name": "Enhanced Character Sheet Generator",
                "description": "Generate detailed character sheets with multiple views using mickmumpitz techniques",
                "version": "2.0",
                "nodes": {
                    "1": {
                        "inputs": {
                            "text": "character sheet, multiple views, front view, side view, back view, reference sheet, detailed anatomy, professional quality, clean background",
                            "clip": ["4", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "Positive Prompt"}
                    },
                    "2": {
                        "inputs": {
                            "samples": ["3", 0],
                            "vae": ["4", 2]
                        },
                        "class_type": "VAEDecode",
                        "_meta": {"title": "VAE Decode"}
                    },
                    "3": {
                        "inputs": {
                            "seed": 42,
                            "steps": 30,
                            "cfg": 8.0,
                            "sampler_name": "dpmpp_2m_sde",
                            "scheduler": "karras",
                            "denoise": 1.0,
                            "model": ["4", 0],
                            "positive": ["1", 0],
                            "negative": ["5", 0],
                            "latent_image": ["6", 0]
                        },
                        "class_type": "KSampler",
                        "_meta": {"title": "KSampler"}
                    },
                    "4": {
                        "inputs": {
                            "ckpt_name": "sd_xl_base_1.0.safetensors"
                        },
                        "class_type": "CheckpointLoaderSimple",
                        "_meta": {"title": "Load Checkpoint"}
                    },
                    "5": {
                        "inputs": {
                            "text": "blurry, low quality, distorted, nsfw, nude, bad anatomy, deformed, ugly, disfigured",
                            "clip": ["4", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "Negative Prompt"}
                    },
                    "6": {
                        "inputs": {
                            "width": 1024,
                            "height": 1024,
                            "batch_size": 1
                        },
                        "class_type": "EmptyLatentImage",
                        "_meta": {"title": "Empty Latent Image"}
                    },
                    "7": {
                        "inputs": {
                            "images": ["2", 0]
                        },
                        "class_type": "SaveImage",
                        "_meta": {"title": "Save Character Sheet"}
                    }
                }
            }
            
            filename = "mickmumpitz_character_sheet.json"
            filepath = os.path.join(self.workflows_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(workflow, f, indent=2)
            
            print(f"✅ Created character sheet workflow: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Character sheet workflow creation failed: {e}")
            return False
    
    def create_3d_rendering_workflow(self):
        """Create 3D rendering workflow"""
        try:
            workflow = {
                "workflow_name": "3D Character Rendering",
                "description": "Generate 3D-style character renders for reference",
                "version": "2.0",
                "nodes": {
                    "1": {
                        "inputs": {
                            "text": "3D rendered character, high quality, detailed modeling, professional lighting, clean background, realistic materials, game character",
                            "clip": ["4", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "3D Positive Prompt"}
                    },
                    "2": {
                        "inputs": {
                            "samples": ["3", 0],
                            "vae": ["4", 2]
                        },
                        "class_type": "VAEDecode",
                        "_meta": {"title": "VAE Decode"}
                    },
                    "3": {
                        "inputs": {
                            "seed": 123,
                            "steps": 25,
                            "cfg": 7.5,
                            "sampler_name": "dpmpp_2m",
                            "scheduler": "karras",
                            "denoise": 1.0,
                            "model": ["4", 0],
                            "positive": ["1", 0],
                            "negative": ["5", 0],
                            "latent_image": ["6", 0]
                        },
                        "class_type": "KSampler",
                        "_meta": {"title": "3D KSampler"}
                    },
                    "4": {
                        "inputs": {
                            "ckpt_name": "sd_xl_base_1.0.safetensors"
                        },
                        "class_type": "CheckpointLoaderSimple",
                        "_meta": {"title": "Load Checkpoint"}
                    },
                    "5": {
                        "inputs": {
                            "text": "2D, flat, cartoon, low quality, blurry, distorted, amateur, bad lighting",
                            "clip": ["4", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "3D Negative Prompt"}
                    },
                    "6": {
                        "inputs": {
                            "width": 768,
                            "height": 768,
                            "batch_size": 1
                        },
                        "class_type": "EmptyLatentImage",
                        "_meta": {"title": "3D Latent Image"}
                    },
                    "7": {
                        "inputs": {
                            "images": ["2", 0]
                        },
                        "class_type": "SaveImage",
                        "_meta": {"title": "Save 3D Render"}
                    }
                }
            }
            
            filename = "mickmumpitz_3d_rendering.json"
            filepath = os.path.join(self.workflows_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(workflow, f, indent=2)
            
            print(f"✅ Created 3D rendering workflow: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ 3D rendering workflow creation failed: {e}")
            return False
    
    def create_face_enhancement_workflow(self):
        """Create face enhancement workflow"""
        try:
            workflow = {
                "workflow_name": "Face Enhancement",
                "description": "Enhance face images for better 3D character generation",
                "version": "2.0",
                "nodes": {
                    "1": {
                        "inputs": {
                            "image": "input_image_placeholder"
                        },
                        "class_type": "LoadImage",
                        "_meta": {"title": "Load Face Image"}
                    },
                    "2": {
                        "inputs": {
                            "text": "high quality face, detailed features, clear skin, professional photography, good lighting",
                            "clip": ["5", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "Enhancement Prompt"}
                    },
                    "3": {
                        "inputs": {
                            "text": "blurry, low quality, distorted, bad lighting, noise, artifacts",
                            "clip": ["5", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "Enhancement Negative"}
                    },
                    "4": {
                        "inputs": {
                            "samples": ["6", 0],
                            "vae": ["5", 2]
                        },
                        "class_type": "VAEDecode",
                        "_meta": {"title": "VAE Decode Enhanced"}
                    },
                    "5": {
                        "inputs": {
                            "ckpt_name": "sd_xl_base_1.0.safetensors"
                        },
                        "class_type": "CheckpointLoaderSimple",
                        "_meta": {"title": "Load Enhancement Model"}
                    },
                    "6": {
                        "inputs": {
                            "seed": 456,
                            "steps": 20,
                            "cfg": 6.0,
                            "sampler_name": "euler_a",
                            "scheduler": "normal",
                            "denoise": 0.3,
                            "model": ["5", 0],
                            "positive": ["2", 0],
                            "negative": ["3", 0],
                            "latent_image": ["7", 0]
                        },
                        "class_type": "KSampler",
                        "_meta": {"title": "Enhancement Sampler"}
                    },
                    "7": {
                        "inputs": {
                            "pixels": ["1", 0],
                            "vae": ["5", 2]
                        },
                        "class_type": "VAEEncode",
                        "_meta": {"title": "VAE Encode Input"}
                    },
                    "8": {
                        "inputs": {
                            "images": ["4", 0]
                        },
                        "class_type": "SaveImage",
                        "_meta": {"title": "Save Enhanced Face"}
                    }
                }
            }
            
            filename = "mickmumpitz_face_enhancement.json"
            filepath = os.path.join(self.workflows_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(workflow, f, indent=2)
            
            print(f"✅ Created face enhancement workflow: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Face enhancement workflow creation failed: {e}")
            return False
    
    def create_style_transfer_workflow(self):
        """Create style transfer workflow"""
        try:
            workflow = {
                "workflow_name": "Character Style Transfer",
                "description": "Apply different art styles to characters while preserving facial features",
                "version": "2.0",
                "nodes": {
                    "1": {
                        "inputs": {
                            "image": "input_character_placeholder"
                        },
                        "class_type": "LoadImage",
                        "_meta": {"title": "Load Character Image"}
                    },
                    "2": {
                        "inputs": {
                            "text": "anime style, detailed art, vibrant colors, professional illustration",
                            "clip": ["4", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "Style Prompt"}
                    },
                    "3": {
                        "inputs": {
                            "text": "blurry, low quality, distorted, bad anatomy",
                            "clip": ["4", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "Style Negative"}
                    },
                    "4": {
                        "inputs": {
                            "ckpt_name": "sd_xl_base_1.0.safetensors"
                        },
                        "class_type": "CheckpointLoaderSimple",
                        "_meta": {"title": "Load Style Model"}
                    },
                    "5": {
                        "inputs": {
                            "seed": 789,
                            "steps": 25,
                            "cfg": 7.0,
                            "sampler_name": "dpmpp_2m_sde",
                            "scheduler": "karras",
                            "denoise": 0.6,
                            "model": ["4", 0],
                            "positive": ["2", 0],
                            "negative": ["3", 0],
                            "latent_image": ["6", 0]
                        },
                        "class_type": "KSampler",
                        "_meta": {"title": "Style Transfer Sampler"}
                    },
                    "6": {
                        "inputs": {
                            "pixels": ["1", 0],
                            "vae": ["4", 2]
                        },
                        "class_type": "VAEEncode",
                        "_meta": {"title": "Encode for Style Transfer"}
                    },
                    "7": {
                        "inputs": {
                            "samples": ["5", 0],
                            "vae": ["4", 2]
                        },
                        "class_type": "VAEDecode",
                        "_meta": {"title": "Decode Styled Image"}
                    },
                    "8": {
                        "inputs": {
                            "images": ["7", 0]
                        },
                        "class_type": "SaveImage",
                        "_meta": {"title": "Save Styled Character"}
                    }
                }
            }
            
            filename = "mickmumpitz_style_transfer.json"
            filepath = os.path.join(self.workflows_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(workflow, f, indent=2)
            
            print(f"✅ Created style transfer workflow: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Style transfer workflow creation failed: {e}")
            return False
    
    def create_upscale_workflow(self):
        """Create upscale enhancement workflow"""
        try:
            workflow = {
                "workflow_name": "Image Upscale Enhancement",
                "description": "Upscale and enhance images for better quality",
                "version": "2.0",
                "nodes": {
                    "1": {
                        "inputs": {
                            "image": "input_image_placeholder"
                        },
                        "class_type": "LoadImage",
                        "_meta": {"title": "Load Image to Upscale"}
                    },
                    "2": {
                        "inputs": {
                            "upscale_method": "nearest-exact",
                            "width": 2048,
                            "height": 2048,
                            "crop": "disabled"
                        },
                        "class_type": "ImageScale",
                        "_meta": {"title": "Upscale Image"}
                    },
                    "3": {
                        "inputs": {
                            "images": ["2", 0]
                        },
                        "class_type": "SaveImage",
                        "_meta": {"title": "Save Upscaled Image"}
                    }
                }
            }
            
            filename = "mickmumpitz_upscale.json"
            filepath = os.path.join(self.workflows_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(workflow, f, indent=2)
            
            print(f"✅ Created upscale workflow: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Upscale workflow creation failed: {e}")
            return False
    
    def create_variations_workflow(self):
        """Create character variations workflow"""
        try:
            workflow = {
                "workflow_name": "Character Variations",
                "description": "Generate variations of a character while maintaining core features",
                "version": "2.0",
                "nodes": {
                    "1": {
                        "inputs": {
                            "image": "input_character_placeholder"
                        },
                        "class_type": "LoadImage",
                        "_meta": {"title": "Load Base Character"}
                    },
                    "2": {
                        "inputs": {
                            "text": "character variation, different pose, same face, detailed, high quality",
                            "clip": ["4", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "Variation Prompt"}
                    },
                    "3": {
                        "inputs": {
                            "text": "different face, different person, blurry, low quality",
                            "clip": ["4", 1]
                        },
                        "class_type": "CLIPTextEncode",
                        "_meta": {"title": "Variation Negative"}
                    },
                    "4": {
                        "inputs": {
                            "ckpt_name": "sd_xl_base_1.0.safetensors"
                        },
                        "class_type": "CheckpointLoaderSimple",
                        "_meta": {"title": "Load Variation Model"}
                    },
                    "5": {
                        "inputs": {
                            "seed": 999,
                            "steps": 20,
                            "cfg": 6.5,
                            "sampler_name": "euler",
                            "scheduler": "normal",
                            "denoise": 0.4,
                            "model": ["4", 0],
                            "positive": ["2", 0],
                            "negative": ["3", 0],
                            "latent_image": ["6", 0]
                        },
                        "class_type": "KSampler",
                        "_meta": {"title": "Variation Sampler"}
                    },
                    "6": {
                        "inputs": {
                            "pixels": ["1", 0],
                            "vae": ["4", 2]
                        },
                        "class_type": "VAEEncode",
                        "_meta": {"title": "Encode for Variation"}
                    },
                    "7": {
                        "inputs": {
                            "samples": ["5", 0],
                            "vae": ["4", 2]
                        },
                        "class_type": "VAEDecode",
                        "_meta": {"title": "Decode Variation"}
                    },
                    "8": {
                        "inputs": {
                            "images": ["7", 0]
                        },
                        "class_type": "SaveImage",
                        "_meta": {"title": "Save Character Variation"}
                    }
                }
            }
            
            filename = "mickmumpitz_variations.json"
            filepath = os.path.join(self.workflows_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(workflow, f, indent=2)
            
            print(f"✅ Created variations workflow: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Variations workflow creation failed: {e}")
            return False
