# 🚀 AI Integration Setup Guide

## Complete Setup for ComfyUI + Hugging Face Integration

This guide will help you set up real AI-powered 3D character generation using ComfyUI and Hugging Face APIs.

---

## 📋 **What You Need to Provide**

### 1. **Hugging Face API Token** (Required)
- **Go to**: [https://huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
- **Create account** if you don't have one (free)
- **Generate new token** with "Read" access
- **Copy the token** - you'll need it for setup

### 2. **ComfyUI Verification** (Required)
- **Confirm path**: `C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)`
- **Start ComfyUI** before running setup
- **Verify it's running** at `http://localhost:8188`

### 3. **Optional: OpenAI API Key**
- For advanced text processing features
- Get from: [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)

---

## 🛠️ **Step-by-Step Setup**

### **Step 1: Run the Setup Script**
```bash
cd character_generator_app
python setup_ai_integration.py
```

This will:
- ✅ Install all required Python packages
- ✅ Check ComfyUI installation
- ✅ Install ComfyUI custom nodes
- ✅ Create configuration files
- ✅ Test all integrations

### **Step 2: Configure API Keys**
```bash
cd backend
python config.py
```

This will prompt you for:
- Hugging Face API token
- ComfyUI settings
- Optional OpenAI key

### **Step 3: Install ComfyUI Custom Nodes**

**Required nodes for 3D generation:**

1. **ComfyUI-Manager** (for easy node management)
   ```bash
   cd "C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)\ComfyUI\custom_nodes"
   git clone https://github.com/ltdrdata/ComfyUI-Manager.git
   ```

2. **ComfyUI-3D-Pack** (for 3D generation)
   ```bash
   git clone https://github.com/MrForExample/ComfyUI-3D-Pack.git
   ```

3. **Restart ComfyUI** after installing nodes

### **Step 4: Test the Integration**
```bash
cd backend
python -c "from config import print_setup_status; print_setup_status()"
```

---

## 🎯 **What's New with AI Integration**

### **Enhanced Features:**

1. **🤖 Real ComfyUI Integration**
   - Direct API communication with ComfyUI
   - Real-time workflow execution
   - Actual AI-powered image generation

2. **🧠 Hugging Face AI Services**
   - Text-to-image generation
   - Image analysis and captioning
   - Character type classification
   - Prompt enhancement

3. **📊 New API Endpoints**
   - `/api/test-ai` - Test AI integration status
   - `/api/enhance-prompt` - AI-powered prompt enhancement
   - Enhanced `/` endpoint with AI status

4. **🔄 Smart Fallbacks**
   - Works with or without AI integration
   - Graceful degradation if services unavailable
   - Clear status reporting

---

## 🧪 **Testing Your Setup**

### **1. Test Backend API**
```bash
curl http://localhost:5000/
```
Should show AI integration status.

### **2. Test AI Integration**
```bash
curl http://localhost:5000/api/test-ai
```
Should show ComfyUI and Hugging Face status.

### **3. Test Prompt Enhancement**
```bash
curl -X POST http://localhost:5000/api/enhance-prompt \
  -H "Content-Type: application/json" \
  -d '{"prompt": "a blue robot"}'
```

### **4. Test Full Workflow**
1. Upload an image via web interface
2. Enter character description
3. Generate character (now uses real AI!)
4. View in 3D viewer

---

## 🔧 **Configuration Files**

### **.env** (API Keys)
```env
HUGGINGFACE_API_TOKEN=your_token_here
COMFYUI_HOST=127.0.0.1
COMFYUI_PORT=8188
OPENAI_API_KEY=your_openai_key_here
```

### **config.json** (Settings)
```json
{
  "HUGGINGFACE_API_TOKEN": "your_token",
  "COMFYUI_HOST": "127.0.0.1",
  "COMFYUI_PORT": "8188"
}
```

---

## 🚀 **Starting the Enhanced System**

### **Option 1: Use Startup Script**
```bash
start_ai_system.bat
```

### **Option 2: Manual Start**
```bash
# Terminal 1: Start ComfyUI (if not running)
cd "C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)"
run_nvidia_gpu.bat

# Terminal 2: Start Backend
cd character_generator_app\backend
python app.py

# Terminal 3: Open Frontend
start simple_frontend.html
```

---

## 🎮 **New Capabilities**

With AI integration, you can now:

1. **Generate from Text Only**
   - "A cyberpunk warrior with neon armor"
   - AI creates image → converts to 3D

2. **Enhance Uploaded Images**
   - Upload rough sketch
   - AI enhances and converts to 3D

3. **Smart Character Analysis**
   - AI analyzes your description
   - Automatically determines character type
   - Optimizes generation parameters

4. **Real 3D Generation**
   - Uses Stable Fast 3D or InstantMesh
   - Actual AI-powered 3D models
   - Professional quality output

---

## 🔍 **Troubleshooting**

### **ComfyUI Not Connecting**
- Ensure ComfyUI is running on port 8188
- Check Windows Firewall settings
- Verify custom nodes are installed

### **Hugging Face Authentication Failed**
- Check API token is correct
- Ensure token has "Read" permissions
- Try regenerating the token

### **Dependencies Missing**
```bash
pip install -r backend/requirements.txt
```

### **Custom Nodes Not Working**
- Restart ComfyUI after installing nodes
- Check ComfyUI console for errors
- Use ComfyUI-Manager to install missing dependencies

---

## 📈 **Performance Tips**

1. **GPU Acceleration**: Ensure CUDA is available for faster generation
2. **Model Caching**: First generation may be slow (downloading models)
3. **Batch Processing**: Generate multiple characters efficiently
4. **Quality Settings**: Adjust steps/CFG for speed vs quality

---

## 🎉 **Success Indicators**

When everything is working:
- ✅ Backend shows "AI integration modules loaded successfully"
- ✅ `/api/test-ai` returns all services as "Connected"
- ✅ Character generation uses real AI models
- ✅ 3D viewer displays actual generated characters

**You're now ready for AI-powered 3D character generation!** 🚀
