#!/usr/bin/env python3
"""
Configuration Setup for AI-Powered 3D Character Generator
Sets up all necessary configuration files for standalone operation
"""

import json
import os
import sys
from pathlib import Path
import getpass

def create_main_config():
    """Create main application configuration"""
    config = {
        "app": {
            "name": "AI-Powered 3D Character Generator",
            "version": "1.0.0",
            "host": "127.0.0.1",
            "port": 8080,
            "debug": False
        },
        "comfyui": {
            "host": "127.0.0.1",
            "port": 8188,
            "auto_start": True,
            "models_path": "./models",
            "workflows_path": "./workflows"
        },
        "huggingface": {
            "api_token": "",
            "cache_dir": "./models/huggingface",
            "use_auth_token": True
        },
        "generation": {
            "default_style": "realistic",
            "default_quality": "high",
            "max_batch_size": 4,
            "timeout_seconds": 300
        },
        "storage": {
            "outputs_dir": "./outputs",
            "uploads_dir": "./uploads",
            "temp_dir": "./temp",
            "max_file_size_mb": 50
        },
        "security": {
            "allowed_origins": ["http://localhost:8080", "http://127.0.0.1:8080"],
            "max_requests_per_minute": 60
        }
    }
    
    # Get Hugging Face token from user
    print("\n🤗 Hugging Face Configuration")
    print("=" * 40)
    print("To use advanced AI models, you need a Hugging Face API token.")
    print("1. Go to https://huggingface.co/settings/tokens")
    print("2. Create a new token with 'Read' permissions")
    print("3. Copy the token and paste it below")
    print("(You can skip this and add it later in config.json)")
    
    hf_token = getpass.getpass("Enter your Hugging Face API token (or press Enter to skip): ")
    if hf_token.strip():
        config["huggingface"]["api_token"] = hf_token.strip()
        print("✓ Hugging Face token configured")
    else:
        print("⚠️  Hugging Face token skipped - some features may be limited")
    
    # Save configuration
    with open("config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✓ Main configuration created: config.json")
    return config

def create_comfyui_config():
    """Create ComfyUI configuration"""
    comfyui_config = {
        "comfyui": {
            "base_path": "./comfyui",
            "models": {
                "checkpoints": "./models/checkpoints",
                "vae": "./models/comfyui/vae",
                "clip_vision": "./models/comfyui/clip_vision",
                "controlnet": "./models/controlnet",
                "embeddings": "./models/embeddings",
                "loras": "./models/loras"
            },
            "settings": {
                "listen": "127.0.0.1",
                "port": 8188,
                "enable_cors_header": "*",
                "max_upload_size": 100,
                "temp_directory": "./temp"
            }
        }
    }
    
    # Create ComfyUI directory structure
    comfyui_dirs = [
        "comfyui",
        "comfyui/models",
        "comfyui/models/checkpoints",
        "comfyui/models/vae", 
        "comfyui/models/clip_vision",
        "comfyui/models/controlnet",
        "comfyui/models/embeddings",
        "comfyui/models/loras",
        "comfyui/custom_nodes",
        "comfyui/input",
        "comfyui/output",
        "comfyui/temp"
    ]
    
    for dir_path in comfyui_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    # Save ComfyUI config
    with open("comfyui/extra_model_paths.yaml", "w") as f:
        f.write("""# Extra model paths for ComfyUI
comfyui:
    base_path: ./
    checkpoints: ../models/checkpoints
    vae: ../models/comfyui/vae
    clip_vision: ../models/comfyui/clip_vision
    controlnet: ../models/controlnet
    embeddings: ../models/embeddings
    loras: ../models/loras
""")
    
    print("✓ ComfyUI configuration created")
    return comfyui_config

def create_workflow_configs():
    """Create workflow configuration files"""
    workflows_dir = Path("workflows")
    workflows_dir.mkdir(exist_ok=True)
    
    # Standard character workflow
    standard_workflow = {
        "name": "Standard Character Generation",
        "description": "Basic text-to-character workflow",
        "version": "1.0",
        "workflow": {
            "1": {
                "class_type": "CLIPTextEncode",
                "inputs": {
                    "text": "{prompt}",
                    "clip": ["4", 1]
                }
            },
            "2": {
                "class_type": "CLIPTextEncode", 
                "inputs": {
                    "text": "low quality, blurry, distorted",
                    "clip": ["4", 1]
                }
            },
            "3": {
                "class_type": "KSampler",
                "inputs": {
                    "seed": 42,
                    "steps": 20,
                    "cfg": 7.0,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 1.0,
                    "model": ["4", 0],
                    "positive": ["1", 0],
                    "negative": ["2", 0],
                    "latent_image": ["5", 0]
                }
            },
            "4": {
                "class_type": "CheckpointLoaderSimple",
                "inputs": {
                    "ckpt_name": "v1-5-pruned-emaonly.ckpt"
                }
            },
            "5": {
                "class_type": "EmptyLatentImage",
                "inputs": {
                    "width": 512,
                    "height": 512,
                    "batch_size": 1
                }
            },
            "6": {
                "class_type": "VAEDecode",
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                }
            },
            "7": {
                "class_type": "SaveImage",
                "inputs": {
                    "filename_prefix": "character_",
                    "images": ["6", 0]
                }
            }
        }
    }
    
    with open("workflows/standard_character.json", "w") as f:
        json.dump(standard_workflow, f, indent=2)
    
    print("✓ Workflow configurations created")

def create_environment_file():
    """Create environment configuration file"""
    env_content = """# AI-Powered 3D Character Generator Environment Configuration

# Application Settings
APP_HOST=127.0.0.1
APP_PORT=8080
APP_DEBUG=false

# ComfyUI Settings
COMFYUI_HOST=127.0.0.1
COMFYUI_PORT=8188
COMFYUI_AUTO_START=true

# Hugging Face Settings
HF_HOME=./models/huggingface
TRANSFORMERS_CACHE=./models/huggingface/transformers
DIFFUSERS_CACHE=./models/huggingface/diffusers

# CUDA Settings (if available)
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
"""
    
    with open(".env", "w") as f:
        f.write(env_content)
    
    print("✓ Environment file created: .env")

def create_gitignore():
    """Create .gitignore file for the repository"""
    gitignore_content = """# AI Models (too large for git)
models/
*.ckpt
*.safetensors
*.pth
*.bin

# Generated outputs
outputs/
uploads/
temp/
logs/

# Virtual environment
venv/
env/
.venv/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment variables
.env
.env.local
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Database
*.db
*.sqlite3

# Temporary files
*.tmp
*.temp

# ComfyUI specific
comfyui/output/
comfyui/input/
comfyui/temp/
"""
    
    with open(".gitignore", "w") as f:
        f.write(gitignore_content)
    
    print("✓ Git ignore file created: .gitignore")

def main():
    """Main configuration setup"""
    print("🔧 AI-Powered 3D Character Generator")
    print("⚙️  Configuration Setup")
    print("=" * 50)
    
    try:
        # Create main directories
        dirs = ["logs", "temp", "outputs", "uploads", "workflows"]
        for dir_name in dirs:
            Path(dir_name).mkdir(exist_ok=True)
        
        print("✓ Created main directories")
        
        # Create configuration files
        create_main_config()
        create_comfyui_config()
        create_workflow_configs()
        create_environment_file()
        create_gitignore()
        
        print("\n🎉 Configuration setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Edit config.json to customize settings")
        print("2. Add your Hugging Face API token if skipped")
        print("3. Run download_ai_models.py to download AI models")
        print("4. Run start.bat (Windows) or start_app.sh (Linux/Mac)")
        
        print("\n📁 Configuration files created:")
        print("  - config.json (main configuration)")
        print("  - .env (environment variables)")
        print("  - .gitignore (git ignore rules)")
        print("  - workflows/standard_character.json (default workflow)")
        print("  - comfyui/extra_model_paths.yaml (ComfyUI paths)")
        
    except Exception as e:
        print(f"\n❌ Configuration setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
