#!/bin/bash

echo "========================================"
echo "AI-Powered 3D Character Generator"
echo "Starting Application..."
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if virtual environment exists
if [ ! -f "venv/bin/activate" ]; then
    echo -e "${RED}ERROR: Virtual environment not found${NC}"
    echo "Please run ./install.sh first"
    exit 1
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Check if ComfyUI is running
echo "Checking ComfyUI status..."
if ! curl -s http://127.0.0.1:8188/system_stats > /dev/null 2>&1; then
    echo "Starting ComfyUI..."
    python -m comfyui.main --listen 127.0.0.1 --port 8188 &
    echo "Waiting for ComfyUI to start..."
    sleep 10
else
    echo -e "${GREEN}ComfyUI is already running${NC}"
fi

# Start the main application
echo
echo "Starting main application..."
echo -e "${GREEN}Open your browser to: http://localhost:8080${NC}"
echo
echo "Press Ctrl+C to stop the application"
echo "========================================"
python comprehensive_backend.py

echo
echo "Application stopped."
