"""
3D Character Generator App - Backend API
"""

import os
import json
import uuid
import time
import threading
from datetime import datetime
from flask import Flask, request, jsonify, send_file, send_from_directory
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
from werkzeug.utils import secure_filename
from PIL import Image
import cv2
import numpy as np

# Import our custom modules
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# Try to import old modules (fallback)
try:
    from src.workflow_generators.comfyui_workflow_generator import ComfyUIWorkflowGenerator
    from src.integrations.comfyui.mickmumpitz_workflows import Mick<PERSON><PERSON>zWorkflows
    OLD_MODULES_AVAILABLE = True
except ImportError:
    OLD_MODULES_AVAILABLE = False

# Import new AI integration modules
try:
    from comfyui_client import ComfyUI<PERSON>lient
    from huggingface_client import HuggingFaceClient
    from comfyui_workflows import get_workflow_by_type
    from config import Config
    AI_INTEGRATION_AVAILABLE = True
    print("✅ AI integration modules loaded successfully")
except ImportError as e:
    print(f"⚠️  AI integration modules not available: {e}")
    print("   Run setup_ai_integration.py to install dependencies")
    AI_INTEGRATION_AVAILABLE = False

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Enable CORS for all routes
CORS(app, origins=["http://localhost:3000", "http://127.0.0.1:3000", "file://", "*"],
     allow_headers=["Content-Type", "Authorization"],
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins=["http://localhost:3000", "http://127.0.0.1:3000"])

# Configuration
UPLOAD_FOLDER = os.path.join(os.path.dirname(__file__), 'uploads')
OUTPUT_FOLDER = os.path.join(os.path.dirname(__file__), 'outputs')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}

# Create directories
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)
os.makedirs(os.path.join(OUTPUT_FOLDER, 'models'), exist_ok=True)
os.makedirs(os.path.join(OUTPUT_FOLDER, 'textures'), exist_ok=True)
os.makedirs(os.path.join(OUTPUT_FOLDER, 'animations'), exist_ok=True)

# Initialize workflow generators and AI clients
if OLD_MODULES_AVAILABLE:
    workflow_generator = ComfyUIWorkflowGenerator()
    mickmumpitz_workflows = MickmumpitzWorkflows()
else:
    workflow_generator = None
    mickmumpitz_workflows = None

# Initialize AI clients
if AI_INTEGRATION_AVAILABLE:
    comfyui_client = ComfyUIClient()
    huggingface_client = HuggingFaceClient()
    print("🤖 AI clients initialized")
else:
    comfyui_client = None
    huggingface_client = None
    print("⚠️  Using fallback mode without AI integration")

# Store active jobs
active_jobs = {}

def allowed_file(filename):
    """Check if the file extension is allowed."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def process_image(image_path):
    """Process the uploaded image for character generation."""
    try:
        # Load and preprocess the image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError("Could not load image")

        # Convert to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Resize image to standard size
        target_size = (512, 512)
        image_resized = cv2.resize(image_rgb, target_size)

        # Save processed image
        processed_path = image_path.replace('.', '_processed.')
        cv2.imwrite(processed_path, cv2.cvtColor(image_resized, cv2.COLOR_RGB2BGR))

        return processed_path
    except Exception as e:
        raise Exception(f"Error processing image: {str(e)}")

def generate_character_workflow(job_id, image_path, text_prompt, style_options):
    """Generate a character using ComfyUI workflows."""
    try:
        print(f"Starting character generation for job {job_id}")
        print(f"  Image path: {image_path}")
        print(f"  Text prompt: {text_prompt}")
        print(f"  Style options: {style_options}")

        # Update job status
        active_jobs[job_id]['status'] = 'generating_workflow'
        active_jobs[job_id]['progress'] = 10
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

        # Create character generation workflow
        try:
            # Try without mickmumpitz first to avoid dependency issues
            workflow = workflow_generator.create_character_generation_workflow(use_mickmumpitz=False)
        except Exception as workflow_error:
            print(f"Workflow generation error: {workflow_error}")
            # Create a simple fallback workflow
            workflow = {
                "1": {
                    "class_type": "CLIPTextEncode",
                    "inputs": {
                        "text": text_prompt or "A 3D character",
                        "clip": ["4", 1]
                    }
                },
                "2": {
                    "class_type": "KSampler",
                    "inputs": {
                        "seed": 42,
                        "steps": 20,
                        "cfg": 8.0,
                        "sampler_name": "euler",
                        "scheduler": "normal",
                        "denoise": 1.0,
                        "model": ["4", 0],
                        "positive": ["1", 0],
                        "negative": ["3", 0],
                        "latent_image": ["5", 0]
                    }
                },
                "3": {
                    "class_type": "CLIPTextEncode",
                    "inputs": {
                        "text": "bad quality, blurry",
                        "clip": ["4", 1]
                    }
                },
                "4": {
                    "class_type": "CheckpointLoaderSimple",
                    "inputs": {
                        "ckpt_name": "sd_xl_base_1.0.safetensors"
                    }
                },
                "5": {
                    "class_type": "EmptyLatentImage",
                    "inputs": {
                        "width": 512,
                        "height": 512,
                        "batch_size": 1
                    }
                },
                "6": {
                    "class_type": "VAEDecode",
                    "inputs": {
                        "samples": ["2", 0],
                        "vae": ["4", 2]
                    }
                },
                "7": {
                    "class_type": "SaveImage",
                    "inputs": {
                        "filename_prefix": "character",
                        "images": ["6", 0]
                    }
                }
            }

        # Customize workflow based on input
        if text_prompt and isinstance(workflow, dict):
            # Handle ComfyUI workflow format (nodes as dictionary with IDs as keys)
            for node_id, node in workflow.items():
                if isinstance(node, dict) and node.get('class_type') == 'CLIPTextEncode':
                    # Update positive prompt (node "1" in our fallback workflow)
                    if node_id == "1" and 'inputs' in node:
                        node['inputs']['text'] = text_prompt
                        print(f"Updated positive prompt to: {text_prompt}")

        # Save workflow
        workflow_filename = f"character_workflow_{job_id}.json"
        workflow_path = os.path.join(OUTPUT_FOLDER, workflow_filename)

        with open(workflow_path, 'w') as f:
            json.dump(workflow, f, indent=2)

        # Update job status
        active_jobs[job_id]['status'] = 'workflow_created'
        active_jobs[job_id]['progress'] = 30
        active_jobs[job_id]['workflow_path'] = workflow_path
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

        # Simulate ComfyUI processing (in a real implementation, this would call ComfyUI API)
        simulate_comfyui_processing(job_id, workflow_path, image_path, text_prompt)

    except Exception as e:
        active_jobs[job_id]['status'] = 'error'
        active_jobs[job_id]['error'] = str(e)
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

def generate_with_comfyui_3d(text_prompt, model_path, image_path):
    """Generate 3D model using ComfyUI with 3D Pack nodes."""
    try:
        if not comfyui_client:
            return False

        # Connect to ComfyUI
        if not comfyui_client.connect():
            print("Failed to connect to ComfyUI")
            return False

        # First, generate an image from text if no image provided
        if not os.path.exists(image_path):
            print("Generating image from text prompt...")
            # Use basic text-to-image workflow
            from comfyui_workflows import get_text_to_3d_workflow_enhanced
            workflow = get_text_to_3d_workflow_enhanced(text_prompt)

            # Queue the workflow
            prompt_id = comfyui_client.queue_prompt(workflow)
            if not prompt_id:
                print("Failed to queue text-to-image workflow")
                return False

            # Wait for completion (simplified)
            time.sleep(30)  # Basic wait - in production, use proper callback

        # Now generate 3D from image
        if os.path.exists(image_path):
            print("Generating 3D model from image...")

            # Upload image to ComfyUI
            uploaded_filename = comfyui_client.upload_image(image_path)
            if not uploaded_filename:
                print("Failed to upload image to ComfyUI")
                return False

            # Use Stable Fast 3D workflow
            from comfyui_workflows import get_stable_fast_3d_workflow
            workflow = get_stable_fast_3d_workflow(uploaded_filename)

            # Queue the 3D generation workflow
            prompt_id = comfyui_client.queue_prompt(workflow)
            if not prompt_id:
                print("Failed to queue 3D generation workflow")
                return False

            # Wait for completion (simplified)
            time.sleep(60)  # 3D generation takes longer

            # Try to get the generated 3D file
            # This would need to be implemented based on ComfyUI-3D-Pack output format
            print("3D generation workflow queued successfully")
            return True

        return False

    except Exception as e:
        print(f"ComfyUI 3D generation error: {e}")
        return False
    finally:
        if comfyui_client:
            comfyui_client.disconnect()

def simulate_comfyui_processing(job_id, workflow_path, image_path, text_prompt):
    """Simulate ComfyUI processing and generate mock 3D character files."""
    try:
        # Update job status
        active_jobs[job_id]['status'] = 'processing_3d'
        active_jobs[job_id]['progress'] = 50
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

        # Simulate processing time
        time.sleep(3)

        # Create mock 3D model files
        print(f"Creating output files for job {job_id}")
        model_filename = f"character_{job_id}.glb"
        texture_filename = f"character_{job_id}_texture.png"
        animation_filename = f"character_{job_id}_idle.fbx"

        model_path = os.path.join(OUTPUT_FOLDER, 'models', model_filename)
        texture_path = os.path.join(OUTPUT_FOLDER, 'textures', texture_filename)
        animation_path = os.path.join(OUTPUT_FOLDER, 'animations', animation_filename)

        print(f"Output paths:")
        print(f"  Model: {model_path}")
        print(f"  Texture: {texture_path}")
        print(f"  Animation: {animation_path}")

        # Create mock files (in a real implementation, these would be generated by ComfyUI/Blender)
        print("Calling create_mock_3d_files...")
        create_mock_3d_files(model_path, texture_path, animation_path, image_path, text_prompt)
        print("create_mock_3d_files completed")

        # Update job status
        active_jobs[job_id]['status'] = 'post_processing'
        active_jobs[job_id]['progress'] = 80
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

        # Simulate post-processing
        time.sleep(2)

        # Finalize job
        active_jobs[job_id]['status'] = 'completed'
        active_jobs[job_id]['progress'] = 100
        active_jobs[job_id]['model_path'] = model_path
        active_jobs[job_id]['texture_path'] = texture_path
        active_jobs[job_id]['animation_path'] = animation_path
        active_jobs[job_id]['completed_at'] = datetime.now().isoformat()

        socketio.emit('job_update', active_jobs[job_id], room=job_id)

    except Exception as e:
        active_jobs[job_id]['status'] = 'error'
        active_jobs[job_id]['error'] = str(e)
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

def create_mock_3d_files(model_path, texture_path, animation_path, image_path, text_prompt):
    """Create mock 3D files for demonstration purposes."""
    try:
        print(f"Creating mock 3D files:")
        print(f"  Model path: {model_path}")
        print(f"  Texture path: {texture_path}")
        print(f"  Animation path: {animation_path}")

        # Ensure directories exist
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        os.makedirs(os.path.dirname(texture_path), exist_ok=True)
        os.makedirs(os.path.dirname(animation_path), exist_ok=True)

        # Try ComfyUI 3D generation first, fallback to procedural
        if comfyui_client and AI_INTEGRATION_AVAILABLE:
            print("Attempting ComfyUI 3D generation...")
            try:
                # Try to use ComfyUI for real 3D generation
                success = generate_with_comfyui_3d(text_prompt, model_path, image_path)
                if success:
                    print("✅ ComfyUI 3D generation successful!")
                else:
                    print("⚠️ ComfyUI 3D generation failed, using procedural fallback")
                    glb_content = create_character_glb(text_prompt)
                    with open(model_path, 'wb') as f:
                        f.write(glb_content)
            except Exception as e:
                print(f"⚠️ ComfyUI 3D generation error: {e}, using procedural fallback")
                glb_content = create_character_glb(text_prompt)
                with open(model_path, 'wb') as f:
                    f.write(glb_content)
        else:
            # Create a detailed GLB character file (procedural fallback)
            print("Creating procedural GLB character file...")
            glb_content = create_character_glb(text_prompt)
            with open(model_path, 'wb') as f:
                f.write(glb_content)

        print(f"GLB file created: {os.path.exists(model_path)}")

        # Create a texture based on the input image
        print("Creating texture file...")
        if os.path.exists(image_path):
            # Copy and resize the input image as texture
            image = Image.open(image_path)
            image = image.resize((512, 512))
            image.save(texture_path)
        else:
            # Create a simple colored texture
            create_simple_texture(texture_path, text_prompt)
        print(f"Texture file created: {os.path.exists(texture_path)}")

        # Create a mock animation file
        print("Creating animation file...")
        with open(animation_path, 'w') as f:
            f.write(f"# Mock FBX animation file for character based on: {text_prompt}\n")
            f.write("# This would contain actual animation data in a real implementation\n")
        print(f"Animation file created: {os.path.exists(animation_path)}")

        print("All mock files created successfully!")

    except Exception as e:
        print(f"Error creating mock 3D files: {e}")
        import traceback
        traceback.print_exc()
        raise Exception(f"Error creating mock 3D files: {str(e)}")

def create_character_glb(text_prompt="A 3D character"):
    """Create a detailed 3D character GLB file based on the text prompt."""
    import json
    import struct
    import math

    # Analyze the text prompt to determine character features
    character_type = analyze_character_prompt(text_prompt)

    # Generate character geometry
    vertices, indices, normals = generate_character_geometry(character_type)

    # Create materials based on prompt
    materials = create_character_materials(character_type)

    # Create GLTF data structure
    gltf_data = {
        "asset": {
            "version": "2.0",
            "generator": "3D Character Generator Pro"
        },
        "scene": 0,
        "scenes": [{"nodes": [0]}],
        "nodes": [
            {
                "mesh": 0,
                "name": f"Character_{character_type['name']}",
                "scale": [1.0, 1.0, 1.0],
                "rotation": [0.0, 0.0, 0.0, 1.0],
                "translation": [0.0, 0.0, 0.0]
            }
        ],
        "meshes": [
            {
                "name": "CharacterMesh",
                "primitives": [
                    {
                        "attributes": {
                            "POSITION": 0,
                            "NORMAL": 1
                        },
                        "indices": 2,
                        "material": 0
                    }
                ]
            }
        ],
        "materials": materials,
        "accessors": [
            {
                "bufferView": 0,
                "componentType": 5126,  # FLOAT
                "count": len(vertices) // 3,
                "type": "VEC3",
                "max": [max(vertices[i::3]) for i in range(3)],
                "min": [min(vertices[i::3]) for i in range(3)]
            },
            {
                "bufferView": 1,
                "componentType": 5126,  # FLOAT
                "count": len(normals) // 3,
                "type": "VEC3"
            },
            {
                "bufferView": 2,
                "componentType": 5123,  # UNSIGNED_SHORT
                "count": len(indices),
                "type": "SCALAR"
            }
        ],
        "bufferViews": [
            {
                "buffer": 0,
                "byteOffset": 0,
                "byteLength": len(vertices) * 4
            },
            {
                "buffer": 0,
                "byteOffset": len(vertices) * 4,
                "byteLength": len(normals) * 4
            },
            {
                "buffer": 0,
                "byteOffset": (len(vertices) + len(normals)) * 4,
                "byteLength": len(indices) * 2
            }
        ],
        "buffers": [
            {
                "byteLength": (len(vertices) + len(normals)) * 4 + len(indices) * 2
            }
        ]
    }

    # Pack binary data
    vertex_data = struct.pack(f'{len(vertices)}f', *vertices)
    normal_data = struct.pack(f'{len(normals)}f', *normals)
    index_data = struct.pack(f'{len(indices)}H', *indices)
    binary_data = vertex_data + normal_data + index_data

    # Create JSON chunk
    json_str = json.dumps(gltf_data, separators=(',', ':'))
    json_bytes = json_str.encode('utf-8')

    # Pad to 4-byte boundaries
    json_padding = (4 - (len(json_bytes) % 4)) % 4
    json_bytes += b' ' * json_padding

    bin_padding = (4 - (len(binary_data) % 4)) % 4
    binary_data += b'\x00' * bin_padding

    # Create GLB
    magic = b'glTF'
    version = struct.pack('<I', 2)
    total_length = struct.pack('<I', 12 + 8 + len(json_bytes) + 8 + len(binary_data))

    json_chunk_length = struct.pack('<I', len(json_bytes))
    json_chunk_type = b'JSON'

    bin_chunk_length = struct.pack('<I', len(binary_data))
    bin_chunk_type = b'BIN\x00'

    glb_content = (magic + version + total_length +
                   json_chunk_length + json_chunk_type + json_bytes +
                   bin_chunk_length + bin_chunk_type + binary_data)

    return glb_content

def analyze_character_prompt(text_prompt):
    """Analyze the text prompt to determine character features."""
    prompt_lower = text_prompt.lower()

    character_type = {
        'name': 'humanoid',
        'style': 'realistic',
        'colors': {'primary': [0.8, 0.6, 0.4], 'secondary': [0.2, 0.4, 0.8]},
        'proportions': {'head_scale': 1.0, 'body_scale': 1.0, 'limb_scale': 1.0}
    }

    # Determine character type
    if any(word in prompt_lower for word in ['robot', 'android', 'cyborg', 'mech']):
        character_type['name'] = 'robot'
        character_type['colors']['primary'] = [0.7, 0.7, 0.8]  # Metallic
        character_type['colors']['secondary'] = [0.2, 0.4, 0.9]  # Blue accents
    elif any(word in prompt_lower for word in ['alien', 'extraterrestrial', 'martian']):
        character_type['name'] = 'alien'
        character_type['colors']['primary'] = [0.6, 0.8, 0.6]  # Green
        character_type['proportions']['head_scale'] = 1.3
    elif any(word in prompt_lower for word in ['warrior', 'knight', 'soldier']):
        character_type['name'] = 'warrior'
        character_type['colors']['primary'] = [0.6, 0.4, 0.2]  # Brown
        character_type['colors']['secondary'] = [0.8, 0.8, 0.8]  # Silver armor
    elif any(word in prompt_lower for word in ['wizard', 'mage', 'sorcerer']):
        character_type['name'] = 'wizard'
        character_type['colors']['primary'] = [0.4, 0.2, 0.8]  # Purple
        character_type['colors']['secondary'] = [0.9, 0.9, 0.2]  # Gold

    # Determine style
    if any(word in prompt_lower for word in ['cartoon', 'toon', 'animated']):
        character_type['style'] = 'cartoon'
        character_type['proportions']['head_scale'] = 1.2
    elif any(word in prompt_lower for word in ['anime', 'manga']):
        character_type['style'] = 'anime'
        character_type['proportions']['head_scale'] = 1.1
    elif any(word in prompt_lower for word in ['low poly', 'lowpoly', 'geometric']):
        character_type['style'] = 'lowpoly'

    # Color analysis
    color_map = {
        'red': [0.9, 0.2, 0.2], 'blue': [0.2, 0.4, 0.9], 'green': [0.2, 0.8, 0.3],
        'yellow': [0.9, 0.9, 0.2], 'purple': [0.7, 0.2, 0.8], 'orange': [0.9, 0.5, 0.1],
        'pink': [0.9, 0.4, 0.7], 'brown': [0.6, 0.4, 0.2], 'black': [0.1, 0.1, 0.1],
        'white': [0.9, 0.9, 0.9], 'gray': [0.5, 0.5, 0.5], 'silver': [0.8, 0.8, 0.8],
        'gold': [0.9, 0.8, 0.2]
    }

    for color_name, color_value in color_map.items():
        if color_name in prompt_lower:
            character_type['colors']['primary'] = color_value
            break

    return character_type

def generate_character_geometry(character_type):
    """Generate detailed 3D character geometry."""
    import math

    vertices = []
    indices = []
    normals = []

    # Scale factors based on character type
    head_scale = character_type['proportions']['head_scale']
    body_scale = character_type['proportions']['body_scale']
    limb_scale = character_type['proportions']['limb_scale']

    # Generate head (sphere)
    head_center = [0, 1.6, 0]
    head_radius = 0.15 * head_scale
    head_verts, head_indices, head_normals = create_sphere(head_center, head_radius, 16, 12)

    # Generate torso (cylinder)
    torso_center = [0, 1.0, 0]
    torso_radius = 0.2 * body_scale
    torso_height = 0.6 * body_scale
    torso_verts, torso_indices, torso_normals = create_cylinder(torso_center, torso_radius, torso_height, 12)

    # Generate arms
    arm_length = 0.5 * limb_scale
    arm_radius = 0.05 * limb_scale

    # Left arm
    left_arm_center = [-0.25, 1.2, 0]
    left_arm_verts, left_arm_indices, left_arm_normals = create_cylinder(left_arm_center, arm_radius, arm_length, 8)

    # Right arm
    right_arm_center = [0.25, 1.2, 0]
    right_arm_verts, right_arm_indices, right_arm_normals = create_cylinder(right_arm_center, arm_radius, arm_length, 8)

    # Generate legs
    leg_length = 0.8 * limb_scale
    leg_radius = 0.06 * limb_scale

    # Left leg
    left_leg_center = [-0.1, 0.4, 0]
    left_leg_verts, left_leg_indices, left_leg_normals = create_cylinder(left_leg_center, leg_radius, leg_length, 8)

    # Right leg
    right_leg_center = [0.1, 0.4, 0]
    right_leg_verts, right_leg_indices, right_leg_normals = create_cylinder(right_leg_center, leg_radius, leg_length, 8)

    # Combine all geometry
    vertex_offset = 0

    # Add head
    vertices.extend(head_verts)
    normals.extend(head_normals)
    indices.extend([i + vertex_offset for i in head_indices])
    vertex_offset += len(head_verts) // 3

    # Add torso
    vertices.extend(torso_verts)
    normals.extend(torso_normals)
    indices.extend([i + vertex_offset for i in torso_indices])
    vertex_offset += len(torso_verts) // 3

    # Add arms
    vertices.extend(left_arm_verts)
    normals.extend(left_arm_normals)
    indices.extend([i + vertex_offset for i in left_arm_indices])
    vertex_offset += len(left_arm_verts) // 3

    vertices.extend(right_arm_verts)
    normals.extend(right_arm_normals)
    indices.extend([i + vertex_offset for i in right_arm_indices])
    vertex_offset += len(right_arm_verts) // 3

    # Add legs
    vertices.extend(left_leg_verts)
    normals.extend(left_leg_normals)
    indices.extend([i + vertex_offset for i in left_leg_indices])
    vertex_offset += len(left_leg_verts) // 3

    vertices.extend(right_leg_verts)
    normals.extend(right_leg_normals)
    indices.extend([i + vertex_offset for i in right_leg_indices])

    return vertices, indices, normals

def create_sphere(center, radius, rings, sectors):
    """Create a sphere mesh."""
    import math

    vertices = []
    normals = []
    indices = []

    for r in range(rings + 1):
        lat = math.pi * (-0.5 + r / rings)
        y = math.sin(lat)
        xz = math.cos(lat)

        for s in range(sectors + 1):
            lon = 2 * math.pi * s / sectors
            x = xz * math.cos(lon)
            z = xz * math.sin(lon)

            # Vertex position
            vertices.extend([
                center[0] + radius * x,
                center[1] + radius * y,
                center[2] + radius * z
            ])

            # Normal
            normals.extend([x, y, z])

    # Generate indices
    for r in range(rings):
        for s in range(sectors):
            first = r * (sectors + 1) + s
            second = first + sectors + 1

            indices.extend([first, second, first + 1])
            indices.extend([second, second + 1, first + 1])

    return vertices, indices, normals

def create_cylinder(center, radius, height, sectors):
    """Create a cylinder mesh."""
    import math

    vertices = []
    normals = []
    indices = []

    # Generate vertices for top and bottom circles
    for i in range(2):  # 0 = bottom, 1 = top
        y = center[1] + (i - 0.5) * height

        # Center vertex
        vertices.extend([center[0], y, center[2]])
        normals.extend([0, 1 if i == 1 else -1, 0])

        # Circle vertices
        for s in range(sectors):
            angle = 2 * math.pi * s / sectors
            x = center[0] + radius * math.cos(angle)
            z = center[2] + radius * math.sin(angle)

            vertices.extend([x, y, z])
            normals.extend([0, 1 if i == 1 else -1, 0])

    # Generate side vertices
    for s in range(sectors + 1):
        angle = 2 * math.pi * s / sectors
        x_norm = math.cos(angle)
        z_norm = math.sin(angle)

        for i in range(2):  # bottom and top
            y = center[1] + (i - 0.5) * height
            x = center[0] + radius * x_norm
            z = center[2] + radius * z_norm

            vertices.extend([x, y, z])
            normals.extend([x_norm, 0, z_norm])

    # Generate indices for caps
    for i in range(2):
        center_idx = i * (sectors + 1)
        for s in range(sectors):
            v1 = center_idx
            v2 = center_idx + 1 + s
            v3 = center_idx + 1 + ((s + 1) % sectors)

            if i == 0:  # bottom
                indices.extend([v1, v3, v2])
            else:  # top
                indices.extend([v1, v2, v3])

    # Generate indices for sides
    side_start = 2 * (sectors + 1)
    for s in range(sectors):
        v1 = side_start + s * 2
        v2 = side_start + s * 2 + 1
        v3 = side_start + ((s + 1) % (sectors + 1)) * 2
        v4 = side_start + ((s + 1) % (sectors + 1)) * 2 + 1

        indices.extend([v1, v2, v3])
        indices.extend([v2, v4, v3])

    return vertices, indices, normals

def create_character_materials(character_type):
    """Create materials for the character based on type."""
    primary_color = character_type['colors']['primary']
    secondary_color = character_type['colors']['secondary']

    materials = [
        {
            "name": f"{character_type['name']}_material",
            "pbrMetallicRoughness": {
                "baseColorFactor": primary_color + [1.0],  # Add alpha
                "metallicFactor": 0.1 if character_type['name'] == 'robot' else 0.0,
                "roughnessFactor": 0.3 if character_type['name'] == 'robot' else 0.8
            },
            "emissiveFactor": [0.0, 0.0, 0.0]
        }
    ]

    return materials

def create_simple_texture(texture_path, text_prompt):
    """Create a simple texture based on the text prompt."""
    # Create a simple colored image based on the text prompt
    color = (128, 128, 128)  # Default gray

    # Simple color mapping based on keywords
    if 'red' in text_prompt.lower():
        color = (255, 100, 100)
    elif 'blue' in text_prompt.lower():
        color = (100, 100, 255)
    elif 'green' in text_prompt.lower():
        color = (100, 255, 100)
    elif 'yellow' in text_prompt.lower():
        color = (255, 255, 100)

    # Create image
    image = Image.new('RGB', (512, 512), color)
    image.save(texture_path)

@app.route('/')
def index():
    """Serve the main page."""
    return jsonify({
        'message': '3D Character Generator API',
        'version': '2.0.0',
        'ai_integration': AI_INTEGRATION_AVAILABLE,
        'comfyui_available': comfyui_client is not None,
        'huggingface_available': huggingface_client is not None,
        'endpoints': {
            'upload': '/api/upload',
            'generate': '/api/generate',
            'status': '/api/status/<job_id>',
            'download': '/api/download/<job_id>/<file_type>',
            'jobs': '/api/jobs',
            'test_ai': '/api/test-ai',
            'enhance_prompt': '/api/enhance-prompt'
        }
    })

@app.route('/api/test-ai', methods=['GET'])
def test_ai_integration():
    """Test AI integration status."""
    results = {}

    # Test ComfyUI
    if comfyui_client:
        try:
            # Test basic connection
            import requests
            response = requests.get("http://127.0.0.1:8188/system_stats", timeout=5)
            results['comfyui'] = {
                'available': True,
                'connected': response.status_code == 200,
                'status': 'Connected' if response.status_code == 200 else 'Not running'
            }
        except Exception as e:
            results['comfyui'] = {
                'available': True,
                'connected': False,
                'status': f'Error: {str(e)}'
            }
    else:
        results['comfyui'] = {
            'available': False,
            'connected': False,
            'status': 'Not installed'
        }

    # Test Hugging Face
    if huggingface_client:
        try:
            connected = huggingface_client.test_connection()
            results['huggingface'] = {
                'available': True,
                'connected': connected,
                'status': 'Connected' if connected else 'Authentication failed'
            }
        except Exception as e:
            results['huggingface'] = {
                'available': True,
                'connected': False,
                'status': f'Error: {str(e)}'
            }
    else:
        results['huggingface'] = {
            'available': False,
            'connected': False,
            'status': 'Not installed'
        }

    return jsonify(results)

@app.route('/api/enhance-prompt', methods=['POST'])
def enhance_prompt():
    """Enhance a character description prompt using AI."""
    try:
        data = request.get_json()
        original_prompt = data.get('prompt', '')

        if not original_prompt:
            return jsonify({'error': 'No prompt provided'}), 400

        enhanced_prompt = original_prompt

        # Use Hugging Face to enhance the prompt if available
        if huggingface_client:
            try:
                enhanced_prompt = huggingface_client.enhance_prompt(original_prompt)

                # Also get character classification
                classification = huggingface_client.classify_character_type(original_prompt)

                return jsonify({
                    'original_prompt': original_prompt,
                    'enhanced_prompt': enhanced_prompt,
                    'character_classification': classification,
                    'ai_enhanced': True
                })
            except Exception as e:
                print(f"Prompt enhancement failed: {e}")

        # Fallback enhancement
        enhanced_prompt = f"A detailed 3D character: {original_prompt}, high quality, well-lit, professional"

        return jsonify({
            'original_prompt': original_prompt,
            'enhanced_prompt': enhanced_prompt,
            'character_classification': {'human character': 1.0},
            'ai_enhanced': False
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle file upload."""
    try:
        print("Upload request received")

        # Check if file is present
        if 'file' not in request.files:
            print("No file in request")
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        print(f"File received: {file.filename}")

        # Check if file is selected
        if file.filename == '':
            print("Empty filename")
            return jsonify({'error': 'No file selected'}), 400

        # Check if file is allowed
        if not allowed_file(file.filename):
            print(f"File type not allowed: {file.filename}")
            return jsonify({'error': 'File type not allowed'}), 400

        # Ensure upload directory exists
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        print(f"Upload folder created/verified: {UPLOAD_FOLDER}")

        # Save file
        filename = secure_filename(file.filename)
        timestamp = str(int(time.time()))
        filename = f"{timestamp}_{filename}"
        file_path = os.path.join(UPLOAD_FOLDER, filename)

        print(f"Saving file to: {file_path}")
        file.save(file_path)

        # Verify file was saved
        if not os.path.exists(file_path):
            print("File was not saved successfully")
            return jsonify({'error': 'Failed to save file'}), 500

        print(f"File saved successfully: {file_path}")

        # Process image
        try:
            processed_path = process_image(file_path)
            print(f"Image processed: {processed_path}")
        except Exception as e:
            print(f"Image processing failed: {e}")
            # Return original path if processing fails
            processed_path = file_path

        return jsonify({
            'message': 'File uploaded successfully',
            'filename': filename,
            'file_path': file_path,
            'processed_path': processed_path
        })

    except Exception as e:
        print(f"Upload error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate', methods=['POST'])
def generate_character():
    """Generate a 3D character."""
    try:
        data = request.get_json()

        # Validate input
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        text_prompt = data.get('text_prompt', '')
        image_path = data.get('image_path', '')
        style_options = data.get('style_options', {})

        if not text_prompt and not image_path:
            return jsonify({'error': 'Either text prompt or image is required'}), 400

        # Create job
        job_id = str(uuid.uuid4())
        active_jobs[job_id] = {
            'id': job_id,
            'status': 'queued',
            'progress': 0,
            'text_prompt': text_prompt,
            'image_path': image_path,
            'style_options': style_options,
            'created_at': datetime.now().isoformat()
        }

        # Start processing in background
        thread = threading.Thread(
            target=generate_character_workflow,
            args=(job_id, image_path, text_prompt, style_options)
        )
        thread.start()

        return jsonify({
            'message': 'Character generation started',
            'job_id': job_id,
            'status': 'queued'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a job."""
    if job_id not in active_jobs:
        return jsonify({'error': 'Job not found'}), 404

    return jsonify(active_jobs[job_id])

@app.route('/api/download/<job_id>/<file_type>', methods=['GET'])
def download_file(job_id, file_type):
    """Download generated files."""
    try:
        print(f"Download request: job_id={job_id}, file_type={file_type}")

        if job_id not in active_jobs:
            print(f"Job {job_id} not found in active_jobs")
            return jsonify({'error': 'Job not found'}), 404

        job = active_jobs[job_id]
        print(f"Job status: {job['status']}")

        if job['status'] != 'completed':
            print(f"Job not completed yet: {job['status']}")
            return jsonify({'error': 'Job not completed yet'}), 400

        # Determine file path based on type
        if file_type == 'model':
            file_path = job.get('model_path')
            filename = f"character_{job_id}.glb"
        elif file_type == 'texture':
            file_path = job.get('texture_path')
            filename = f"character_{job_id}_texture.png"
        elif file_type == 'animation':
            file_path = job.get('animation_path')
            filename = f"character_{job_id}_idle.fbx"
        else:
            print(f"Invalid file type: {file_type}")
            return jsonify({'error': 'Invalid file type'}), 400

        print(f"File path: {file_path}")
        print(f"File exists: {os.path.exists(file_path) if file_path else False}")

        if not file_path:
            print("File path is None")
            return jsonify({'error': 'File path not set'}), 404

        if not os.path.exists(file_path):
            print(f"File does not exist: {file_path}")
            return jsonify({'error': 'File not found on disk'}), 404

        print(f"Sending file: {file_path} as {filename}")
        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        print(f"Download error: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/jobs', methods=['GET'])
def list_jobs():
    """List all jobs."""
    return jsonify({
        'jobs': list(active_jobs.values())
    })

@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    print('Client connected')
    emit('connected', {'message': 'Connected to 3D Character Generator'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    print('Client disconnected')

@socketio.on('join_job')
def handle_join_job(data):
    """Join a job room for updates."""
    job_id = data.get('job_id')
    if job_id:
        # Join the room for this job
        # In a real implementation, you would use join_room(job_id)
        emit('joined_job', {'job_id': job_id})

if __name__ == '__main__':
    print("Starting 3D Character Generator API...")
    print("API will be available at http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
