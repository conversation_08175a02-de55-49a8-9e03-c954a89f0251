"""
3D Character Generator App - Backend API
"""

import os
import json
import uuid
import time
import threading
from datetime import datetime
from flask import Flask, request, jsonify, send_file, send_from_directory
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
from werkzeug.utils import secure_filename
from PIL import Image
import cv2
import numpy as np

# Import our custom modules
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from src.workflow_generators.comfyui_workflow_generator import ComfyUIWorkflowGenerator
from src.integrations.comfyui.mickmumpitz_workflows import MickmumpitzWorkflows

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Enable CORS for all routes
CORS(app, origins=["http://localhost:3000", "http://127.0.0.1:3000", "file://", "*"],
     allow_headers=["Content-Type", "Authorization"],
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins=["http://localhost:3000", "http://127.0.0.1:3000"])

# Configuration
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}

# Create directories
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)
os.makedirs(os.path.join(OUTPUT_FOLDER, 'models'), exist_ok=True)
os.makedirs(os.path.join(OUTPUT_FOLDER, 'textures'), exist_ok=True)
os.makedirs(os.path.join(OUTPUT_FOLDER, 'animations'), exist_ok=True)

# Initialize workflow generators
workflow_generator = ComfyUIWorkflowGenerator()
mickmumpitz_workflows = MickmumpitzWorkflows()

# Store active jobs
active_jobs = {}

def allowed_file(filename):
    """Check if the file extension is allowed."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def process_image(image_path):
    """Process the uploaded image for character generation."""
    try:
        # Load and preprocess the image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError("Could not load image")

        # Convert to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Resize image to standard size
        target_size = (512, 512)
        image_resized = cv2.resize(image_rgb, target_size)

        # Save processed image
        processed_path = image_path.replace('.', '_processed.')
        cv2.imwrite(processed_path, cv2.cvtColor(image_resized, cv2.COLOR_RGB2BGR))

        return processed_path
    except Exception as e:
        raise Exception(f"Error processing image: {str(e)}")

def generate_character_workflow(job_id, image_path, text_prompt, style_options):
    """Generate a character using ComfyUI workflows."""
    try:
        # Update job status
        active_jobs[job_id]['status'] = 'generating_workflow'
        active_jobs[job_id]['progress'] = 10
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

        # Create character generation workflow
        try:
            # Try without mickmumpitz first to avoid dependency issues
            workflow = workflow_generator.create_character_generation_workflow(use_mickmumpitz=False)
        except Exception as workflow_error:
            print(f"Workflow generation error: {workflow_error}")
            # Create a simple fallback workflow
            workflow = {
                "1": {
                    "class_type": "CLIPTextEncode",
                    "inputs": {
                        "text": text_prompt or "A 3D character",
                        "clip": ["4", 1]
                    }
                },
                "2": {
                    "class_type": "KSampler",
                    "inputs": {
                        "seed": 42,
                        "steps": 20,
                        "cfg": 8.0,
                        "sampler_name": "euler",
                        "scheduler": "normal",
                        "denoise": 1.0,
                        "model": ["4", 0],
                        "positive": ["1", 0],
                        "negative": ["3", 0],
                        "latent_image": ["5", 0]
                    }
                },
                "3": {
                    "class_type": "CLIPTextEncode",
                    "inputs": {
                        "text": "bad quality, blurry",
                        "clip": ["4", 1]
                    }
                },
                "4": {
                    "class_type": "CheckpointLoaderSimple",
                    "inputs": {
                        "ckpt_name": "sd_xl_base_1.0.safetensors"
                    }
                },
                "5": {
                    "class_type": "EmptyLatentImage",
                    "inputs": {
                        "width": 512,
                        "height": 512,
                        "batch_size": 1
                    }
                },
                "6": {
                    "class_type": "VAEDecode",
                    "inputs": {
                        "samples": ["2", 0],
                        "vae": ["4", 2]
                    }
                },
                "7": {
                    "class_type": "SaveImage",
                    "inputs": {
                        "filename_prefix": "character",
                        "images": ["6", 0]
                    }
                }
            }

        # Customize workflow based on input
        if text_prompt and isinstance(workflow, dict):
            # Handle ComfyUI workflow format (nodes as dictionary with IDs as keys)
            for node_id, node in workflow.items():
                if isinstance(node, dict) and node.get('class_type') == 'CLIPTextEncode':
                    # Update positive prompt (node "1" in our fallback workflow)
                    if node_id == "1" and 'inputs' in node:
                        node['inputs']['text'] = text_prompt
                        print(f"Updated positive prompt to: {text_prompt}")

        # Save workflow
        workflow_filename = f"character_workflow_{job_id}.json"
        workflow_path = os.path.join(OUTPUT_FOLDER, workflow_filename)

        with open(workflow_path, 'w') as f:
            json.dump(workflow, f, indent=2)

        # Update job status
        active_jobs[job_id]['status'] = 'workflow_created'
        active_jobs[job_id]['progress'] = 30
        active_jobs[job_id]['workflow_path'] = workflow_path
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

        # Simulate ComfyUI processing (in a real implementation, this would call ComfyUI API)
        simulate_comfyui_processing(job_id, workflow_path, image_path, text_prompt)

    except Exception as e:
        active_jobs[job_id]['status'] = 'error'
        active_jobs[job_id]['error'] = str(e)
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

def simulate_comfyui_processing(job_id, workflow_path, image_path, text_prompt):
    """Simulate ComfyUI processing and generate mock 3D character files."""
    try:
        # Update job status
        active_jobs[job_id]['status'] = 'processing_3d'
        active_jobs[job_id]['progress'] = 50
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

        # Simulate processing time
        time.sleep(3)

        # Create mock 3D model files
        model_filename = f"character_{job_id}.glb"
        texture_filename = f"character_{job_id}_texture.png"
        animation_filename = f"character_{job_id}_idle.fbx"

        model_path = os.path.join(OUTPUT_FOLDER, 'models', model_filename)
        texture_path = os.path.join(OUTPUT_FOLDER, 'textures', texture_filename)
        animation_path = os.path.join(OUTPUT_FOLDER, 'animations', animation_filename)

        # Create mock files (in a real implementation, these would be generated by ComfyUI/Blender)
        create_mock_3d_files(model_path, texture_path, animation_path, image_path, text_prompt)

        # Update job status
        active_jobs[job_id]['status'] = 'post_processing'
        active_jobs[job_id]['progress'] = 80
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

        # Simulate post-processing
        time.sleep(2)

        # Finalize job
        active_jobs[job_id]['status'] = 'completed'
        active_jobs[job_id]['progress'] = 100
        active_jobs[job_id]['model_path'] = model_path
        active_jobs[job_id]['texture_path'] = texture_path
        active_jobs[job_id]['animation_path'] = animation_path
        active_jobs[job_id]['completed_at'] = datetime.now().isoformat()

        socketio.emit('job_update', active_jobs[job_id], room=job_id)

    except Exception as e:
        active_jobs[job_id]['status'] = 'error'
        active_jobs[job_id]['error'] = str(e)
        socketio.emit('job_update', active_jobs[job_id], room=job_id)

def create_mock_3d_files(model_path, texture_path, animation_path, image_path, text_prompt):
    """Create mock 3D files for demonstration purposes."""
    try:
        # Create a simple GLB file (mock)
        glb_content = create_simple_glb()
        with open(model_path, 'wb') as f:
            f.write(glb_content)

        # Create a texture based on the input image
        if os.path.exists(image_path):
            # Copy and resize the input image as texture
            image = Image.open(image_path)
            image = image.resize((512, 512))
            image.save(texture_path)
        else:
            # Create a simple colored texture
            create_simple_texture(texture_path, text_prompt)

        # Create a mock animation file
        with open(animation_path, 'w') as f:
            f.write(f"# Mock FBX animation file for character based on: {text_prompt}\n")
            f.write("# This would contain actual animation data in a real implementation\n")

    except Exception as e:
        raise Exception(f"Error creating mock 3D files: {str(e)}")

def create_simple_glb():
    """Create a simple GLB file content."""
    # This is a minimal GLB file structure
    # In a real implementation, this would be generated by a 3D modeling library
    glb_header = b'glTF' + (2).to_bytes(4, 'little') + (0).to_bytes(4, 'little')
    return glb_header + b'\x00' * 100  # Minimal GLB content

def create_simple_texture(texture_path, text_prompt):
    """Create a simple texture based on the text prompt."""
    # Create a simple colored image based on the text prompt
    color = (128, 128, 128)  # Default gray

    # Simple color mapping based on keywords
    if 'red' in text_prompt.lower():
        color = (255, 100, 100)
    elif 'blue' in text_prompt.lower():
        color = (100, 100, 255)
    elif 'green' in text_prompt.lower():
        color = (100, 255, 100)
    elif 'yellow' in text_prompt.lower():
        color = (255, 255, 100)

    # Create image
    image = Image.new('RGB', (512, 512), color)
    image.save(texture_path)

@app.route('/')
def index():
    """Serve the main page."""
    return jsonify({
        'message': '3D Character Generator API',
        'version': '1.0.0',
        'endpoints': {
            'upload': '/api/upload',
            'generate': '/api/generate',
            'status': '/api/status/<job_id>',
            'download': '/api/download/<job_id>/<file_type>'
        }
    })

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle file upload."""
    try:
        print("Upload request received")

        # Check if file is present
        if 'file' not in request.files:
            print("No file in request")
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        print(f"File received: {file.filename}")

        # Check if file is selected
        if file.filename == '':
            print("Empty filename")
            return jsonify({'error': 'No file selected'}), 400

        # Check if file is allowed
        if not allowed_file(file.filename):
            print(f"File type not allowed: {file.filename}")
            return jsonify({'error': 'File type not allowed'}), 400

        # Ensure upload directory exists
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        print(f"Upload folder created/verified: {UPLOAD_FOLDER}")

        # Save file
        filename = secure_filename(file.filename)
        timestamp = str(int(time.time()))
        filename = f"{timestamp}_{filename}"
        file_path = os.path.join(UPLOAD_FOLDER, filename)

        print(f"Saving file to: {file_path}")
        file.save(file_path)

        # Verify file was saved
        if not os.path.exists(file_path):
            print("File was not saved successfully")
            return jsonify({'error': 'Failed to save file'}), 500

        print(f"File saved successfully: {file_path}")

        # Process image
        try:
            processed_path = process_image(file_path)
            print(f"Image processed: {processed_path}")
        except Exception as e:
            print(f"Image processing failed: {e}")
            # Return original path if processing fails
            processed_path = file_path

        return jsonify({
            'message': 'File uploaded successfully',
            'filename': filename,
            'file_path': file_path,
            'processed_path': processed_path
        })

    except Exception as e:
        print(f"Upload error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate', methods=['POST'])
def generate_character():
    """Generate a 3D character."""
    try:
        data = request.get_json()

        # Validate input
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        text_prompt = data.get('text_prompt', '')
        image_path = data.get('image_path', '')
        style_options = data.get('style_options', {})

        if not text_prompt and not image_path:
            return jsonify({'error': 'Either text prompt or image is required'}), 400

        # Create job
        job_id = str(uuid.uuid4())
        active_jobs[job_id] = {
            'id': job_id,
            'status': 'queued',
            'progress': 0,
            'text_prompt': text_prompt,
            'image_path': image_path,
            'style_options': style_options,
            'created_at': datetime.now().isoformat()
        }

        # Start processing in background
        thread = threading.Thread(
            target=generate_character_workflow,
            args=(job_id, image_path, text_prompt, style_options)
        )
        thread.start()

        return jsonify({
            'message': 'Character generation started',
            'job_id': job_id,
            'status': 'queued'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a job."""
    if job_id not in active_jobs:
        return jsonify({'error': 'Job not found'}), 404

    return jsonify(active_jobs[job_id])

@app.route('/api/download/<job_id>/<file_type>', methods=['GET'])
def download_file(job_id, file_type):
    """Download generated files."""
    try:
        if job_id not in active_jobs:
            return jsonify({'error': 'Job not found'}), 404

        job = active_jobs[job_id]

        if job['status'] != 'completed':
            return jsonify({'error': 'Job not completed yet'}), 400

        # Determine file path based on type
        if file_type == 'model':
            file_path = job.get('model_path')
            filename = f"character_{job_id}.glb"
        elif file_type == 'texture':
            file_path = job.get('texture_path')
            filename = f"character_{job_id}_texture.png"
        elif file_type == 'animation':
            file_path = job.get('animation_path')
            filename = f"character_{job_id}_idle.fbx"
        else:
            return jsonify({'error': 'Invalid file type'}), 400

        if not file_path or not os.path.exists(file_path):
            return jsonify({'error': 'File not found'}), 404

        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/jobs', methods=['GET'])
def list_jobs():
    """List all jobs."""
    return jsonify({
        'jobs': list(active_jobs.values())
    })

@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    print('Client connected')
    emit('connected', {'message': 'Connected to 3D Character Generator'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    print('Client disconnected')

@socketio.on('join_job')
def handle_join_job(data):
    """Join a job room for updates."""
    job_id = data.get('job_id')
    if job_id:
        # Join the room for this job
        # In a real implementation, you would use join_room(job_id)
        emit('joined_job', {'job_id': job_id})

if __name__ == '__main__':
    print("Starting 3D Character Generator API...")
    print("API will be available at http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
