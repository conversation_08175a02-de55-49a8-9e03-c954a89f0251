
import unreal
import sys
import os
import json
import random

# Get arguments
args = sys.argv
if len(args) > 1:
    params_json = args[1]
    params = json.loads(params_json)
else:
    params = {}

asset_paths = params.get('asset_paths', [])
positions = params.get('positions', [])
rotations = params.get('rotations', [])
scales = params.get('scales', [])
random_placement = params.get('random_placement', False)
num_instances = params.get('num_instances', 1)
area_size = params.get('area_size', 1000)

# Get the current level
level = unreal.EditorLevelLibrary.get_editor_world()

# Place assets in the level
placed_actors = []

for i, asset_path in enumerate(asset_paths):
    # Check if the asset exists
    if not unreal.EditorAssetLibrary.does_asset_exist(asset_path):
        print(f"Asset not found: {asset_path}")
        continue
    
    # Load the asset
    asset = unreal.EditorAssetLibrary.load_asset(asset_path)
    
    # Determine how many instances to place
    instances_to_place = num_instances
    
    for j in range(instances_to_place):
        # Determine position, rotation, and scale
        if random_placement:
            # Random position within the specified area
            x = random.uniform(-area_size/2, area_size/2)
            y = random.uniform(-area_size/2, area_size/2)
            z = 0  # Place on the ground
            
            # Random rotation
            yaw = random.uniform(0, 360)
            pitch = 0
            roll = 0
            
            # Random scale (within reasonable limits)
            scale_factor = random.uniform(0.8, 1.2)
            scale_x = scale_factor
            scale_y = scale_factor
            scale_z = scale_factor
        else:
            # Use provided position, rotation, and scale if available
            if i < len(positions):
                x, y, z = positions[i]
            else:
                x, y, z = 0, 0, 0
            
            if i < len(rotations):
                yaw, pitch, roll = rotations[i]
            else:
                yaw, pitch, roll = 0, 0, 0
            
            if i < len(scales):
                scale_x, scale_y, scale_z = scales[i]
            else:
                scale_x, scale_y, scale_z = 1, 1, 1
        
        # Create location, rotation, and scale
        location = unreal.Vector(x, y, z)
        rotation = unreal.Rotator(pitch, yaw, roll)
        scale = unreal.Vector(scale_x, scale_y, scale_z)
        
        # Place the asset in the level
        actor = unreal.EditorLevelLibrary.spawn_actor_from_object(asset, location, rotation)
        
        if actor:
            # Set the scale
            actor.set_actor_scale3d(scale)
            placed_actors.append(actor)
            print(f"Placed {asset_path} at {location}")

# Save the level
unreal.EditorLevelLibrary.save_current_level()

print(f"Placed {len(placed_actors)} actors in the level")
