#!/usr/bin/env python3
"""Test Hugging Face token"""

import requests
import json

def test_token():
    token = "*************************************"

    print(f"🔍 Testing token: {token[:10]}...{token[-10:]}")

    # Test 1: Check token validity with Hub API
    print("\n🧪 Testing Hugging Face token validity...")
    try:
        url = "https://huggingface.co/api/whoami"
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(url, headers=headers, timeout=10)

        print(f"Token validation - Status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response text: {response.text}")

        if response.status_code == 200:
            user_info = response.json()
            print(f"✅ Token is valid! User: {user_info.get('name', 'Unknown')}")
        elif response.status_code == 401:
            print("❌ Token is invalid or expired")
            print("💡 Please check:")
            print("   1. Token copied correctly (no extra spaces)")
            print("   2. Token hasn't expired")
            print("   3. Token has 'Read' permissions")
            return False
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"❌ Error validating token: {e}")
        return False

    # Test 2: Try Inference API with a simple model
    print("\n🤖 Testing Inference API...")
    try:
        url = "https://api-inference.huggingface.co/models/distilbert-base-uncased"
        headers = {"Authorization": f"Bearer {token}"}

        payload = {"inputs": "Hello world"}

        response = requests.post(url, headers=headers, json=payload, timeout=15)

        print(f"Inference API - Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")

        if response.status_code == 200:
            print("✅ Inference API is working!")
            return True
        elif response.status_code == 401:
            print("❌ Token authentication failed for Inference API")
            return False
        elif response.status_code == 503:
            print("⚠️ Model is loading, but token is valid")
            return True
        elif response.status_code == 429:
            print("⚠️ Rate limited, but token is valid")
            return True
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error testing Inference API: {e}")
        return False

if __name__ == "__main__":
    test_token()
