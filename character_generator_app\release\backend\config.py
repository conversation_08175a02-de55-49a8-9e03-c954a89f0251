"""
Configuration file for 3D Character Generator
Contains API keys, settings, and configuration options
"""

import os
from typing import Dict, Any

class Config:
    """Configuration class for the application."""
    
    # API Configuration
    HUGGINGFACE_API_TOKEN = os.getenv('HUGGINGFACE_API_TOKEN', '')
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    
    # ComfyUI Configuration
    COMFYUI_HOST = os.getenv('COMFYUI_HOST', '127.0.0.1')
    COMFYUI_PORT = os.getenv('COMFYUI_PORT', '8188')
    COMFYUI_ADDRESS = f"{COMFYUI_HOST}:{COMFYUI_PORT}"
    
    # File Paths
    UPLOAD_FOLDER = 'uploads'
    OUTPUT_FOLDER = 'outputs'
    COMFYUI_INPUT_FOLDER = os.getenv('COMFYUI_INPUT_FOLDER', '')
    COMFYUI_OUTPUT_FOLDER = os.getenv('COMFYUI_OUTPUT_FOLDER', '')
    
    # Generation Settings
    DEFAULT_IMAGE_SIZE = (512, 512)
    MAX_GENERATION_TIME = 300  # 5 minutes
    MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB
    
    # Supported file formats
    ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
    ALLOWED_3D_EXTENSIONS = {'glb', 'gltf', 'obj', 'fbx', 'ply'}
    
    # Model configurations
    HUGGINGFACE_MODELS = {
        'text_to_image': 'stabilityai/stable-diffusion-xl-base-1.0',
        'image_captioning': 'Salesforce/blip-image-captioning-base',
        'text_classification': 'facebook/bart-large-mnli',
        'text_generation': 'microsoft/DialoGPT-medium'
    }
    
    # ComfyUI Model requirements
    COMFYUI_REQUIRED_MODELS = {
        'checkpoint': 'sd_xl_base_1.0.safetensors',
        'vae': 'sdxl_vae.safetensors',
        'clip': 'clip_l.safetensors'
    }
    
    # 3D Generation settings
    GENERATION_SETTINGS = {
        'default_steps': 25,
        'default_cfg': 7.5,
        'default_seed': 42,
        'max_steps': 50,
        'min_steps': 10
    }

def load_config_from_file(config_file: str = 'config.json') -> Dict[str, Any]:
    """Load configuration from a JSON file."""
    import json
    
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Error loading config file: {e}")
        return {}

def save_config_to_file(config: Dict[str, Any], config_file: str = 'config.json'):
    """Save configuration to a JSON file."""
    import json
    
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving config file: {e}")
        return False

def setup_api_keys():
    """Interactive setup for API keys."""
    print("🔧 Setting up API keys for 3D Character Generator")
    print("=" * 50)
    
    config = {}
    
    # Hugging Face API Key
    print("\n1. Hugging Face API Token")
    print("   - Go to: https://huggingface.co/settings/tokens")
    print("   - Create a new token with 'Read' access")
    print("   - Copy the token")
    
    hf_token = input("\nEnter your Hugging Face API token (or press Enter to skip): ").strip()
    if hf_token:
        config['HUGGINGFACE_API_TOKEN'] = hf_token
        os.environ['HUGGINGFACE_API_TOKEN'] = hf_token
        print("✅ Hugging Face API token saved")
    else:
        print("⚠️  Hugging Face API token skipped")
    
    # ComfyUI Configuration
    print("\n2. ComfyUI Configuration")
    comfyui_host = input("Enter ComfyUI host (default: 127.0.0.1): ").strip() or "127.0.0.1"
    comfyui_port = input("Enter ComfyUI port (default: 8188): ").strip() or "8188"
    
    config['COMFYUI_HOST'] = comfyui_host
    config['COMFYUI_PORT'] = comfyui_port
    
    print(f"✅ ComfyUI configured for {comfyui_host}:{comfyui_port}")
    
    # Optional: OpenAI API Key
    print("\n3. OpenAI API Key (Optional)")
    print("   - For advanced text processing")
    openai_key = input("Enter your OpenAI API key (or press Enter to skip): ").strip()
    if openai_key:
        config['OPENAI_API_KEY'] = openai_key
        os.environ['OPENAI_API_KEY'] = openai_key
        print("✅ OpenAI API key saved")
    else:
        print("⚠️  OpenAI API key skipped")
    
    # Save configuration
    if save_config_to_file(config):
        print("\n✅ Configuration saved to config.json")
    else:
        print("\n❌ Failed to save configuration")
    
    return config

def validate_setup() -> Dict[str, bool]:
    """Validate the current setup."""
    results = {}
    
    # Check Hugging Face API
    try:
        from huggingface_client import test_huggingface_connection
        results['huggingface'] = test_huggingface_connection()
    except Exception as e:
        print(f"Hugging Face test failed: {e}")
        results['huggingface'] = False
    
    # Check ComfyUI connection
    try:
        from comfyui_client import test_comfyui_connection
        results['comfyui'] = test_comfyui_connection()
    except Exception as e:
        print(f"ComfyUI test failed: {e}")
        results['comfyui'] = False
    
    # Check required directories
    results['directories'] = True
    for folder in [Config.UPLOAD_FOLDER, Config.OUTPUT_FOLDER]:
        if not os.path.exists(folder):
            try:
                os.makedirs(folder, exist_ok=True)
                print(f"✅ Created directory: {folder}")
            except Exception as e:
                print(f"❌ Failed to create directory {folder}: {e}")
                results['directories'] = False
    
    return results

def print_setup_status():
    """Print the current setup status."""
    print("\n🔍 Current Setup Status")
    print("=" * 30)
    
    results = validate_setup()
    
    for service, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {service.title()}: {'Working' if status else 'Not configured'}")
    
    if all(results.values()):
        print("\n🎉 All systems ready for 3D character generation!")
    else:
        print("\n⚠️  Some services need configuration. Run setup_api_keys() to fix.")

if __name__ == "__main__":
    # Run interactive setup
    setup_api_keys()
    print_setup_status()
