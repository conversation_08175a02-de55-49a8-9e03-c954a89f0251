{"last_node_id": 229, "last_link_id": 595, "nodes": [{"id": 139, "type": "Reroute", "pos": {"0": 270, "1": -860}, "size": [75, 26], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 518}], "outputs": [{"name": "", "type": "MASK", "links": [328], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 147, "type": "Reroute", "pos": {"0": 2330, "1": 560}, "size": [75, 26], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 342}], "outputs": [{"name": "", "type": "LATENT", "links": [343], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 188, "type": "Reroute", "pos": {"0": -2075, "1": 507}, "size": [75, 26], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 471}], "outputs": [{"name": "", "type": "IMAGE", "links": [477], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 191, "type": "Reroute", "pos": {"0": 1190, "1": 510}, "size": [75, 26], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 477}], "outputs": [{"name": "", "type": "IMAGE", "links": [478], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 154, "type": "Reroute", "pos": {"0": 1130, "1": -110}, "size": [75, 26], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 593}], "outputs": [{"name": "", "type": "CONDITIONING", "links": [505], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 137, "type": "Reroute", "pos": {"0": 270, "1": -1200}, "size": [75, 26], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 517}], "outputs": [{"name": "", "type": "MASK", "links": [327], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 170, "type": "ImpactGaussianBlurMask", "pos": {"0": -1300, "1": -1200}, "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 38, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 457}], "outputs": [{"name": "MASK", "type": "MASK", "links": [424, 517], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [10, 6]}, {"id": 171, "type": "ImpactGaussianBlurMask", "pos": {"0": -1310, "1": -860}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 423}], "outputs": [{"name": "MASK", "type": "MASK", "links": [489, 518], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [10, 6]}, {"id": 200, "type": "CLIPSetLastLayer", "pos": {"0": -3230, "1": 1070}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 490}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [491], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 201, "type": "Reroute", "pos": {"0": -2100, "1": 1070}, "size": [75, 26], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 499}], "outputs": [{"name": "", "type": "CLIP", "links": [500, 501, 502, 503, 504], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 67, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -2840, "1": 1050}, "size": {"0": 315, "1": 126}, "flags": {}, "order": 19, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 376}, {"name": "clip", "type": "CLIP", "link": 491}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [594], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [499], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["sdxl\\EnvyAnimeOilXL01.safetensors", 1, 1]}, {"id": 146, "type": "Reroute", "pos": {"0": -2384, "1": 558}, "size": [75, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 341}], "outputs": [{"name": "", "type": "LATENT", "links": [342], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 155, "type": "Reroute", "pos": {"0": 1130, "1": -90}, "size": [75, 26], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 357}], "outputs": [{"name": "", "type": "CONDITIONING", "links": [506], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 133, "type": "CLIPTextEncodeSDXL", "pos": {"0": 50, "1": 160}, "size": {"0": 399.4946594238281, "1": 220.0544891357422}, "flags": {"collapsed": true, "pinned": false}, "order": 29, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 313}, {"name": "text_g", "type": "STRING", "link": 311, "widget": {"name": "text_g"}}, {"name": "text_l", "type": "STRING", "link": 312, "widget": {"name": "text_l"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [314, 315, 316, 317, 357], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncodeSDXL"}, "widgets_values": [1024, 1024, 0, 0, 512, 512, "text, watermark, uniform, patterns, underexposed, ugly, dark, night, oversaturated, high contrast, jpeg, (worst quality, low quality, normal quality, lowres, low details, oversaturated, undersaturated, overexposed, underexposed, grayscale, bw, bad photo, photography, bad art:1.4), (font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2), (blur, blurry, grainy), poorly lit, bad shadow, draft, cropped, out of frame, cut off,, jpeg artifacts, out of focus, glitch, duplicate, (amateur:1.3), ", "text, watermark, uniform, patterns, underexposed, ugly, dark, night, oversaturated, high contrast, jpeg, (worst quality, low quality, normal quality, lowres, low details, oversaturated, undersaturated, overexposed, underexposed, grayscale, bw, bad photo, photography, bad art:1.4), (font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2), (blur, blurry, grainy), poorly lit, bad shadow, draft, cropped, out of frame, cut off,, jpeg artifacts, out of focus, glitch, duplicate, (amateur:1.3), ", true, true], "color": "#322", "bgcolor": "#533"}, {"id": 141, "type": "Reroute", "pos": {"0": 270, "1": -534}, "size": [75, 26], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 519}], "outputs": [{"name": "", "type": "MASK", "links": [331], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 144, "type": "Reroute", "pos": {"0": -2172, "1": -1095}, "size": [75, 26], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 335}], "outputs": [{"name": "", "type": "IMAGE", "links": [362, 363, 364, 366], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 157, "type": "RegionalConditioningColorMask //Inspire", "pos": {"0": -1820, "1": -880}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 503}, {"name": "color_mask", "type": "IMAGE", "link": 362}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [398], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [423], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "RegionalConditioningColorMask //Inspire"}, "widgets_values": ["#FF9000", 1, "default", "LEAVE EMPTY", 0, true]}, {"id": 158, "type": "RegionalConditioningColorMask //Inspire", "pos": {"0": -1820, "1": -550}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 502}, {"name": "color_mask", "type": "IMAGE", "link": 364}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [427], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "RegionalConditioningColorMask //Inspire"}, "widgets_values": ["#EC00FF", 1, "default", "LEAVE EMPTY", 0, true]}, {"id": 159, "type": "RegionalConditioningColorMask //Inspire", "pos": {"0": -1820, "1": -230}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 501}, {"name": "color_mask", "type": "IMAGE", "link": 366}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [401], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [435], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "RegionalConditioningColorMask //Inspire"}, "widgets_values": ["#05FF04", 1, "default", "LEAVE EMPTY", 0, true]}, {"id": 156, "type": "RegionalConditioningColorMask //Inspire", "pos": {"0": -1820, "1": -1220}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 504}, {"name": "color_mask", "type": "IMAGE", "link": 363}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [457], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "RegionalConditioningColorMask //Inspire"}, "widgets_values": ["#00B2FF", 1, "default", "LEAVE EMPTY", 0, true]}, {"id": 172, "type": "ImpactGaussianBlurMask", "pos": {"0": -1300, "1": -531}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 427}], "outputs": [{"name": "MASK", "type": "MASK", "links": [434, 519], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [10, 6]}, {"id": 22, "type": "MaskPreview+", "pos": {"0": -890, "1": -210}, "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 436}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 21, "type": "MaskPreview+", "pos": {"0": -874, "1": -531}, "size": {"0": 216.18955993652344, "1": 246}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 434}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 199, "type": "MaskPreview+", "pos": {"0": -880, "1": -863}, "size": {"0": 219.1777801513672, "1": 246}, "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 489}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 19, "type": "MaskPreview+", "pos": {"0": -880, "1": -1200}, "size": {"0": 219.1777801513672, "1": 246}, "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 424}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 8, "type": "VAEDecode", "pos": {"0": 2970, "1": -130}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 186}, {"name": "vae", "type": "VAE", "link": 353}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [523], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 153, "type": "Reroute", "pos": {"0": 2870, "1": 1210}, "size": [75, 26], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 595}], "outputs": [{"name": "", "type": "VAE", "links": [353], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 202, "type": "SaveImage", "pos": {"0": 3620, "1": -320}, "size": {"0": 315, "1": 270}, "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 523}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 210, "type": "Reroute", "pos": {"0": 1680, "1": 460}, "size": [75, 26], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 536}], "outputs": [{"name": "", "type": "IMAGE", "links": [537], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 208, "type": "Reroute", "pos": {"0": -2080, "1": 460}, "size": [75, 26], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 533}], "outputs": [{"name": "", "type": "IMAGE", "links": [536], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 173, "type": "ImpactGaussianBlurMask", "pos": {"0": -1326, "1": -211}, "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 35, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 435}], "outputs": [{"name": "MASK", "type": "MASK", "links": [436, 520], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [10, 6]}, {"id": 143, "type": "Reroute", "pos": {"0": 270, "1": -210}, "size": [75, 26], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 520}], "outputs": [{"name": "", "type": "MASK", "links": [334], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 203, "type": "LoadImage", "pos": {"0": -3957, "1": -1095}, "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [524], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["Image0090 (9).png", "image"]}, {"id": 204, "type": "LoadImage", "pos": {"0": -3957, "1": -715}, "size": {"0": 315, "1": 314}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [525], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["Image0090 (10).png", "image"]}, {"id": 205, "type": "LoadImage", "pos": {"0": -3957, "1": -347}, "size": {"0": 315, "1": 314}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [532], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["Image0090 (11).png", "image"]}, {"id": 215, "type": "PrimitiveNode", "pos": {"0": -3852, "1": 327}, "size": {"0": 210, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [555, 557, 558, 559], "slot_index": 0, "widget": {"name": "height"}}], "properties": {"Run widget replace on values": false}, "widgets_values": [720, "fixed"]}, {"id": 216, "type": "PrimitiveNode", "pos": {"0": -3852, "1": 543}, "size": {"0": 210, "1": 82}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [556, 560, 561, 562], "slot_index": 0, "widget": {"name": "width"}}], "properties": {"Run widget replace on values": false}, "widgets_values": [1280, "fixed"]}, {"id": 226, "type": "CLIPTextEncode", "pos": {"0": -545, "1": -1122}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 579}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [582], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["((squid)), organic, intricate design, cute, alien", true], "color": "#232", "bgcolor": "#353"}, {"id": 227, "type": "CLIPTextEncode", "pos": {"0": -545, "1": -793}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 580}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [583], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["alien planet, alien plants, white sand, craters, rock formations", true], "color": "#232", "bgcolor": "#353"}, {"id": 229, "type": "CLIPTextEncode", "pos": {"0": -545, "1": -164}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 586}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [585], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["alien planet, oceans, atmosphere, clouds, satellite image", true], "color": "#232", "bgcolor": "#353"}, {"id": 228, "type": "CLIPTextEncode", "pos": {"0": -545, "1": -471}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 590}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [584], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["alien planet, oceans, atmosphere, clouds, satellite image", true], "color": "#232", "bgcolor": "#353"}, {"id": 58, "type": "ConditioningSetMaskAndCombine4", "pos": {"0": 572, "1": -642}, "size": {"0": 355.20001220703125, "1": 374}, "flags": {"collapsed": true}, "order": 48, "mode": 0, "inputs": [{"name": "positive_1", "type": "CONDITIONING", "link": 582}, {"name": "negative_1", "type": "CONDITIONING", "link": 314}, {"name": "positive_2", "type": "CONDITIONING", "link": 583}, {"name": "negative_2", "type": "CONDITIONING", "link": 315}, {"name": "positive_3", "type": "CONDITIONING", "link": 584}, {"name": "negative_3", "type": "CONDITIONING", "link": 316}, {"name": "positive_4", "type": "CONDITIONING", "link": 585}, {"name": "negative_4", "type": "CONDITIONING", "link": 317}, {"name": "mask_1", "type": "MASK", "link": 327}, {"name": "mask_2", "type": "MASK", "link": 328}, {"name": "mask_3", "type": "MASK", "link": 331}, {"name": "mask_4", "type": "MASK", "link": 334}], "outputs": [{"name": "combined_positive", "type": "CONDITIONING", "links": [592], "slot_index": 0, "shape": 3}, {"name": "combined_negative", "type": "CONDITIONING", "links": [], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "ConditioningSetMaskAndCombine4"}, "widgets_values": [1, 1, 1, 1, "default"]}, {"id": 225, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": {"0": 850, "1": -682}, "size": {"0": 342.5999755859375, "1": 46}, "flags": {"collapsed": true}, "order": 49, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 591}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 592}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [593], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}}, {"id": 68, "type": "Reroute", "pos": {"0": -695, "1": 1070}, "size": [75, 26], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 500}], "outputs": [{"name": "", "type": "CLIP", "links": [313, 576, 579, 580, 586, 590], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 223, "type": "CLIPTextEncode", "pos": {"0": -545, "1": -1497}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 576}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [591], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cinematic film still, (masterpiece), cinematic, best quality, 8k, high detail, photography, bokeh, 35mm film movie still, photorealism, 24mm lens, sharp focus, (perfect real extremely details), sony fe 12-24mm f/2.8 gm, close up, 32k uhd, underwater, long exposure photography, wildlife photography, nature documentary, backlight, fog, atmosphere, ", true], "color": "#223", "bgcolor": "#335"}, {"id": 132, "type": "PrimitiveNode", "pos": {"0": -545, "1": 130}, "size": {"0": 400.6640319824219, "1": 212.09536743164062}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [311, 312], "slot_index": 0, "widget": {"name": "text_g"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["text, watermark, uniform, patterns, underexposed, ugly, dark, night, oversaturated, high contrast, jpeg, (worst quality, low quality, normal quality, lowres, low details, oversaturated, undersaturated, overexposed, underexposed, grayscale, bw, bad photo, photography, bad art:1.4), (font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2), (blur, blurry, grainy), poorly lit, bad shadow, draft, cropped, out of frame, cut off,, jpeg artifacts, out of focus, glitch, duplicate, (amateur:1.3), "], "color": "#322", "bgcolor": "#533"}, {"id": 190, "type": "ACN_AdvancedControlNetApply", "pos": {"0": 1390, "1": -110}, "size": {"0": 355.20001220703125, "1": 286}, "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 505}, {"name": "negative", "type": "CONDITIONING", "link": 506}, {"name": "control_net", "type": "CONTROL_NET", "link": 472}, {"name": "image", "type": "IMAGE", "link": 478}, {"name": "mask_optional", "type": "MASK", "link": null}, {"name": "timestep_kf", "type": "TIMESTEP_KEYFRAME", "link": null}, {"name": "latent_kf_override", "type": "LATENT_KEYFRAME", "link": null}, {"name": "weights_override", "type": "CONTROL_NET_WEIGHTS", "link": null}, {"name": "model_optional", "type": "MODEL", "link": null}, {"name": "vae_optional", "type": "VAE", "link": null}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [526], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [527], "slot_index": 1, "shape": 3}, {"name": "model_opt", "type": "MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ACN_AdvancedControlNetApply"}, "widgets_values": [0.8, 0, 0.8, ""], "color": "#223", "bgcolor": "#335"}, {"id": 207, "type": "ControlNetLoaderAdvanced", "pos": {"0": 1810, "1": -220}, "size": {"0": 367.79998779296875, "1": 58}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "timestep_keyframe", "type": "TIMESTEP_KEYFRAME", "link": null}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [530], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ControlNetLoaderAdvanced"}, "widgets_values": ["sdxl\\mistoLine_rank256.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 206, "type": "ACN_AdvancedControlNetApply", "pos": {"0": 1810, "1": -110}, "size": {"0": 355.20001220703125, "1": 286}, "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 526}, {"name": "negative", "type": "CONDITIONING", "link": 527}, {"name": "control_net", "type": "CONTROL_NET", "link": 530}, {"name": "image", "type": "IMAGE", "link": 537}, {"name": "mask_optional", "type": "MASK", "link": null}, {"name": "timestep_kf", "type": "TIMESTEP_KEYFRAME", "link": null}, {"name": "latent_kf_override", "type": "LATENT_KEYFRAME", "link": null}, {"name": "weights_override", "type": "CONTROL_NET_WEIGHTS", "link": null}, {"name": "model_optional", "type": "MODEL", "link": null}, {"name": "vae_optional", "type": "VAE", "link": null}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [528], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [529], "slot_index": 1, "shape": 3}, {"name": "model_opt", "type": "MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ACN_AdvancedControlNetApply"}, "widgets_values": [0.35000000000000003, 0, 1, ""], "color": "#223", "bgcolor": "#335"}, {"id": 189, "type": "ControlNetLoaderAdvanced", "pos": {"0": 1380, "1": -220}, "size": {"0": 367.79998779296875, "1": 58}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "timestep_keyframe", "type": "TIMESTEP_KEYFRAME", "link": null}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [472], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ControlNetLoaderAdvanced"}, "widgets_values": ["control-lora-depth-rank256.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 76, "type": "KSamplerAdvanced", "pos": {"0": 2495, "1": -131}, "size": {"0": 315, "1": 546}, "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 594}, {"name": "positive", "type": "CONDITIONING", "link": 528}, {"name": "negative", "type": "CONDITIONING", "link": 529}, {"name": "latent_image", "type": "LATENT", "link": 343}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [186], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "KSamplerAdvanced"}, "widgets_values": ["enable", 1111873535434758, "fixed", 10, 3.2, "dpmpp_2s_ancestral", "karras", 0, 10000, "disable"]}, {"id": 24, "type": "EmptyLatentImage", "pos": {"0": -3098, "1": 557}, "size": {"0": 315, "1": 106}, "flags": {"collapsed": false}, "order": 9, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 555, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 556, "widget": {"name": "width"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [341], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1280, 720, 1]}, {"id": 160, "type": "CheckpointLoaderSimple", "pos": {"0": -3670, "1": 1050}, "size": {"0": 315, "1": 98}, "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [376], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [490], "slot_index": 1, "shape": 3}, {"name": "VAE", "type": "VAE", "links": [595], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["wildcardxXLTURBO_wildcardxXLTURBOV10.safetensors"]}, {"id": 209, "type": "ImageScale", "pos": {"0": -3098, "1": -317}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 532}, {"name": "width", "type": "INT", "link": 560, "slot_index": 1, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 557, "slot_index": 2, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [533], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 1280, 720, "disabled"]}, {"id": 187, "type": "ImageScale", "pos": {"0": -3098, "1": -685}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 525}, {"name": "width", "type": "INT", "link": 561, "slot_index": 1, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 558, "slot_index": 2, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [471], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 1280, 720, "disabled"]}, {"id": 69, "type": "ImageScale", "pos": {"0": -3098, "1": -1067}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 12, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 524}, {"name": "width", "type": "INT", "link": 562, "slot_index": 1, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 559, "slot_index": 2, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [335], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 1280, 720, "disabled"]}], "links": [[186, 76, 0, 8, 0, "LATENT"], [311, 132, 0, 133, 1, "STRING"], [312, 132, 0, 133, 2, "STRING"], [313, 68, 0, 133, 0, "CLIP"], [314, 133, 0, 58, 1, "CONDITIONING"], [315, 133, 0, 58, 3, "CONDITIONING"], [316, 133, 0, 58, 5, "CONDITIONING"], [317, 133, 0, 58, 7, "CONDITIONING"], [327, 137, 0, 58, 8, "MASK"], [328, 139, 0, 58, 9, "MASK"], [331, 141, 0, 58, 10, "MASK"], [334, 143, 0, 58, 11, "MASK"], [335, 69, 0, 144, 0, "*"], [341, 24, 0, 146, 0, "*"], [342, 146, 0, 147, 0, "*"], [343, 147, 0, 76, 3, "LATENT"], [353, 153, 0, 8, 1, "VAE"], [357, 133, 0, 155, 0, "*"], [362, 144, 0, 157, 1, "IMAGE"], [363, 144, 0, 156, 1, "IMAGE"], [364, 144, 0, 158, 1, "IMAGE"], [366, 144, 0, 159, 1, "IMAGE"], [376, 160, 0, 67, 0, "MODEL"], [398, 157, 0, 161, 0, "CONDITIONING"], [401, 159, 0, 161, 1, "CONDITIONING"], [423, 157, 1, 171, 0, "MASK"], [424, 170, 0, 19, 0, "MASK"], [427, 158, 1, 172, 0, "MASK"], [434, 172, 0, 21, 0, "MASK"], [435, 159, 1, 173, 0, "MASK"], [436, 173, 0, 22, 0, "MASK"], [457, 156, 1, 170, 0, "MASK"], [471, 187, 0, 188, 0, "*"], [472, 189, 0, 190, 2, "CONTROL_NET"], [477, 188, 0, 191, 0, "*"], [478, 191, 0, 190, 3, "IMAGE"], [489, 171, 0, 199, 0, "MASK"], [490, 160, 1, 200, 0, "CLIP"], [491, 200, 0, 67, 1, "CLIP"], [499, 67, 1, 201, 0, "*"], [500, 201, 0, 68, 0, "*"], [501, 201, 0, 159, 0, "CLIP"], [502, 201, 0, 158, 0, "CLIP"], [503, 201, 0, 157, 0, "CLIP"], [504, 201, 0, 156, 0, "CLIP"], [505, 154, 0, 190, 0, "CONDITIONING"], [506, 155, 0, 190, 1, "CONDITIONING"], [517, 170, 0, 137, 0, "*"], [518, 171, 0, 139, 0, "*"], [519, 172, 0, 141, 0, "*"], [520, 173, 0, 143, 0, "*"], [523, 8, 0, 202, 0, "IMAGE"], [524, 203, 0, 69, 0, "IMAGE"], [525, 204, 0, 187, 0, "IMAGE"], [526, 190, 0, 206, 0, "CONDITIONING"], [527, 190, 1, 206, 1, "CONDITIONING"], [528, 206, 0, 76, 1, "CONDITIONING"], [529, 206, 1, 76, 2, "CONDITIONING"], [530, 207, 0, 206, 2, "CONTROL_NET"], [532, 205, 0, 209, 0, "IMAGE"], [533, 209, 0, 208, 0, "*"], [536, 208, 0, 210, 0, "*"], [537, 210, 0, 206, 3, "IMAGE"], [555, 215, 0, 24, 0, "INT"], [556, 216, 0, 24, 1, "INT"], [557, 215, 0, 209, 2, "INT"], [558, 215, 0, 187, 2, "INT"], [559, 215, 0, 69, 2, "INT"], [560, 216, 0, 209, 1, "INT"], [561, 216, 0, 187, 1, "INT"], [562, 216, 0, 69, 1, "INT"], [576, 68, 0, 223, 0, "CLIP"], [579, 68, 0, 226, 0, "CLIP"], [580, 68, 0, 227, 0, "CLIP"], [582, 226, 0, 58, 0, "CONDITIONING"], [583, 227, 0, 58, 2, "CONDITIONING"], [584, 228, 0, 58, 4, "CONDITIONING"], [585, 229, 0, 58, 6, "CONDITIONING"], [586, 68, 0, 229, 0, "CLIP"], [590, 68, 0, 228, 0, "CLIP"], [591, 223, 0, 225, 0, "CONDITIONING"], [592, 58, 0, 225, 1, "CONDITIONING"], [593, 225, 0, 154, 0, "*"], [594, 67, 0, 76, 0, "MODEL"], [595, 160, 2, 153, 0, "*"]], "groups": [{"title": "Group", "bounding": [-565, -1650, 443, 2029], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "Group", "bounding": [-1977, -1319, 1365, 1412], "color": "#8AA", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8954302432554627, "offset": [-924.7645309317179, 660.8161637169409]}}, "version": 0.4}