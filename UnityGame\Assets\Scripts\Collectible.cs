using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Collectible : MonoBehaviour
{
    // Points awarded for collecting this item
    public int pointValue = 10;
    
    // Rotation speed
    public float rotationSpeed = 50f;
    
    // Hover parameters
    public float hoverHeight = 0.5f;
    public float hoverSpeed = 1f;
    
    // Starting position
    private Vector3 startPosition;
    
    // Start is called before the first frame update
    void Start()
    {
        // Store the starting position
        startPosition = transform.position;
    }

    // Update is called once per frame
    void Update()
    {
        // Rotate the collectible
        transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
        
        // Make the collectible hover up and down
        float newY = startPosition.y + Mathf.Sin(Time.time * hoverSpeed) * hoverHeight;
        transform.position = new Vector3(transform.position.x, newY, transform.position.z);
    }
    
    // Called when another collider enters this trigger
    void OnTriggerEnter(Collider other)
    {
        // Check if the player collected this item
        if (other.CompareTag("Player"))
        {
            // Add points to the score
            if (GameManager.instance != null)
            {
                GameManager.instance.AddScore(pointValue);
            }
            
            // Play collection effect
            PlayCollectionEffect();
            
            // Destroy the collectible
            Destroy(gameObject);
        }
    }
    
    // Play a visual effect when collected
    void PlayCollectionEffect()
    {
        // Create a particle effect
        ParticleSystem particleSystem = GetComponentInChildren<ParticleSystem>();
        if (particleSystem != null)
        {
            // Detach the particle system from the parent
            particleSystem.transform.parent = null;
            
            // Play the particle effect
            particleSystem.Play();
            
            // Destroy the particle system after it finishes
            Destroy(particleSystem.gameObject, particleSystem.main.duration);
        }
        
        // Play a sound effect
        AudioSource audioSource = GetComponent<AudioSource>();
        if (audioSource != null)
        {
            // Detach the audio source from the parent
            audioSource.transform.parent = null;
            
            // Play the sound effect
            audioSource.Play();
            
            // Destroy the audio source after it finishes
            Destroy(audioSource.gameObject, audioSource.clip.length);
        }
    }
}
