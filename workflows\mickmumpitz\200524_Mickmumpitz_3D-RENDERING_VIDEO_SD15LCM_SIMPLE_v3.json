{"last_node_id": 351, "last_link_id": 746, "nodes": [{"id": 27, "type": "Concat Text _O", "pos": [4060, -890], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"name": "text1", "type": "STRING", "link": 44, "widget": {"name": "text1"}}, {"name": "text2", "type": "STRING", "link": 45, "widget": {"name": "text2"}, "slot_index": 1}], "outputs": [{"name": "STRING", "type": "STRING", "links": [154], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Concat Text _O"}, "widgets_values": ["((masterpiece)), ((anime)), cartoon, sunny day, best quality, 8k, high detail, award winning, greek temple, underwater scene", "((temple structure)), (large stones), broken, pyramid, greek temple, ancient building", ","], "color": "#232", "bgcolor": "#353"}, {"id": 101, "type": "CLIPTextEncode", "pos": [4240, -890], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 91, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 155}, {"name": "text", "type": "STRING", "link": 154, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [156], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 31, "type": "Concat Text _O", "pos": [4060, -570], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [{"name": "text1", "type": "STRING", "link": 49, "widget": {"name": "text1"}}, {"name": "text2", "type": "STRING", "link": 50, "widget": {"name": "text2"}, "slot_index": 1}], "outputs": [{"name": "STRING", "type": "STRING", "links": [157], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Concat Text _O"}, "widgets_values": ["((masterpiece)), ((anime)), cartoon, sunny day, best quality, 8k, high detail, award winning, greek temple, underwater scene", "((temple structure)), (large stones), broken, pyramid", ","], "color": "#232", "bgcolor": "#353"}, {"id": 102, "type": "CLIPTextEncode", "pos": [4240, -570], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 92, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 158}, {"name": "text", "type": "STRING", "link": 157, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [159], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 40, "type": "Reroute", "pos": [4460, -570], "size": [75, 26], "flags": {}, "order": 143, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 589}], "outputs": [{"name": "", "type": "MASK", "links": [21]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 39, "type": "Reroute", "pos": [4460, -890], "size": [75, 26], "flags": {}, "order": 142, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 591}], "outputs": [{"name": "", "type": "MASK", "links": [20]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 38, "type": "Reroute", "pos": [4460, -1250], "size": [75, 26], "flags": {}, "order": 141, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 593}], "outputs": [{"name": "", "type": "MASK", "links": [19]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 109, "type": "ADE_ApplyAnimateDiffModel", "pos": [4979.103086195403, 2314.700269761583], "size": {"0": 319.20001220703125, "1": 182}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "motion_model", "type": "MOTION_MODEL_ADE", "link": 176}, {"name": "motion_lora", "type": "MOTION_LORA", "link": null}, {"name": "scale_multival", "type": "MULTIVAL", "link": null}, {"name": "effect_multival", "type": "MULTIVAL", "link": null}, {"name": "ad_keyframes", "type": "AD_KEYFRAMES", "link": null}, {"name": "prev_m_models", "type": "M_MODELS", "link": null}], "outputs": [{"name": "M_MODELS", "type": "M_MODELS", "links": [177], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ADE_ApplyAnimateDiffModel"}, "widgets_values": [0, 1], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 76, "type": "ControlNetLoaderAdvanced", "pos": [5930, 1610], "size": {"0": 367.79998779296875, "1": 58}, "flags": {}, "order": 0, "mode": 4, "inputs": [{"name": "timestep_keyframe", "type": "TIMESTEP_KEYFRAME", "link": null}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [113], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoaderAdvanced"}, "widgets_values": ["control_v11p_sd15_normalbae.pth"], "color": "#223", "bgcolor": "#335"}, {"id": 100, "type": "CLIPTextEncode", "pos": [4240, -1250], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 90, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 152}, {"name": "text", "type": "STRING", "link": 151, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [153], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 34, "type": "Concat Text _O", "pos": [4060, -240], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 41, "mode": 0, "inputs": [{"name": "text1", "type": "STRING", "link": 54, "widget": {"name": "text1"}}, {"name": "text2", "type": "STRING", "link": 55, "widget": {"name": "text2"}, "slot_index": 1}], "outputs": [{"name": "STRING", "type": "STRING", "links": [160], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Concat Text _O"}, "widgets_values": ["((masterpiece)), ((anime)), cartoon, sunny day, best quality, 8k, high detail, award winning, greek temple, underwater scene", "((stones concrete)), rubble, plants", ","], "color": "#232", "bgcolor": "#353"}, {"id": 152, "type": "Concat Text _O", "pos": [4060, 110], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 43, "mode": 0, "inputs": [{"name": "text1", "type": "STRING", "link": 264, "widget": {"name": "text1"}}, {"name": "text2", "type": "STRING", "link": 265, "widget": {"name": "text2"}, "slot_index": 1}], "outputs": [{"name": "STRING", "type": "STRING", "links": [263], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Concat Text _O"}, "widgets_values": ["(blue ocean), ocean, deep sea", "((masterpiece)), ((anime)), cartoon, sunny day, best quality, 8k, high detail, award winning, greek temple, underwater scene", ","], "color": "#232", "bgcolor": "#353"}, {"id": 154, "type": "CLIPTextEncode", "pos": [4240, 110], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 95, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 266}, {"name": "text", "type": "STRING", "link": 263, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [267], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 153, "type": "Reroute", "pos": [4460, 110], "size": [75, 26], "flags": {}, "order": 145, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 585}], "outputs": [{"name": "", "type": "MASK", "links": [270], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 160, "type": "Concat Text _O", "pos": [4060, 410], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 44, "mode": 0, "inputs": [{"name": "text1", "type": "STRING", "link": 274, "widget": {"name": "text1"}}, {"name": "text2", "type": "STRING", "link": 277, "widget": {"name": "text2"}, "slot_index": 1}], "outputs": [{"name": "STRING", "type": "STRING", "links": [276], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Concat Text _O"}, "widgets_values": ["(submarine), ", "((masterpiece)), ((anime)), cartoon, sunny day, best quality, 8k, high detail, award winning, greek temple, underwater scene", ","], "color": "#232", "bgcolor": "#353"}, {"id": 161, "type": "Reroute", "pos": [4460, 410], "size": [75, 26], "flags": {}, "order": 146, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 583}], "outputs": [{"name": "", "type": "MASK", "links": [279], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 177, "type": "Reroute", "pos": [4460, 1050], "size": [75, 26], "flags": {}, "order": 148, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 579}], "outputs": [{"name": "", "type": "MASK", "links": [295], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 176, "type": "Concat Text _O", "pos": [4060, 1050], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 42, "mode": 0, "inputs": [{"name": "text1", "type": "STRING", "link": 303, "widget": {"name": "text1"}}, {"name": "text2", "type": "STRING", "link": 302, "widget": {"name": "text2"}, "slot_index": 1}], "outputs": [{"name": "STRING", "type": "STRING", "links": [293], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Concat Text _O"}, "widgets_values": ["", "((masterpiece)), ((anime)), cartoon, sunny day, best quality, 8k, high detail, award winning, greek temple, underwater scene", ","], "color": "#232", "bgcolor": "#353"}, {"id": 162, "type": "CLIPTextEncode", "pos": [4240, 410], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 96, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 312}, {"name": "text", "type": "STRING", "link": 276, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [278], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 178, "type": "CLIPTextEncode", "pos": [4240, 1050], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 98, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 314}, {"name": "text", "type": "STRING", "link": 293, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [297], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 169, "type": "Reroute", "pos": [4460, 730], "size": [75, 26], "flags": {}, "order": 147, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 581}], "outputs": [{"name": "", "type": "MASK", "links": [294], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 170, "type": "CLIPTextEncode", "pos": [4240, 730], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 97, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 313}, {"name": "text", "type": "STRING", "link": 287, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [296], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 168, "type": "Concat Text _O", "pos": [4060, 730], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 45, "mode": 0, "inputs": [{"name": "text1", "type": "STRING", "link": 285, "widget": {"name": "text1"}}, {"name": "text2", "type": "STRING", "link": 300, "widget": {"name": "text2"}, "slot_index": 1}], "outputs": [{"name": "STRING", "type": "STRING", "links": [287], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Concat Text _O"}, "widgets_values": ["((ocean floor)), ", "((masterpiece)), ((anime)), cartoon, sunny day, best quality, 8k, high detail, award winning, greek temple, underwater scene", ","], "color": "#232", "bgcolor": "#353"}, {"id": 150, "type": "ConditioningSetMaskAndCombine4", "pos": [4677, 620], "size": {"0": 355.20001220703125, "1": 374}, "flags": {"collapsed": true}, "order": 150, "mode": 0, "inputs": [{"name": "positive_1", "type": "CONDITIONING", "link": 267}, {"name": "negative_1", "type": "CONDITIONING", "link": 268}, {"name": "positive_2", "type": "CONDITIONING", "link": 278}, {"name": "negative_2", "type": "CONDITIONING", "link": 280}, {"name": "positive_3", "type": "CONDITIONING", "link": 296}, {"name": "negative_3", "type": "CONDITIONING", "link": 298}, {"name": "positive_4", "type": "CONDITIONING", "link": 297}, {"name": "negative_4", "type": "CONDITIONING", "link": 299}, {"name": "mask_1", "type": "MASK", "link": 270}, {"name": "mask_2", "type": "MASK", "link": 279}, {"name": "mask_3", "type": "MASK", "link": 294}, {"name": "mask_4", "type": "MASK", "link": 295}], "outputs": [{"name": "combined_positive", "type": "CONDITIONING", "links": [259], "shape": 3, "slot_index": 0}, {"name": "combined_negative", "type": "CONDITIONING", "links": [], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "ConditioningSetMaskAndCombine4"}, "widgets_values": [1, 1, 1, 1, "default"]}, {"id": 41, "type": "Reroute", "pos": [4460, -240], "size": [75, 26], "flags": {}, "order": 144, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 587}], "outputs": [{"name": "", "type": "MASK", "links": [22]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 103, "type": "CLIPTextEncode", "pos": [4240, -240], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 93, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 161}, {"name": "text", "type": "STRING", "link": 160, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [162], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 171, "type": "MaskPreview+", "pos": [3040, 1050], "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 139, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 288}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 24, "type": "Concat Text _O", "pos": [4060, -1250], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 38, "mode": 0, "inputs": [{"name": "text1", "type": "STRING", "link": 42, "widget": {"name": "text1"}}, {"name": "text2", "type": "STRING", "link": 43, "widget": {"name": "text2"}, "slot_index": 1}], "outputs": [{"name": "STRING", "type": "STRING", "links": [151], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Concat Text _O"}, "widgets_values": ["((masterpiece)), ((anime)), cartoon, sunny day, best quality, 8k, high detail, award winning, greek temple, underwater scene", "((stone monuments)), dirty, stone,  marble, bridge, intricate design", ","], "color": "#232", "bgcolor": "#353"}, {"id": 113, "type": "ControlNetLoaderAdvanced", "pos": [6420, 1610], "size": {"0": 367.79998779296875, "1": 58}, "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "timestep_keyframe", "type": "TIMESTEP_KEYFRAME", "link": null}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [432], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoaderAdvanced"}, "widgets_values": ["control_v11p_sd15_canny.pth"], "color": "#223", "bgcolor": "#335"}, {"id": 108, "type": "ADE_LoopedUniformContextOptions", "pos": [4582.103086195403, 2216.700269761583], "size": {"0": 317.4000244140625, "1": 246}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "prev_context", "type": "CONTEXT_OPTIONS", "link": null}, {"name": "view_opts", "type": "VIEW_OPTS", "link": null}], "outputs": [{"name": "CONTEXT_OPTS", "type": "CONTEXT_OPTIONS", "links": [178], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ADE_LoopedUniformContextOptions"}, "widgets_values": [16, 1, 4, true, "pyramid", false, 0, 1], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 5, "type": "MaskPreview+", "pos": [3060, -570], "size": {"0": 216.18955993652344, "1": 246}, "flags": {}, "order": 129, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 6}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 228, "type": "ImpactImageBatchToImageList", "pos": [2269.513372117256, -1110], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 81, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 439}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [435], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "color": "#233", "bgcolor": "#355"}, {"id": 227, "type": "ImageScale", "pos": [1909.710296544782, -1050], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 71, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 438}, {"name": "width", "type": "INT", "link": 443, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 442, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [439], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 238, "type": "ImpactImageBatchToImageList", "pos": [2269.513372117256, -760], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 82, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 472}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [469], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "color": "#233", "bgcolor": "#355"}, {"id": 236, "type": "MaskFromRGBCMYBW+", "pos": [2269.513372117256, -850], "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 110, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 470}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [473], "shape": 3, "slot_index": 6}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05, 0, false], "color": "#233", "bgcolor": "#355"}, {"id": 235, "type": "BlendModes", "pos": [2269.513372117256, -800], "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 100, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 469}, {"name": "source", "type": "IMAGE", "link": 474}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [470], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 237, "type": "ImageScale", "pos": [1909.710296544782, -700], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 72, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 471}, {"name": "width", "type": "INT", "link": 475, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 476, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [472], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 243, "type": "ImpactImageBatchToImageList", "pos": [2269.513372117256, -450], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 83, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 483}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [480], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "color": "#233", "bgcolor": "#355"}, {"id": 241, "type": "MaskFromRGBCMYBW+", "pos": [2269.513372117256, -540], "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 111, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 481}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [484], "shape": 3, "slot_index": 6}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05, 0, false], "color": "#233", "bgcolor": "#355"}, {"id": 242, "type": "ImageScale", "pos": [1909.710296544782, -390], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 73, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 482}, {"name": "width", "type": "INT", "link": 487, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 488, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [483], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 240, "type": "BlendModes", "pos": [2269.513372117256, -500], "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 101, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 480}, {"name": "source", "type": "IMAGE", "link": 489}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [481], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 245, "type": "BlendModes", "pos": [2269.513372117256, -190], "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 102, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 490}, {"name": "source", "type": "IMAGE", "link": 499}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [491], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 247, "type": "ImageScale", "pos": [1909.710296544782, -80], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 74, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 492}, {"name": "width", "type": "INT", "link": 497, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 498, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [493], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 225, "type": "BlendModes", "pos": [2269.513372117256, -1160], "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 99, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 435}, {"name": "source", "type": "IMAGE", "link": 436}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [437], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 226, "type": "MaskFromRGBCMYBW+", "pos": [2269.710296544782, -1250], "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 109, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 437}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [440], "shape": 3, "slot_index": 6}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05, 0, false], "color": "#233", "bgcolor": "#355"}, {"id": 253, "type": "ImpactImageBatchToImageList", "pos": [2259.513372117256, 190], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 85, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 503}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [500], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "color": "#233", "bgcolor": "#355"}, {"id": 251, "type": "MaskFromRGBCMYBW+", "pos": [2259.513372117256, 100], "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 113, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 501}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [504], "shape": 3, "slot_index": 6}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05, 0, false], "color": "#233", "bgcolor": "#355"}, {"id": 250, "type": "BlendModes", "pos": [2259.513372117256, 140], "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 103, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 500}, {"name": "source", "type": "IMAGE", "link": 509}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [501], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 252, "type": "ImageScale", "pos": [1909.710296544782, 250], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 75, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 502}, {"name": "width", "type": "INT", "link": 507, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 508, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [503], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 258, "type": "ImpactImageBatchToImageList", "pos": [2259.513372117256, 530], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 86, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 513}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [510], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "color": "#233", "bgcolor": "#355"}, {"id": 256, "type": "MaskFromRGBCMYBW+", "pos": [2259.513372117256, 440], "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 114, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 511}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [514], "shape": 3, "slot_index": 6}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05, 0, false], "color": "#233", "bgcolor": "#355"}, {"id": 255, "type": "BlendModes", "pos": [2259.513372117256, 480], "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 104, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 510}, {"name": "source", "type": "IMAGE", "link": 519}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [511], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 257, "type": "ImageScale", "pos": [1909.710296544782, 590], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 76, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 512}, {"name": "width", "type": "INT", "link": 515, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 518, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [513], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 260, "type": "BlendModes", "pos": [2259.513372117256, 800], "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 105, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 520}, {"name": "source", "type": "IMAGE", "link": 553}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [521], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 273, "type": "ImpactImageBatchToImageList", "pos": [2239.513372117256, 1170], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 88, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 558}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [555], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "color": "#233", "bgcolor": "#355"}, {"id": 271, "type": "MaskFromRGBCMYBW+", "pos": [2239.513372117256, 1080], "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 116, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 556}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [559], "shape": 3, "slot_index": 6}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05, 0, false], "color": "#233", "bgcolor": "#355"}, {"id": 270, "type": "BlendModes", "pos": [2239.513372117256, 1120], "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 106, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 555}, {"name": "source", "type": "IMAGE", "link": 560}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [556], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 8, "type": "ConditioningSetMaskAndCombine4", "pos": [4677, -560], "size": {"0": 355.20001220703125, "1": 374}, "flags": {"collapsed": true}, "order": 149, "mode": 0, "inputs": [{"name": "positive_1", "type": "CONDITIONING", "link": 153}, {"name": "negative_1", "type": "CONDITIONING", "link": 169}, {"name": "positive_2", "type": "CONDITIONING", "link": 156}, {"name": "negative_2", "type": "CONDITIONING", "link": 168}, {"name": "positive_3", "type": "CONDITIONING", "link": 159}, {"name": "negative_3", "type": "CONDITIONING", "link": 167}, {"name": "positive_4", "type": "CONDITIONING", "link": 162}, {"name": "negative_4", "type": "CONDITIONING", "link": 166}, {"name": "mask_1", "type": "MASK", "link": 19}, {"name": "mask_2", "type": "MASK", "link": 20}, {"name": "mask_3", "type": "MASK", "link": 21}, {"name": "mask_4", "type": "MASK", "link": 22}], "outputs": [{"name": "combined_positive", "type": "CONDITIONING", "links": [258], "shape": 3, "slot_index": 0}, {"name": "combined_negative", "type": "CONDITIONING", "links": [], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "ConditioningSetMaskAndCombine4"}, "widgets_values": [1, 1, 1, 1, "default"]}, {"id": 173, "type": "ImpactGaussianBlurMask", "pos": [2510, 1050], "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 124, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 559}], "outputs": [{"name": "MASK", "type": "MASK", "links": [288, 549, 578], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 165, "type": "ImpactGaussianBlurMask", "pos": [2510, 730], "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 123, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 524}], "outputs": [{"name": "MASK", "type": "MASK", "links": [282, 580], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 147, "type": "ImpactGaussianBlurMask", "pos": [2509.710296544782, 90], "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 121, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 504}], "outputs": [{"name": "MASK", "type": "MASK", "links": [254, 584], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 55, "type": "ImpactGaussianBlurMask", "pos": [2509.710296544782, -560], "size": {"0": 315, "1": 82}, "flags": {}, "order": 119, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 484}], "outputs": [{"name": "MASK", "type": "MASK", "links": [6, 588], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 54, "type": "ImpactGaussianBlurMask", "pos": [2509.710296544782, -880], "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 118, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 473}], "outputs": [{"name": "MASK", "type": "MASK", "links": [95, 543, 590], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 172, "type": "PrimitiveNode", "pos": [3400, 1050], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [303], "slot_index": 0, "widget": {"name": "text1"}}], "properties": {"Run widget replace on values": false}, "widgets_values": [""], "color": "#232", "bgcolor": "#353"}, {"id": 286, "type": "MaskListToMaskBatch", "pos": [2850, -840], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 128, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 590}], "outputs": [{"name": "MASK", "type": "MASK", "links": [591], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "color": "#233", "bgcolor": "#355"}, {"id": 285, "type": "MaskListToMaskBatch", "pos": [2850, -530], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 130, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 588}], "outputs": [{"name": "MASK", "type": "MASK", "links": [589], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "color": "#233", "bgcolor": "#355"}, {"id": 284, "type": "MaskListToMaskBatch", "pos": [2850, -190], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 132, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 586}], "outputs": [{"name": "MASK", "type": "MASK", "links": [587], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "color": "#233", "bgcolor": "#355"}, {"id": 283, "type": "MaskListToMaskBatch", "pos": [2850, 120], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 134, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 584}], "outputs": [{"name": "MASK", "type": "MASK", "links": [585], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "color": "#233", "bgcolor": "#355"}, {"id": 280, "type": "MaskListToMaskBatch", "pos": [2850, 1050], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 140, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 578}], "outputs": [{"name": "MASK", "type": "MASK", "links": [579], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "color": "#233", "bgcolor": "#355"}, {"id": 15, "type": "ImageScale", "pos": [1133.5805686188803, 625.6460091111527], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 48, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 30}, {"name": "width", "type": "INT", "link": 459, "widget": {"name": "width"}, "slot_index": 1}, {"name": "height", "type": "INT", "link": 460, "widget": {"name": "height"}, "slot_index": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [441, 605], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 768, 432, "disabled"], "color": "#232", "bgcolor": "#353"}, {"id": 246, "type": "MaskFromRGBCMYBW+", "pos": [2269.710296544782, -230], "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 112, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 491}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [494], "shape": 3, "slot_index": 6}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.03, 0.03, 0.03, 0, false], "color": "#233", "bgcolor": "#355"}, {"id": 248, "type": "ImpactImageBatchToImageList", "pos": [2269.710296544782, -140], "size": {"0": 210, "1": 26}, "flags": {"collapsed": false}, "order": 84, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 493}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [490], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "color": "#233", "bgcolor": "#355"}, {"id": 263, "type": "ImpactImageBatchToImageList", "pos": [2259.710296544782, 850], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 87, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 523}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [520], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "color": "#233", "bgcolor": "#355"}, {"id": 53, "type": "ImpactGaussianBlurMask", "pos": [2509.710296544782, -1250], "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 117, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 440}], "outputs": [{"name": "MASK", "type": "MASK", "links": [5, 592], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 56, "type": "ImpactGaussianBlurMask", "pos": [2509.710296544782, -230], "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 120, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 494}], "outputs": [{"name": "MASK", "type": "MASK", "links": [7, 545, 586], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 64, "type": "MaskPreview+", "pos": [3050, -890], "size": {"0": 219.1777801513672, "1": 246}, "flags": {}, "order": 127, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 95}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 224, "type": "SolidColorRGB", "pos": [1909.710296544782, -1250], "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 444, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 446, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [438], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#ffe906"], "color": "#233", "bgcolor": "#355"}, {"id": 234, "type": "SolidColorRGB", "pos": [1909.710296544782, -890], "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 478, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 479, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [471], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#009050"], "color": "#233", "bgcolor": "#355"}, {"id": 239, "type": "SolidColorRGB", "pos": [1909.710296544782, -580], "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 486, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 485, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [482], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#16013e"], "color": "#233", "bgcolor": "#355"}, {"id": 145, "type": "MaskPreview+", "pos": [3040, 80], "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 133, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 254}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 244, "type": "SolidColorRGB", "pos": [1909.710296544782, -270], "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 496, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 495, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [492], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#00dceb"], "color": "#233", "bgcolor": "#355"}, {"id": 249, "type": "SolidColorRGB", "pos": [1909.710296544782, 60.00000000000001], "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 506, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 505, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [502], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#0073f4"], "color": "#233", "bgcolor": "#355"}, {"id": 36, "type": "PrimitiveNode", "pos": [3403, 1377.547797781892], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [163], "slot_index": 0, "widget": {"name": "text"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["text, watermark, uniform, patterns, underexposed, ugly, high contrast, jpeg, (worst quality, low quality, lowres, low details, overexposed, underexposed, grayscale, bw,  bad art:1.4), (font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2), (blur, blurry, grainy), poorly lit, bad shadow, draft, cropped, out of frame, cut off, censored, jpeg artifacts, out of focus, glitch, duplicate, (amateur:1.3), JuggernautNegative"], "color": "#322", "bgcolor": "#533"}, {"id": 209, "type": "Reroute", "pos": [1760, 380], "size": [75, 26], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 451, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [442, 444, 476, 478, 486, 488, 496, 498, 506, 508, 517, 518, 552, 554, 563, 564], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 208, "type": "Reroute", "pos": [1760, 280], "size": [75, 26], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 450, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [443, 446, 475, 479, 485, 487, 495, 497, 505, 507, 515, 516, 527, 528, 561, 562], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 281, "type": "MaskListToMaskBatch", "pos": [2850, 730], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 138, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 580}], "outputs": [{"name": "MASK", "type": "MASK", "links": [581], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "color": "#233", "bgcolor": "#355"}, {"id": 107, "type": "ADE_LoadAnimateDiffModel", "pos": [4979.103086195403, 2208.700269761583], "size": {"0": 315, "1": 58}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "ad_settings", "type": "AD_SETTINGS", "link": null}], "outputs": [{"name": "MOTION_MODEL", "type": "MOTION_MODEL_ADE", "links": [176], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ADE_LoadAnimateDiffModel"}, "widgets_values": ["sd15_t2v_beta.ckpt"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 92, "type": "Note", "pos": [363.737107000195, 945.526338328969], "size": {"0": 210, "1": 58}, "flags": {}, "order": 6, "mode": 0, "properties": {"text": ""}, "widgets_values": ["<PERSON>ad De<PERSON>h here -->"], "color": "#432", "bgcolor": "#653"}, {"id": 77, "type": "ACN_AdvancedControlNetApply", "pos": [5940, 1730], "size": {"0": 355.20001220703125, "1": 266}, "flags": {}, "order": 154, "mode": 4, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 114}, {"name": "negative", "type": "CONDITIONING", "link": 115}, {"name": "control_net", "type": "CONTROL_NET", "link": 113}, {"name": "image", "type": "IMAGE", "link": 727}, {"name": "mask_optional", "type": "MASK", "link": null}, {"name": "timestep_kf", "type": "TIMESTEP_KEYFRAME", "link": null}, {"name": "latent_kf_override", "type": "LATENT_KEYFRAME", "link": null}, {"name": "weights_override", "type": "CONTROL_NET_WEIGHTS", "link": null}, {"name": "model_optional", "type": "MODEL", "link": null}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [186], "shape": 3, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [187], "shape": 3, "slot_index": 1}, {"name": "model_opt", "type": "MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ACN_AdvancedControlNetApply"}, "widgets_values": [0.3, 0, 1], "color": "#223", "bgcolor": "#335"}, {"id": 93, "type": "Note", "pos": [363.58056861888014, 1285.6460091111535], "size": {"0": 210, "1": 75.61187744140625}, "flags": {}, "order": 7, "mode": 0, "properties": {"text": ""}, "widgets_values": ["if you have an additional Lineart / Freestyle Pass, activate these nodes and the 2nd ControlNet and load the images here -->"], "color": "#432", "bgcolor": "#653"}, {"id": 118, "type": "Note", "pos": [343.58056861888014, 1605.6460091111535], "size": {"0": 210, "1": 75.61187744140625}, "flags": {}, "order": 8, "mode": 0, "properties": {"text": ""}, "widgets_values": ["if you have an additional Normal Pass, activate these nodes and the 2nd ControlNet and load the images here -->"], "color": "#432", "bgcolor": "#653"}, {"id": 57, "type": "VHS_LoadImagesPath", "pos": [653.5805686188805, 945.6460091111527], "size": [293.7653503417969, 122], "flags": {"collapsed": false}, "order": 32, "mode": 0, "inputs": [{"name": "image_load_cap", "type": "INT", "link": 455, "widget": {"name": "image_load_cap"}}, {"name": "skip_first_images", "type": "INT", "link": 632, "widget": {"name": "skip_first_images"}}, {"name": "select_every_nth", "type": "INT", "link": 650, "widget": {"name": "select_every_nth"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [716], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}, {"name": "INT", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_LoadImagesPath"}, "widgets_values": {"directory": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\depth_v01_", "image_load_cap": 10, "skip_first_images": 0, "select_every_nth": 1, "choose folder to upload": "image", "videopreview": {"hidden": true, "paused": false, "params": {"frame_load_cap": 10, "skip_first_images": 0, "filename": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\depth_v01_", "type": "path", "format": "folder", "select_every_nth": 1}}}, "color": "#223", "bgcolor": "#335"}, {"id": 61, "type": "ControlNetLoaderAdvanced", "pos": [5510, 1620], "size": {"0": 367.79998779296875, "1": 58}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "timestep_keyframe", "type": "TIMESTEP_KEYFRAME", "link": null}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [92], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoaderAdvanced"}, "widgets_values": ["control_v11f1p_sd15_depth.pth"], "color": "#223", "bgcolor": "#335"}, {"id": 2, "type": "VHS_LoadImagesPath", "pos": [653.5805686188805, 625.6460091111527], "size": [291.70172119140625, 122], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "image_load_cap", "type": "INT", "link": 454, "widget": {"name": "image_load_cap"}}, {"name": "skip_first_images", "type": "INT", "link": 631, "widget": {"name": "skip_first_images"}}, {"name": "select_every_nth", "type": "INT", "link": 649, "widget": {"name": "select_every_nth"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [30], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}, {"name": "INT", "type": "INT", "links": null, "shape": 3}], "title": "Load Images (Path) MASK 🎥🅥🅗🅢", "properties": {"Node name for S&R": "VHS_LoadImagesPath"}, "widgets_values": {"directory": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\crypto_v01_", "image_load_cap": 10, "skip_first_images": 0, "select_every_nth": 1, "choose folder to upload": "image", "videopreview": {"hidden": true, "paused": false, "params": {"frame_load_cap": 10, "skip_first_images": 0, "filename": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\crypto_v01_", "type": "path", "format": "folder", "select_every_nth": 1}}}, "color": "#232", "bgcolor": "#353"}, {"id": 320, "type": "GetNode", "pos": [5515, 1506], "size": {"0": 210, "1": 58}, "flags": {}, "order": 10, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [689], "slot_index": 0}], "title": "Get_DEPTH", "properties": {}, "widgets_values": ["DEPTH"], "color": "#223", "bgcolor": "#335"}, {"id": 14, "type": "Reroute", "pos": [4116, 1580], "size": [75, 26], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 741}], "outputs": [{"name": "", "type": "CLIP", "links": [152, 155, 158, 161, 170, 266, 312, 313, 314], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 340, "type": "SetNode", "pos": [1123.2382361646387, 1988.4233107855903], "size": {"0": 210, "1": 58}, "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "VAE", "type": "VAE", "link": 719}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_VAE", "properties": {"previousName": "VAE"}, "widgets_values": ["VAE"], "color": "#323", "bgcolor": "#535"}, {"id": 341, "type": "GetNode", "pos": [7710, 1610], "size": {"0": 210, "1": 58}, "flags": {}, "order": 11, "mode": 0, "outputs": [{"name": "VAE", "type": "VAE", "links": [706], "slot_index": 0}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#323", "bgcolor": "#535"}, {"id": 319, "type": "SetNode", "pos": [1094, 1006], "size": {"0": 210, "1": 58}, "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 715}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_DEPTH", "properties": {"previousName": "DEPTH"}, "widgets_values": ["DEPTH"], "color": "#223", "bgcolor": "#335"}, {"id": 47, "type": "Reroute", "pos": [5272, 1749], "size": [75, 26], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 175}], "outputs": [{"name": "", "type": "CONDITIONING", "links": [91], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 46, "type": "Reroute", "pos": [5272, 1728], "size": [75, 26], "flags": {}, "order": 152, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 260}], "outputs": [{"name": "", "type": "CONDITIONING", "links": [90]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 229, "type": "ImpactImageBatchToImageList", "pos": [1371, 552], "size": {"0": 210, "1": 26}, "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 441}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [436, 474, 489, 499, 509, 519, 553, 560], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "color": "#232", "bgcolor": "#353"}, {"id": 151, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [4955, 38], "size": {"0": 342.5999755859375, "1": 46}, "flags": {"collapsed": true}, "order": 151, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 258}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 259}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [260], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}}, {"id": 104, "type": "CLIPTextEncode", "pos": [4240, 1407], "size": {"0": 400, "1": 200}, "flags": {"collapsed": true}, "order": 94, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 170}, {"name": "text", "type": "STRING", "link": 163, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [166, 167, 168, 169, 175, 268, 280, 298, 299], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark, uniform, patterns, underexposed, ugly, high contrast, jpeg, (worst quality, low quality, lowres, low details, overexposed, underexposed, grayscale, bw,  bad art:1.4), (font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2), (blur, blurry, grainy), poorly lit, bad shadow, draft, cropped, out of frame, cut off, censored, jpeg artifacts, out of focus, glitch, duplicate, (amateur:1.3), JuggernautNegative"]}, {"id": 117, "type": "ImageScale", "pos": [1134, 1606], "size": {"0": 210, "1": 122}, "flags": {"collapsed": true}, "order": 50, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 191}, {"name": "width", "type": "INT", "link": 465, "widget": {"name": "width"}, "slot_index": 1}, {"name": "height", "type": "INT", "link": 466, "widget": {"name": "height"}, "slot_index": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [190, 713], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 768, 432, "disabled"], "color": "#432", "bgcolor": "#653"}, {"id": 79, "type": "ImageScale", "pos": [1124, 1286], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 52, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 119}, {"name": "width", "type": "INT", "link": 572, "widget": {"name": "width"}, "slot_index": 1}, {"name": "height", "type": "INT", "link": 573, "widget": {"name": "height"}, "slot_index": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [125, 714], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 768, 432, "disabled"], "color": "#223", "bgcolor": "#335"}, {"id": 59, "type": "ImageScale", "pos": [1124, 946], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 49, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 716}, {"name": "width", "type": "INT", "link": 570, "widget": {"name": "width"}, "slot_index": 1}, {"name": "height", "type": "INT", "link": 571, "widget": {"name": "height"}, "slot_index": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [610, 715], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 768, 432, "disabled"], "color": "#223", "bgcolor": "#335"}, {"id": 287, "type": "MaskListToMaskBatch", "pos": [2850, -1240], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 126, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 592}], "outputs": [{"name": "MASK", "type": "MASK", "links": [593], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "color": "#233", "bgcolor": "#355"}, {"id": 4, "type": "MaskPreview+", "pos": [3050, -1260], "size": {"0": 219.1777801513672, "1": 246}, "flags": {}, "order": 125, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 5}], "title": "🔧 Mask 1", "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 1, "type": "VAEDecode", "pos": [7710, 1710], "size": {"0": 210, "1": 46}, "flags": {}, "order": 157, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 332}, {"name": "vae", "type": "VAE", "link": 706}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [647], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "color": "#323", "bgcolor": "#535"}, {"id": 194, "type": "VHS_VideoCombine", "pos": [8548, 1201], "size": [900, 799], "flags": {}, "order": 159, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 648}, {"name": "audio", "type": "VHS_AUDIO", "link": null}, {"name": "meta_batch", "type": "VHS_BatchManager", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "preview", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 4, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "preview_00236.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4"}}}}, {"id": 339, "type": "GetNode", "pos": [6880, 1833], "size": {"0": 210, "1": 58}, "flags": {}, "order": 12, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [703], "slot_index": 0}], "title": "Get_LATENT", "properties": {}, "widgets_values": ["LATENT"], "color": "#323", "bgcolor": "#535"}, {"id": 115, "type": "VHS_LoadImagesPath", "pos": [653.5805686188805, 1605.6460091111535], "size": [300, 122], "flags": {"collapsed": false}, "order": 33, "mode": 0, "inputs": [{"name": "image_load_cap", "type": "INT", "link": 457, "widget": {"name": "image_load_cap"}, "slot_index": 0}, {"name": "skip_first_images", "type": "INT", "link": 635, "widget": {"name": "skip_first_images"}}, {"name": "select_every_nth", "type": "INT", "link": 652, "widget": {"name": "select_every_nth"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [191], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}, {"name": "INT", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_LoadImagesPath"}, "widgets_values": {"directory": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\normal_v01", "image_load_cap": 10, "skip_first_images": 0, "select_every_nth": 1, "choose folder to upload": "image", "videopreview": {"hidden": true, "paused": false, "params": {"frame_load_cap": 10, "skip_first_images": 0, "filename": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\normal_v01", "type": "path", "format": "folder", "select_every_nth": 1}}}, "color": "#432", "bgcolor": "#653"}, {"id": 323, "type": "SetNode", "pos": [1095.7371070001952, 1661.5263383289694], "size": {"0": 210, "1": 58}, "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 713}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_NORMAL", "properties": {"previousName": "NORMAL"}, "widgets_values": ["NORMAL"], "color": "#432", "bgcolor": "#653"}, {"id": 322, "type": "SetNode", "pos": [1084, 1340], "size": {"0": 210, "1": 58}, "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 714}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_LINE", "properties": {"previousName": "LINE"}, "widgets_values": ["LINE"], "color": "#223", "bgcolor": "#335"}, {"id": 330, "type": "GetNode", "pos": [6416, 1487], "size": {"0": 210, "1": 58}, "flags": {}, "order": 13, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [726], "slot_index": 0}], "title": "Get_LINE", "properties": {}, "widgets_values": ["LINE"], "color": "#223", "bgcolor": "#335"}, {"id": 331, "type": "GetNode", "pos": [5934, 1501], "size": {"0": 210, "1": 58}, "flags": {}, "order": 14, "mode": 4, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [727], "slot_index": 0}], "title": "Get_NORMAL", "properties": {}, "widgets_values": ["NORMAL"], "color": "#223", "bgcolor": "#335"}, {"id": 110, "type": "ADE_UseEvolvedSampling", "pos": [4985.314140572321, 2536.874482807285], "size": {"0": 315, "1": 118}, "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 745}, {"name": "m_models", "type": "M_MODELS", "link": 177}, {"name": "context_options", "type": "CONTEXT_OPTIONS", "link": 178}, {"name": "sample_settings", "type": "SAMPLE_SETTINGS", "link": null}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [328], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ADE_UseEvolvedSampling"}, "widgets_values": ["autoselect"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 65, "type": "CLIPSetLastLayer", "pos": [974.0008001353383, 2294.9474198573666], "size": {"0": 315, "1": 58}, "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 718}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [620], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-1], "color": "#323", "bgcolor": "#535"}, {"id": 62, "type": "ACN_AdvancedControlNetApply", "pos": [5510, 1730], "size": {"0": 355.20001220703125, "1": 266}, "flags": {}, "order": 153, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 90}, {"name": "negative", "type": "CONDITIONING", "link": 91}, {"name": "control_net", "type": "CONTROL_NET", "link": 92}, {"name": "image", "type": "IMAGE", "link": 689}, {"name": "mask_optional", "type": "MASK", "link": null}, {"name": "timestep_kf", "type": "TIMESTEP_KEYFRAME", "link": null}, {"name": "latent_kf_override", "type": "LATENT_KEYFRAME", "link": null}, {"name": "weights_override", "type": "CONTROL_NET_WEIGHTS", "link": null}, {"name": "model_optional", "type": "MODEL", "link": null}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [114], "shape": 3, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [115], "shape": 3, "slot_index": 1}, {"name": "model_opt", "type": "MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ACN_AdvancedControlNetApply"}, "widgets_values": [0.98, 0, 1], "color": "#223", "bgcolor": "#335"}, {"id": 114, "type": "ACN_AdvancedControlNetApply", "pos": [6420, 1730], "size": {"0": 355.20001220703125, "1": 266}, "flags": {}, "order": 155, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 186}, {"name": "negative", "type": "CONDITIONING", "link": 187}, {"name": "control_net", "type": "CONTROL_NET", "link": 432}, {"name": "image", "type": "IMAGE", "link": 726}, {"name": "mask_optional", "type": "MASK", "link": null}, {"name": "timestep_kf", "type": "TIMESTEP_KEYFRAME", "link": null}, {"name": "latent_kf_override", "type": "LATENT_KEYFRAME", "link": null}, {"name": "weights_override", "type": "CONTROL_NET_WEIGHTS", "link": null}, {"name": "model_optional", "type": "MODEL", "link": null}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [329], "shape": 3, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [330], "shape": 3, "slot_index": 1}, {"name": "model_opt", "type": "MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ACN_AdvancedControlNetApply"}, "widgets_values": [0.23, 0, 1], "color": "#223", "bgcolor": "#335"}, {"id": 30, "type": "PrimitiveNode", "pos": [3403, -569.4522022181105], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 15, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [50], "slot_index": 0, "widget": {"name": "text2"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["((temple structure)), (large stones), broken, pyramid"], "color": "#232", "bgcolor": "#353"}, {"id": 6, "type": "MaskPreview+", "pos": [3040, -240], "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 131, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 7}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 33, "type": "PrimitiveNode", "pos": [3403, -249.45220221811024], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 16, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [55], "slot_index": 0, "widget": {"name": "text2"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["((stones concrete)), rubble, plants"], "color": "#232", "bgcolor": "#353"}, {"id": 163, "type": "MaskPreview+", "pos": [3040, 730], "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 137, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 282}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 196, "type": "ModelSamplingDiscrete", "pos": [1331.0008001353378, 2120.9474198573666], "size": {"0": 315, "1": 82}, "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 684}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [728], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ModelSamplingDiscrete"}, "widgets_values": ["lcm", false], "color": "#323", "bgcolor": "#535"}, {"id": 91, "type": "Note", "pos": [363.737107000195, 625.526338328969], "size": {"0": 210, "1": 58}, "flags": {}, "order": 17, "mode": 0, "properties": {"text": ""}, "widgets_values": ["Load Mask Pass here -->"], "color": "#432", "bgcolor": "#653"}, {"id": 116, "type": "PreviewImage", "pos": [1384, 1606], "size": {"0": 251.63223266601562, "1": 248.08242797851562}, "flags": {"collapsed": false}, "order": 66, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 190}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#432", "bgcolor": "#653"}, {"id": 78, "type": "VHS_LoadImagesPath", "pos": [662, 1259], "size": [300, 122], "flags": {"collapsed": false}, "order": 35, "mode": 0, "inputs": [{"name": "image_load_cap", "type": "INT", "link": 738, "widget": {"name": "image_load_cap"}, "slot_index": 0}, {"name": "skip_first_images", "type": "INT", "link": 739, "widget": {"name": "skip_first_images"}}, {"name": "select_every_nth", "type": "INT", "link": 740, "widget": {"name": "select_every_nth"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [119], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}, {"name": "INT", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_LoadImagesPath"}, "widgets_values": {"directory": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\freestyle_v01_", "image_load_cap": 10, "skip_first_images": 0, "select_every_nth": 1, "choose folder to upload": "image", "videopreview": {"hidden": true, "paused": false, "params": {"frame_load_cap": 10, "skip_first_images": 0, "filename": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\freestyle_v01_", "type": "path", "format": "folder", "select_every_nth": 1}}}, "color": "#223", "bgcolor": "#335"}, {"id": 82, "type": "PreviewImage", "pos": [1374, 1285], "size": {"0": 251.63223266601562, "1": 248.08242797851562}, "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 125}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#223", "bgcolor": "#335"}, {"id": 58, "type": "PreviewImage", "pos": [1375, 946], "size": {"0": 252.15289306640625, "1": 248.84286499023438}, "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 610}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#223", "bgcolor": "#335"}, {"id": 3, "type": "PreviewImage", "pos": [1374, 626], "size": {"0": 252.57337951660156, "1": 246}, "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 605}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#232", "bgcolor": "#353"}, {"id": 297, "type": "DF_Integer", "pos": [234, 386], "size": {"0": 315, "1": 58}, "flags": {}, "order": 18, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [631, 632, 635, 739], "shape": 3, "slot_index": 0}], "title": "SkipFirstImages", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [0]}, {"id": 300, "type": "DF_Integer", "pos": [234, 486], "size": {"0": 315, "1": 58}, "flags": {}, "order": 19, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [649, 650, 652, 740], "shape": 3, "slot_index": 0}], "title": "SelectEveryNth", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [1]}, {"id": 132, "type": "FILM VFI", "pos": [8018, 1710], "size": {"0": 443.4000244140625, "1": 126}, "flags": {}, "order": 158, "mode": 4, "inputs": [{"name": "frames", "type": "IMAGE", "link": 647}, {"name": "optional_interpolation_states", "type": "INTERPOLATION_STATES", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [648], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "FILM VFI"}, "widgets_values": ["film_net_fp32.pt", 10, 2]}, {"id": 28, "type": "PrimitiveNode", "pos": [3403, -884.4522022181108], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 20, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [45], "slot_index": 0, "widget": {"name": "text2"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["((temple structure)), (large stones), broken, pyramid, greek temple, ancient building"], "color": "#232", "bgcolor": "#353"}, {"id": 187, "type": "K<PERSON><PERSON><PERSON>", "pos": [7153, 1710], "size": {"0": 315, "1": 474}, "flags": {}, "order": 156, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 328}, {"name": "positive", "type": "CONDITIONING", "link": 329}, {"name": "negative", "type": "CONDITIONING", "link": 330}, {"name": "latent_image", "type": "LATENT", "link": 703}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [332], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [9984526452378, "fixed", 8, 1.6, "lcm", "sgm_uniform", 1], "color": "#323", "bgcolor": "#535"}, {"id": 233, "type": "DF_Integer", "pos": [234, 286], "size": {"0": 315, "1": 58}, "flags": {}, "order": 21, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [454, 455, 457, 594, 738], "shape": 3, "slot_index": 0}], "title": "ImageLoadCap", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [100]}, {"id": 231, "type": "DF_Integer", "pos": [614, 281], "size": {"0": 315, "1": 58}, "flags": {}, "order": 22, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [450, 459, 465, 570, 572, 575], "shape": 3, "slot_index": 0}], "title": "<PERSON><PERSON><PERSON>", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [768]}, {"id": 232, "type": "DF_Integer", "pos": [609, 381], "size": {"0": 315, "1": 58}, "flags": {}, "order": 23, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [451, 460, 466, 571, 573, 574], "shape": 3, "slot_index": 0}], "title": "Height", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [432]}, {"id": 26, "type": "PrimitiveNode", "pos": [3403, -1250], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 24, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [43], "slot_index": 0, "widget": {"name": "text2"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["((stone monuments)), dirty, stone,  marble, bridge, intricate design"], "color": "#232", "bgcolor": "#353"}, {"id": 25, "type": "PrimitiveNode", "pos": [3403, -1549], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 25, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [42, 44, 49, 54, 265, 277, 300, 302], "slot_index": 0, "widget": {"name": "text1"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["((masterpiece)), ((anime)), cartoon, sunny day, best quality, 8k, high detail, award winning, greek temple, underwater scene"], "color": "#432", "bgcolor": "#653"}, {"id": 146, "type": "PrimitiveNode", "pos": [3403, 80.54779778188944], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 26, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [264], "slot_index": 0, "widget": {"name": "text1"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["(blue ocean), ocean, deep sea"], "color": "#232", "bgcolor": "#353"}, {"id": 156, "type": "PrimitiveNode", "pos": [3400, 410], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 27, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [274], "slot_index": 0, "widget": {"name": "text1"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["(submarine), "], "color": "#232", "bgcolor": "#353"}, {"id": 164, "type": "PrimitiveNode", "pos": [3400, 730], "size": {"0": 364.58697509765625, "1": 225.04115295410156}, "flags": {}, "order": 28, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [285], "slot_index": 0, "widget": {"name": "text1"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["((ocean floor)), "], "color": "#232", "bgcolor": "#353"}, {"id": 269, "type": "SolidColorRGB", "pos": [1910, 1050], "size": {"0": 315.2945251464844, "1": 150}, "flags": {"collapsed": false}, "order": 60, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 563, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 561, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [557], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, ""], "color": "#233", "bgcolor": "#355"}, {"id": 259, "type": "SolidColorRGB", "pos": [1910, 730], "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 554, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 528, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [522], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#00dec8"], "color": "#233", "bgcolor": "#355"}, {"id": 261, "type": "MaskFromRGBCMYBW+", "pos": [2260, 730], "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 115, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 521}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [524], "shape": 3, "slot_index": 6}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05, 0, false], "color": "#233", "bgcolor": "#355"}, {"id": 272, "type": "ImageScale", "pos": [1910, 1240], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 78, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 557}, {"name": "width", "type": "INT", "link": 562, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 564, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [558], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 262, "type": "ImageScale", "pos": [1910, 930], "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 77, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 522}, {"name": "width", "type": "INT", "link": 527, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 552, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [523], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 157, "type": "ImpactGaussianBlurMask", "pos": [2510, 410], "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 122, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 514}], "outputs": [{"name": "MASK", "type": "MASK", "links": [271, 547, 582], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 254, "type": "SolidColorRGB", "pos": [1910, 410], "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 517, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 516, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [512], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#0094d7"], "color": "#233", "bgcolor": "#355"}, {"id": 282, "type": "MaskListToMaskBatch", "pos": [2850, 410], "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 136, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 582}], "outputs": [{"name": "MASK", "type": "MASK", "links": [583], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "color": "#233", "bgcolor": "#355"}, {"id": 155, "type": "MaskPreview+", "pos": [3040, 410], "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 135, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 271}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#233", "bgcolor": "#355"}, {"id": 343, "type": "CheckpointLoaderSimple", "pos": [588.0008001353383, 2016.9474198573653], "size": {"0": 315, "1": 98}, "flags": {}, "order": 29, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [717], "shape": 3, "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [718], "shape": 3, "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [719], "shape": 3, "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["juggernaut_reborn.safetensors"], "color": "#323", "bgcolor": "#535"}, {"id": 13, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [969.0008001353383, 2120.9474198573666], "size": {"0": 315, "1": 126}, "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 717}, {"name": "clip", "type": "CLIP", "link": 620}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [684], "shape": 3, "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [741], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["sd15_lora_beta.safetensors", 1, 1], "color": "#323", "bgcolor": "#535"}, {"id": 348, "type": "FreeU_V2", "pos": [1329, 2260], "size": {"0": 315, "1": 130}, "flags": {"collapsed": false}, "order": 89, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 728}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [745], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "FreeU_V2"}, "widgets_values": [1.3, 1.4, 0.9, 0.2], "color": "#323", "bgcolor": "#535"}, {"id": 90, "type": "Reroute", "pos": [603, 2407], "size": [75, 26], "flags": {"pinned": false}, "order": 34, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 594, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [134], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#323", "bgcolor": "#535"}, {"id": 7, "type": "EmptyLatentImage", "pos": [969, 2408], "size": {"0": 315, "1": 106}, "flags": {"collapsed": false}, "order": 51, "mode": 0, "inputs": [{"name": "batch_size", "type": "INT", "link": 134, "widget": {"name": "batch_size"}}, {"name": "height", "type": "INT", "link": 574, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 575, "widget": {"name": "width"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [746], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 432, 10], "color": "#323", "bgcolor": "#535"}, {"id": 338, "type": "SetNode", "pos": [1410, 2441], "size": {"0": 210, "1": 58}, "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "LATENT", "type": "LATENT", "link": 746}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_LATENT", "properties": {"previousName": "LATENT"}, "widgets_values": ["LATENT"], "color": "#323", "bgcolor": "#535"}], "links": [[5, 53, 0, 4, 0, "MASK"], [6, 55, 0, 5, 0, "MASK"], [7, 56, 0, 6, 0, "MASK"], [19, 38, 0, 8, 8, "MASK"], [20, 39, 0, 8, 9, "MASK"], [21, 40, 0, 8, 10, "MASK"], [22, 41, 0, 8, 11, "MASK"], [30, 2, 0, 15, 0, "IMAGE"], [42, 25, 0, 24, 0, "STRING"], [43, 26, 0, 24, 1, "STRING"], [44, 25, 0, 27, 0, "STRING"], [45, 28, 0, 27, 1, "STRING"], [49, 25, 0, 31, 0, "STRING"], [50, 30, 0, 31, 1, "STRING"], [54, 25, 0, 34, 0, "STRING"], [55, 33, 0, 34, 1, "STRING"], [90, 46, 0, 62, 0, "CONDITIONING"], [91, 47, 0, 62, 1, "CONDITIONING"], [92, 61, 0, 62, 2, "CONTROL_NET"], [95, 54, 0, 64, 0, "MASK"], [113, 76, 0, 77, 2, "CONTROL_NET"], [114, 62, 0, 77, 0, "CONDITIONING"], [115, 62, 1, 77, 1, "CONDITIONING"], [119, 78, 0, 79, 0, "IMAGE"], [125, 79, 0, 82, 0, "IMAGE"], [134, 90, 0, 7, 0, "INT"], [151, 24, 0, 100, 1, "STRING"], [152, 14, 0, 100, 0, "CLIP"], [153, 100, 0, 8, 0, "CONDITIONING"], [154, 27, 0, 101, 1, "STRING"], [155, 14, 0, 101, 0, "CLIP"], [156, 101, 0, 8, 2, "CONDITIONING"], [157, 31, 0, 102, 1, "STRING"], [158, 14, 0, 102, 0, "CLIP"], [159, 102, 0, 8, 4, "CONDITIONING"], [160, 34, 0, 103, 1, "STRING"], [161, 14, 0, 103, 0, "CLIP"], [162, 103, 0, 8, 6, "CONDITIONING"], [163, 36, 0, 104, 1, "STRING"], [166, 104, 0, 8, 7, "CONDITIONING"], [167, 104, 0, 8, 5, "CONDITIONING"], [168, 104, 0, 8, 3, "CONDITIONING"], [169, 104, 0, 8, 1, "CONDITIONING"], [170, 14, 0, 104, 0, "CLIP"], [175, 104, 0, 47, 0, "*"], [176, 107, 0, 109, 0, "MOTION_MODEL_ADE"], [177, 109, 0, 110, 1, "M_MODELS"], [178, 108, 0, 110, 2, "CONTEXT_OPTIONS"], [186, 77, 0, 114, 0, "CONDITIONING"], [187, 77, 1, 114, 1, "CONDITIONING"], [190, 117, 0, 116, 0, "IMAGE"], [191, 115, 0, 117, 0, "IMAGE"], [254, 147, 0, 145, 0, "MASK"], [258, 8, 0, 151, 0, "CONDITIONING"], [259, 150, 0, 151, 1, "CONDITIONING"], [260, 151, 0, 46, 0, "*"], [263, 152, 0, 154, 1, "STRING"], [264, 146, 0, 152, 0, "STRING"], [265, 25, 0, 152, 1, "STRING"], [266, 14, 0, 154, 0, "CLIP"], [267, 154, 0, 150, 0, "CONDITIONING"], [268, 104, 0, 150, 1, "CONDITIONING"], [270, 153, 0, 150, 8, "MASK"], [271, 157, 0, 155, 0, "MASK"], [274, 156, 0, 160, 0, "STRING"], [276, 160, 0, 162, 1, "STRING"], [277, 25, 0, 160, 1, "STRING"], [278, 162, 0, 150, 2, "CONDITIONING"], [279, 161, 0, 150, 9, "MASK"], [280, 104, 0, 150, 3, "CONDITIONING"], [282, 165, 0, 163, 0, "MASK"], [285, 164, 0, 168, 0, "STRING"], [287, 168, 0, 170, 1, "STRING"], [288, 173, 0, 171, 0, "MASK"], [293, 176, 0, 178, 1, "STRING"], [294, 169, 0, 150, 10, "MASK"], [295, 177, 0, 150, 11, "MASK"], [296, 170, 0, 150, 4, "CONDITIONING"], [297, 178, 0, 150, 6, "CONDITIONING"], [298, 104, 0, 150, 5, "CONDITIONING"], [299, 104, 0, 150, 7, "CONDITIONING"], [300, 25, 0, 168, 1, "STRING"], [302, 25, 0, 176, 1, "STRING"], [303, 172, 0, 176, 0, "STRING"], [312, 14, 0, 162, 0, "CLIP"], [313, 14, 0, 170, 0, "CLIP"], [314, 14, 0, 178, 0, "CLIP"], [328, 110, 0, 187, 0, "MODEL"], [329, 114, 0, 187, 1, "CONDITIONING"], [330, 114, 1, 187, 2, "CONDITIONING"], [332, 187, 0, 1, 0, "LATENT"], [432, 113, 0, 114, 2, "CONTROL_NET"], [435, 228, 0, 225, 0, "IMAGE"], [436, 229, 0, 225, 1, "IMAGE"], [437, 225, 0, 226, 0, "IMAGE"], [438, 224, 0, 227, 0, "IMAGE"], [439, 227, 0, 228, 0, "IMAGE"], [440, 226, 6, 53, 0, "MASK"], [441, 15, 0, 229, 0, "IMAGE"], [442, 209, 0, 227, 2, "INT"], [443, 208, 0, 227, 1, "INT"], [444, 209, 0, 224, 0, "INT"], [446, 208, 0, 224, 1, "INT"], [450, 231, 0, 208, 0, "*"], [451, 232, 0, 209, 0, "*"], [454, 233, 0, 2, 0, "INT"], [455, 233, 0, 57, 0, "INT"], [457, 233, 0, 115, 0, "INT"], [459, 231, 0, 15, 1, "INT"], [460, 232, 0, 15, 2, "INT"], [465, 231, 0, 117, 1, "INT"], [466, 232, 0, 117, 2, "INT"], [469, 238, 0, 235, 0, "IMAGE"], [470, 235, 0, 236, 0, "IMAGE"], [471, 234, 0, 237, 0, "IMAGE"], [472, 237, 0, 238, 0, "IMAGE"], [473, 236, 6, 54, 0, "MASK"], [474, 229, 0, 235, 1, "IMAGE"], [475, 208, 0, 237, 1, "INT"], [476, 209, 0, 237, 2, "INT"], [478, 209, 0, 234, 0, "INT"], [479, 208, 0, 234, 1, "INT"], [480, 243, 0, 240, 0, "IMAGE"], [481, 240, 0, 241, 0, "IMAGE"], [482, 239, 0, 242, 0, "IMAGE"], [483, 242, 0, 243, 0, "IMAGE"], [484, 241, 6, 55, 0, "MASK"], [485, 208, 0, 239, 1, "INT"], [486, 209, 0, 239, 0, "INT"], [487, 208, 0, 242, 1, "INT"], [488, 209, 0, 242, 2, "INT"], [489, 229, 0, 240, 1, "IMAGE"], [490, 248, 0, 245, 0, "IMAGE"], [491, 245, 0, 246, 0, "IMAGE"], [492, 244, 0, 247, 0, "IMAGE"], [493, 247, 0, 248, 0, "IMAGE"], [494, 246, 6, 56, 0, "MASK"], [495, 208, 0, 244, 1, "INT"], [496, 209, 0, 244, 0, "INT"], [497, 208, 0, 247, 1, "INT"], [498, 209, 0, 247, 2, "INT"], [499, 229, 0, 245, 1, "IMAGE"], [500, 253, 0, 250, 0, "IMAGE"], [501, 250, 0, 251, 0, "IMAGE"], [502, 249, 0, 252, 0, "IMAGE"], [503, 252, 0, 253, 0, "IMAGE"], [504, 251, 6, 147, 0, "MASK"], [505, 208, 0, 249, 1, "INT"], [506, 209, 0, 249, 0, "INT"], [507, 208, 0, 252, 1, "INT"], [508, 209, 0, 252, 2, "INT"], [509, 229, 0, 250, 1, "IMAGE"], [510, 258, 0, 255, 0, "IMAGE"], [511, 255, 0, 256, 0, "IMAGE"], [512, 254, 0, 257, 0, "IMAGE"], [513, 257, 0, 258, 0, "IMAGE"], [514, 256, 6, 157, 0, "MASK"], [515, 208, 0, 257, 1, "INT"], [516, 208, 0, 254, 1, "INT"], [517, 209, 0, 254, 0, "INT"], [518, 209, 0, 257, 2, "INT"], [519, 229, 0, 255, 1, "IMAGE"], [520, 263, 0, 260, 0, "IMAGE"], [521, 260, 0, 261, 0, "IMAGE"], [522, 259, 0, 262, 0, "IMAGE"], [523, 262, 0, 263, 0, "IMAGE"], [524, 261, 6, 165, 0, "MASK"], [527, 208, 0, 262, 1, "INT"], [528, 208, 0, 259, 1, "INT"], [543, 54, 0, 266, 0, "MASK"], [545, 56, 0, 266, 1, "MASK"], [547, 157, 0, 266, 2, "MASK"], [549, 173, 0, 266, 3, "MASK"], [552, 209, 0, 262, 2, "INT"], [553, 229, 0, 260, 1, "IMAGE"], [554, 209, 0, 259, 0, "INT"], [555, 273, 0, 270, 0, "IMAGE"], [556, 270, 0, 271, 0, "IMAGE"], [557, 269, 0, 272, 0, "IMAGE"], [558, 272, 0, 273, 0, "IMAGE"], [559, 271, 6, 173, 0, "MASK"], [560, 229, 0, 270, 1, "IMAGE"], [561, 208, 0, 269, 1, "INT"], [562, 208, 0, 272, 1, "INT"], [563, 209, 0, 269, 0, "INT"], [564, 209, 0, 272, 2, "INT"], [570, 231, 0, 59, 1, "INT"], [571, 232, 0, 59, 2, "INT"], [572, 231, 0, 79, 1, "INT"], [573, 232, 0, 79, 2, "INT"], [574, 232, 0, 7, 1, "INT"], [575, 231, 0, 7, 2, "INT"], [578, 173, 0, 280, 0, "MASK"], [579, 280, 0, 177, 0, "*"], [580, 165, 0, 281, 0, "MASK"], [581, 281, 0, 169, 0, "*"], [582, 157, 0, 282, 0, "MASK"], [583, 282, 0, 161, 0, "*"], [584, 147, 0, 283, 0, "MASK"], [585, 283, 0, 153, 0, "*"], [586, 56, 0, 284, 0, "MASK"], [587, 284, 0, 41, 0, "*"], [588, 55, 0, 285, 0, "MASK"], [589, 285, 0, 40, 0, "*"], [590, 54, 0, 286, 0, "MASK"], [591, 286, 0, 39, 0, "*"], [592, 53, 0, 287, 0, "MASK"], [593, 287, 0, 38, 0, "*"], [594, 233, 0, 90, 0, "*"], [605, 15, 0, 3, 0, "IMAGE"], [610, 59, 0, 58, 0, "IMAGE"], [620, 65, 0, 13, 1, "CLIP"], [631, 297, 0, 2, 1, "INT"], [632, 297, 0, 57, 1, "INT"], [635, 297, 0, 115, 1, "INT"], [647, 1, 0, 132, 0, "IMAGE"], [648, 132, 0, 194, 0, "IMAGE"], [649, 300, 0, 2, 2, "INT"], [650, 300, 0, 57, 2, "INT"], [652, 300, 0, 115, 2, "INT"], [684, 13, 0, 196, 0, "MODEL"], [689, 320, 0, 62, 3, "IMAGE"], [703, 339, 0, 187, 3, "LATENT"], [706, 341, 0, 1, 1, "VAE"], [713, 117, 0, 323, 0, "IMAGE"], [714, 79, 0, 322, 0, "IMAGE"], [715, 59, 0, 319, 0, "IMAGE"], [716, 57, 0, 59, 0, "IMAGE"], [717, 343, 0, 13, 0, "MODEL"], [718, 343, 1, 65, 0, "CLIP"], [719, 343, 2, 340, 0, "VAE"], [726, 330, 0, 114, 3, "IMAGE"], [727, 331, 0, 77, 3, "IMAGE"], [728, 196, 0, 348, 0, "MODEL"], [738, 233, 0, 78, 0, "INT"], [739, 297, 0, 78, 1, "INT"], [740, 300, 0, 78, 2, "INT"], [741, 13, 1, 14, 0, "*"], [745, 348, 0, 110, 0, "MODEL"], [746, 7, 0, 338, 0, "*"]], "groups": [{"title": "EXTRACT MASKS", "bounding": [1833, -1335, 1463, 2681], "color": "#8AA", "font_size": 24}, {"title": "PROMPTS", "bounding": [3351, -1661, 479, 3326], "color": "#8A8", "font_size": 24}, {"title": "CONTROLNET", "bounding": [5441, 1414, 1383, 601], "color": "#88A", "font_size": 24}, {"title": "ANIMATEDIFF", "bounding": [4562, 2132, 752, 546], "color": "#3f789e", "font_size": 24}, {"title": "INPUT", "bounding": [207, 113, 1470, 1766], "color": "#3f789e", "font_size": 24}, {"title": "MODELS", "bounding": [560, 1920, 1124, 611], "color": "#a1309b", "font_size": 24}], "config": {}, "extra": {"ds": {"scale": 0.25937424601000353, "offset": {"0": 85.82096227578565, "1": 998.7997619123955}}}, "version": 0.4}