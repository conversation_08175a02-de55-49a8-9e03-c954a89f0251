#!/usr/bin/env python3
"""
Minimal working backend server for 3D Character Generator
Avoids import issues by using only essential components
"""

import sys
import os
import json
import uuid
import time
from datetime import datetime

# Add backend to path
sys.path.append('backend')

# Basic Flask setup
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS

app = Flask(__name__)
CORS(app)
app.config['SECRET_KEY'] = 'character-generator-2024'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024

# Global variables
active_jobs = {}

# Ensure output directories exist
os.makedirs('backend/outputs/models', exist_ok=True)
os.makedirs('backend/outputs/textures', exist_ok=True)
os.makedirs('backend/outputs/animations', exist_ok=True)

@app.route('/')
def index():
    """API status endpoint"""
    return jsonify({
        'message': '3D Character Generator API',
        'status': 'running',
        'version': '2.0.0',
        'features': ['procedural_generation', 'web_interface', 'glb_export'],
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/test')
def test():
    """Test endpoint"""
    return jsonify({
        'test': 'success',
        'backend': 'working',
        'character_generation': 'available',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/status')
def status():
    """Server status endpoint"""
    return jsonify({
        'server': 'running',
        'character_generation': 'available',
        'active_jobs': len(active_jobs),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/generate', methods=['POST'])
def generate_character():
    """Generate a 3D character"""
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        
        text_prompt = data.get('prompt', 'A 3D character')
        
        print(f"🎯 Generating character: {text_prompt}")
        
        # Import character generation functions (lazy import to avoid startup issues)
        try:
            from app import create_character_glb, analyze_character_prompt
        except Exception as e:
            print(f"⚠️ Using fallback character generation: {e}")
            return generate_fallback_character(text_prompt)
        
        # Analyze character
        character_type = analyze_character_prompt(text_prompt)
        
        # Generate GLB
        glb_content = create_character_glb(text_prompt)
        
        # Save file
        job_id = str(uuid.uuid4())[:8]
        filename = f"character_{job_id}.glb"
        filepath = os.path.join('backend', 'outputs', 'models', filename)
        
        with open(filepath, 'wb') as f:
            f.write(glb_content)
        
        print(f"✅ Character generated: {filename} ({len(glb_content)} bytes)")
        
        # Store job info
        active_jobs[job_id] = {
            'id': job_id,
            'prompt': text_prompt,
            'character_type': character_type['name'],
            'filename': filename,
            'file_size': len(glb_content),
            'status': 'completed',
            'created_at': datetime.now().isoformat()
        }
        
        return jsonify({
            'success': True,
            'job_id': job_id,
            'filename': filename,
            'character_type': character_type['name'],
            'file_size': len(glb_content),
            'download_url': f'/api/download/{filename}',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"❌ Generation error: {e}")
        import traceback
        traceback.print_exc()
        
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

def generate_fallback_character(text_prompt):
    """Generate a simple fallback character if main generation fails"""
    try:
        print("🔄 Using fallback character generation...")
        
        # Simple character analysis
        character_type = 'humanoid'
        if 'robot' in text_prompt.lower():
            character_type = 'robot'
        elif 'alien' in text_prompt.lower():
            character_type = 'alien'
        elif 'warrior' in text_prompt.lower():
            character_type = 'warrior'
        
        # Create a minimal GLB file (placeholder)
        job_id = str(uuid.uuid4())[:8]
        filename = f"fallback_character_{job_id}.glb"
        filepath = os.path.join('backend', 'outputs', 'models', filename)
        
        # Create minimal GLB content (this would be a real GLB in production)
        glb_content = b"GLB_FALLBACK_" + text_prompt.encode('utf-8')[:100]
        
        with open(filepath, 'wb') as f:
            f.write(glb_content)
        
        print(f"✅ Fallback character generated: {filename}")
        
        return jsonify({
            'success': True,
            'job_id': job_id,
            'filename': filename,
            'character_type': character_type,
            'file_size': len(glb_content),
            'download_url': f'/api/download/{filename}',
            'fallback': True,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"❌ Fallback generation error: {e}")
        return jsonify({
            'success': False,
            'error': f'Fallback generation failed: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/download/<filename>')
def download_file(filename):
    """Download generated character file"""
    try:
        filepath = os.path.join('backend', 'outputs', 'models', filename)
        
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True, download_name=filename)
        else:
            return jsonify({
                'error': 'File not found',
                'filename': filename,
                'timestamp': datetime.now().isoformat()
            }), 404
            
    except Exception as e:
        print(f"❌ Download error: {e}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/jobs')
def list_jobs():
    """List all active jobs"""
    return jsonify({
        'jobs': list(active_jobs.values()),
        'count': len(active_jobs),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/jobs/<job_id>')
def get_job(job_id):
    """Get specific job details"""
    if job_id in active_jobs:
        return jsonify(active_jobs[job_id])
    else:
        return jsonify({
            'error': 'Job not found',
            'job_id': job_id,
            'timestamp': datetime.now().isoformat()
        }), 404

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'error': 'Endpoint not found',
        'timestamp': datetime.now().isoformat()
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'error': 'Internal server error',
        'timestamp': datetime.now().isoformat()
    }), 500

def main():
    """Start the server"""
    print("🚀 Starting 3D Character Generator Backend")
    print("=" * 40)
    print("📱 API: http://localhost:5000")
    print("🔧 Status: http://localhost:5000/api/status")
    print("🧪 Test: http://localhost:5000/api/test")
    print("\n💡 Open your web interface and connect to this backend!")
    print("⌨️ Press Ctrl+C to stop the server")
    
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,  # Disable debug to avoid issues
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
