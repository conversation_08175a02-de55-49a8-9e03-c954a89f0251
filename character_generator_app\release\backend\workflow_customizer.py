#!/usr/bin/env python3
"""
Workflow customization system for different art styles and use cases
"""

import os
import json
import shutil
from datetime import datetime

class WorkflowCustomizer:
    """Customize ComfyUI workflows for different art styles and purposes"""
    
    def __init__(self, comfyui_path):
        self.comfyui_path = comfyui_path
        self.workflows_dir = os.path.join(comfyui_path, "workflows")
        self.custom_workflows_dir = os.path.join(self.workflows_dir, "custom")
        
        # Art style presets
        self.art_styles = {
            "realistic": {
                "description": "Photorealistic characters for film and VR",
                "prompts": {
                    "positive": "photorealistic, detailed skin, natural lighting, high quality, professional photography",
                    "negative": "cartoon, anime, stylized, low quality, blurry, distorted"
                },
                "settings": {
                    "steps": 30,
                    "cfg": 7.5,
                    "denoise": 0.8,
                    "sampler": "dpmpp_2m_sde"
                }
            },
            "anime": {
                "description": "Anime-style characters for games and animation",
                "prompts": {
                    "positive": "anime style, cel shading, vibrant colors, detailed art, professional illustration",
                    "negative": "photorealistic, 3d render, blurry, low quality, western cartoon"
                },
                "settings": {
                    "steps": 25,
                    "cfg": 8.0,
                    "denoise": 0.7,
                    "sampler": "euler_a"
                }
            },
            "cartoon": {
                "description": "Cartoon-style characters for family content",
                "prompts": {
                    "positive": "cartoon style, clean lines, bright colors, friendly appearance, family-friendly",
                    "negative": "realistic, dark, scary, inappropriate, low quality"
                },
                "settings": {
                    "steps": 20,
                    "cfg": 6.5,
                    "denoise": 0.6,
                    "sampler": "euler"
                }
            },
            "fantasy": {
                "description": "Fantasy characters for RPGs and storytelling",
                "prompts": {
                    "positive": "fantasy art, detailed armor, magical elements, epic character design, high fantasy",
                    "negative": "modern, contemporary, low quality, simple, boring"
                },
                "settings": {
                    "steps": 35,
                    "cfg": 8.5,
                    "denoise": 0.9,
                    "sampler": "dpmpp_2m"
                }
            },
            "cyberpunk": {
                "description": "Futuristic cyberpunk characters",
                "prompts": {
                    "positive": "cyberpunk style, neon lights, futuristic, high tech, detailed cybernetics, sci-fi",
                    "negative": "medieval, fantasy, low tech, natural, organic, low quality"
                },
                "settings": {
                    "steps": 28,
                    "cfg": 7.8,
                    "denoise": 0.75,
                    "sampler": "dpmpp_2m_sde"
                }
            },
            "stylized": {
                "description": "Stylized characters for indie games",
                "prompts": {
                    "positive": "stylized art, unique design, creative character, artistic interpretation, indie game style",
                    "negative": "photorealistic, generic, boring, low quality, amateur"
                },
                "settings": {
                    "steps": 25,
                    "cfg": 7.0,
                    "denoise": 0.65,
                    "sampler": "euler_a"
                }
            }
        }
        
        # Use case presets
        self.use_cases = {
            "game_character": {
                "description": "Optimized for game engines (Unity, Unreal)",
                "mesh_settings": {
                    "quality": "high",
                    "target_vertices": 2000,
                    "optimization": "game_ready"
                },
                "material_settings": {
                    "pbr_workflow": True,
                    "texture_resolution": 1024,
                    "optimize_for_realtime": True
                }
            },
            "vr_avatar": {
                "description": "VR-ready avatars with face matching",
                "mesh_settings": {
                    "quality": "medium",
                    "target_vertices": 1500,
                    "optimization": "vr_ready"
                },
                "material_settings": {
                    "pbr_workflow": True,
                    "texture_resolution": 512,
                    "optimize_for_mobile": True
                }
            },
            "film_character": {
                "description": "High-quality characters for film and animation",
                "mesh_settings": {
                    "quality": "ultra",
                    "target_vertices": 5000,
                    "optimization": "quality_first"
                },
                "material_settings": {
                    "pbr_workflow": True,
                    "texture_resolution": 2048,
                    "subsurface_scattering": True
                }
            },
            "social_avatar": {
                "description": "Social media and chat avatars",
                "mesh_settings": {
                    "quality": "medium",
                    "target_vertices": 1000,
                    "optimization": "web_ready"
                },
                "material_settings": {
                    "pbr_workflow": False,
                    "texture_resolution": 512,
                    "simple_materials": True
                }
            }
        }
        
        self.setup_custom_directory()
    
    def setup_custom_directory(self):
        """Setup custom workflows directory"""
        os.makedirs(self.custom_workflows_dir, exist_ok=True)
        print(f"✅ Custom workflows directory ready: {self.custom_workflows_dir}")
    
    def create_custom_workflow(self, base_workflow, art_style, use_case, custom_name=None):
        """Create a customized workflow"""
        try:
            if not custom_name:
                custom_name = f"{art_style}_{use_case}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            print(f"🎨 Creating custom workflow: {custom_name}")
            print(f"   Art style: {art_style}")
            print(f"   Use case: {use_case}")
            
            # Load base workflow
            base_path = os.path.join(self.workflows_dir, base_workflow)
            if not os.path.exists(base_path):
                print(f"❌ Base workflow not found: {base_workflow}")
                return None
            
            with open(base_path, 'r') as f:
                workflow = json.load(f)
            
            # Apply art style customizations
            if art_style in self.art_styles:
                workflow = self.apply_art_style(workflow, art_style)
            
            # Apply use case customizations
            if use_case in self.use_cases:
                workflow = self.apply_use_case(workflow, use_case)
            
            # Add custom metadata
            workflow["custom_metadata"] = {
                "created_date": datetime.now().isoformat(),
                "base_workflow": base_workflow,
                "art_style": art_style,
                "use_case": use_case,
                "custom_name": custom_name,
                "description": f"{self.art_styles.get(art_style, {}).get('description', '')} - {self.use_cases.get(use_case, {}).get('description', '')}"
            }
            
            # Save custom workflow
            custom_path = os.path.join(self.custom_workflows_dir, f"{custom_name}.json")
            with open(custom_path, 'w') as f:
                json.dump(workflow, f, indent=2)
            
            print(f"✅ Custom workflow created: {custom_path}")
            return custom_path
            
        except Exception as e:
            print(f"❌ Failed to create custom workflow: {e}")
            return None
    
    def apply_art_style(self, workflow, art_style):
        """Apply art style settings to workflow"""
        try:
            style_config = self.art_styles[art_style]
            
            # Update text prompts
            for node_id, node in workflow.get("nodes", {}).items():
                if node.get("class_type") == "CLIPTextEncode":
                    title = node.get("_meta", {}).get("title", "").lower()
                    
                    if "positive" in title:
                        current_text = node["inputs"].get("text", "")
                        enhanced_text = f"{style_config['prompts']['positive']}, {current_text}"
                        node["inputs"]["text"] = enhanced_text
                    
                    elif "negative" in title:
                        current_text = node["inputs"].get("text", "")
                        enhanced_text = f"{style_config['prompts']['negative']}, {current_text}"
                        node["inputs"]["text"] = enhanced_text
                
                # Update sampler settings
                elif node.get("class_type") == "KSampler":
                    settings = style_config["settings"]
                    node["inputs"].update({
                        "steps": settings.get("steps", node["inputs"].get("steps", 20)),
                        "cfg": settings.get("cfg", node["inputs"].get("cfg", 7.0)),
                        "denoise": settings.get("denoise", node["inputs"].get("denoise", 1.0)),
                        "sampler_name": settings.get("sampler", node["inputs"].get("sampler_name", "euler"))
                    })
            
            print(f"   ✅ Applied {art_style} art style")
            return workflow
            
        except Exception as e:
            print(f"   ❌ Failed to apply art style: {e}")
            return workflow
    
    def apply_use_case(self, workflow, use_case):
        """Apply use case optimizations to workflow"""
        try:
            use_case_config = self.use_cases[use_case]
            
            # Add use case metadata for the character generator to use
            workflow["use_case_settings"] = use_case_config
            
            # Adjust image dimensions based on use case
            for node_id, node in workflow.get("nodes", {}).items():
                if node.get("class_type") == "EmptyLatentImage":
                    if use_case == "vr_avatar" or use_case == "social_avatar":
                        # Lower resolution for VR/social
                        node["inputs"]["width"] = 512
                        node["inputs"]["height"] = 512
                    elif use_case == "film_character":
                        # Higher resolution for film
                        node["inputs"]["width"] = 1024
                        node["inputs"]["height"] = 1024
                    elif use_case == "game_character":
                        # Standard resolution for games
                        node["inputs"]["width"] = 768
                        node["inputs"]["height"] = 768
            
            print(f"   ✅ Applied {use_case} use case optimizations")
            return workflow
            
        except Exception as e:
            print(f"   ❌ Failed to apply use case: {e}")
            return workflow
    
    def create_style_preset_workflows(self):
        """Create workflows for all art style presets"""
        print("🎨 Creating art style preset workflows...")
        
        base_workflows = [
            "mickmumpitz_character_sheet.json",
            "mickmumpitz_3d_rendering.json",
            "mickmumpitz_face_enhancement.json"
        ]
        
        created_workflows = []
        
        for base_workflow in base_workflows:
            base_path = os.path.join(self.workflows_dir, base_workflow)
            if not os.path.exists(base_path):
                print(f"⚠️ Base workflow not found: {base_workflow}")
                continue
            
            for art_style in self.art_styles.keys():
                custom_name = f"{art_style}_{base_workflow.replace('mickmumpitz_', '').replace('.json', '')}"
                custom_path = self.create_custom_workflow(
                    base_workflow, art_style, "game_character", custom_name
                )
                
                if custom_path:
                    created_workflows.append(custom_path)
        
        print(f"✅ Created {len(created_workflows)} style preset workflows")
        return created_workflows
    
    def create_use_case_workflows(self):
        """Create workflows for all use case presets"""
        print("🎯 Creating use case preset workflows...")
        
        base_workflow = "mickmumpitz_character_sheet.json"
        created_workflows = []
        
        for use_case in self.use_cases.keys():
            custom_name = f"realistic_{use_case}_workflow"
            custom_path = self.create_custom_workflow(
                base_workflow, "realistic", use_case, custom_name
            )
            
            if custom_path:
                created_workflows.append(custom_path)
        
        print(f"✅ Created {len(created_workflows)} use case workflows")
        return created_workflows
    
    def list_custom_workflows(self):
        """List all custom workflows"""
        try:
            custom_files = [f for f in os.listdir(self.custom_workflows_dir) if f.endswith('.json')]
            
            if not custom_files:
                print("📁 No custom workflows found")
                return []
            
            print(f"📁 Custom workflows ({len(custom_files)}):")
            
            workflows_info = []
            for filename in custom_files:
                filepath = os.path.join(self.custom_workflows_dir, filename)
                
                try:
                    with open(filepath, 'r') as f:
                        workflow = json.load(f)
                    
                    metadata = workflow.get("custom_metadata", {})
                    info = {
                        "filename": filename,
                        "path": filepath,
                        "art_style": metadata.get("art_style", "unknown"),
                        "use_case": metadata.get("use_case", "unknown"),
                        "description": metadata.get("description", "No description"),
                        "created": metadata.get("created_date", "unknown")
                    }
                    
                    workflows_info.append(info)
                    print(f"   - {filename}")
                    print(f"     Style: {info['art_style']}, Use case: {info['use_case']}")
                    print(f"     Description: {info['description']}")
                    
                except Exception as e:
                    print(f"   - {filename} (error reading metadata: {e})")
            
            return workflows_info
            
        except Exception as e:
            print(f"❌ Failed to list custom workflows: {e}")
            return []
    
    def create_workflow_library(self):
        """Create a complete library of customized workflows"""
        print("📚 CREATING COMPLETE WORKFLOW LIBRARY")
        print("=" * 40)
        
        # Create style presets
        style_workflows = self.create_style_preset_workflows()
        
        # Create use case presets
        use_case_workflows = self.create_use_case_workflows()
        
        # Create combination workflows (popular combinations)
        combination_workflows = []
        popular_combinations = [
            ("anime", "game_character"),
            ("realistic", "vr_avatar"),
            ("fantasy", "game_character"),
            ("cyberpunk", "game_character"),
            ("cartoon", "social_avatar"),
            ("realistic", "film_character")
        ]
        
        print("\n🎭 Creating popular combination workflows...")
        for art_style, use_case in popular_combinations:
            custom_name = f"combo_{art_style}_{use_case}"
            custom_path = self.create_custom_workflow(
                "mickmumpitz_character_sheet.json", art_style, use_case, custom_name
            )
            
            if custom_path:
                combination_workflows.append(custom_path)
        
        # Generate library documentation
        self.generate_library_documentation()
        
        total_workflows = len(style_workflows) + len(use_case_workflows) + len(combination_workflows)
        
        print(f"\n📚 WORKFLOW LIBRARY COMPLETE!")
        print(f"   Style presets: {len(style_workflows)}")
        print(f"   Use case presets: {len(use_case_workflows)}")
        print(f"   Popular combinations: {len(combination_workflows)}")
        print(f"   Total workflows: {total_workflows}")
        
        return {
            "style_workflows": style_workflows,
            "use_case_workflows": use_case_workflows,
            "combination_workflows": combination_workflows,
            "total": total_workflows
        }
    
    def generate_library_documentation(self):
        """Generate documentation for the workflow library"""
        try:
            doc = {
                "workflow_library": {
                    "created": datetime.now().isoformat(),
                    "description": "Complete library of customized ComfyUI workflows for character generation",
                    "art_styles": self.art_styles,
                    "use_cases": self.use_cases,
                    "usage_instructions": {
                        "1": "Choose an art style based on your project needs",
                        "2": "Select a use case for optimization",
                        "3": "Use the corresponding custom workflow",
                        "4": "Adjust parameters as needed for your specific requirements"
                    },
                    "workflow_naming": {
                        "pattern": "{art_style}_{use_case}_{timestamp}",
                        "examples": [
                            "anime_game_character_20250525_120000.json",
                            "realistic_vr_avatar_20250525_120001.json",
                            "fantasy_film_character_20250525_120002.json"
                        ]
                    }
                }
            }
            
            doc_path = os.path.join(self.custom_workflows_dir, "workflow_library_documentation.json")
            with open(doc_path, 'w') as f:
                json.dump(doc, f, indent=2)
            
            print(f"📖 Library documentation saved: {doc_path}")
            
        except Exception as e:
            print(f"❌ Failed to generate documentation: {e}")
    
    def get_workflow_recommendations(self, project_type):
        """Get workflow recommendations for a project type"""
        recommendations = {
            "indie_game": {
                "primary": "stylized_game_character",
                "alternatives": ["cartoon_game_character", "anime_game_character"],
                "description": "Stylized characters work well for indie games with unique art styles"
            },
            "aaa_game": {
                "primary": "realistic_game_character", 
                "alternatives": ["fantasy_game_character", "cyberpunk_game_character"],
                "description": "High-quality realistic characters for AAA productions"
            },
            "vr_application": {
                "primary": "realistic_vr_avatar",
                "alternatives": ["cartoon_vr_avatar", "stylized_vr_avatar"],
                "description": "Optimized avatars for VR with face matching capabilities"
            },
            "social_platform": {
                "primary": "cartoon_social_avatar",
                "alternatives": ["anime_social_avatar", "stylized_social_avatar"],
                "description": "Friendly, approachable avatars for social interactions"
            },
            "film_animation": {
                "primary": "realistic_film_character",
                "alternatives": ["fantasy_film_character", "stylized_film_character"],
                "description": "High-quality characters for film and professional animation"
            }
        }
        
        return recommendations.get(project_type, {
            "primary": "realistic_game_character",
            "alternatives": ["stylized_game_character"],
            "description": "General-purpose character generation"
        })

def main():
    """Main customization function"""
    comfyui_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\ComfyUI_windows_portable_nvidia (1)"
    
    customizer = WorkflowCustomizer(comfyui_path)
    
    print("🎨 WORKFLOW CUSTOMIZATION SYSTEM")
    print("=" * 35)
    
    # Create complete workflow library
    library_result = customizer.create_workflow_library()
    
    # List all custom workflows
    print(f"\n📁 CUSTOM WORKFLOW INVENTORY:")
    workflows_info = customizer.list_custom_workflows()
    
    # Show recommendations
    print(f"\n💡 PROJECT TYPE RECOMMENDATIONS:")
    project_types = ["indie_game", "aaa_game", "vr_application", "social_platform", "film_animation"]
    
    for project_type in project_types:
        rec = customizer.get_workflow_recommendations(project_type)
        print(f"   {project_type.replace('_', ' ').title()}:")
        print(f"     Primary: {rec['primary']}")
        print(f"     Description: {rec['description']}")
    
    print(f"\n🎉 WORKFLOW CUSTOMIZATION COMPLETE!")
    print(f"✅ {library_result['total']} custom workflows ready")
    print(f"📁 Location: {customizer.custom_workflows_dir}")
    
    input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
