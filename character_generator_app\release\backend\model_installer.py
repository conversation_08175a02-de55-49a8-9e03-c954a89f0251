#!/usr/bin/env python3
"""
Automatic model installer for ComfyUI
Downloads and installs required models for character generation
"""

import os
import requests
import hashlib
from urllib.parse import urlparse
from pathlib import Path
import json

class ComfyUIModelInstaller:
    """Install required models for ComfyUI character generation"""
    
    def __init__(self, comfyui_path):
        self.comfyui_path = comfyui_path
        self.models_dir = os.path.join(comfyui_path, "models")
        
        # Required models for character generation
        self.required_models = {
            "checkpoints": {
                "sd_xl_base_1.0.safetensors": {
                    "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors",
                    "size": "6.94 GB",
                    "description": "SDXL Base model for high-quality image generation"
                },
                "sd_xl_refiner_1.0.safetensors": {
                    "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0.safetensors",
                    "size": "6.08 GB",
                    "description": "SDXL Refiner for enhanced image quality"
                }
            },
            "vae": {
                "sdxl_vae.safetensors": {
                    "url": "https://huggingface.co/stabilityai/sdxl-vae/resolve/main/sdxl_vae.safetensors",
                    "size": "335 MB",
                    "description": "SDXL VAE for image encoding/decoding"
                }
            },
            "clip": {
                "clip_l.safetensors": {
                    "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors",
                    "size": "246 MB",
                    "description": "CLIP text encoder"
                }
            },
            "upscale_models": {
                "RealESRGAN_x4plus.pth": {
                    "url": "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth",
                    "size": "64 MB",
                    "description": "Real-ESRGAN upscaler for image enhancement"
                }
            }
        }
        
        self.setup_directories()
    
    def setup_directories(self):
        """Setup required model directories"""
        directories = [
            "checkpoints",
            "vae", 
            "clip",
            "upscale_models",
            "loras",
            "controlnet"
        ]
        
        for directory in directories:
            dir_path = os.path.join(self.models_dir, directory)
            os.makedirs(dir_path, exist_ok=True)
            print(f"✅ Directory ready: {directory}")
    
    def check_installed_models(self):
        """Check which models are already installed"""
        installed = {}
        missing = {}
        
        for category, models in self.required_models.items():
            installed[category] = {}
            missing[category] = {}
            
            category_path = os.path.join(self.models_dir, category)
            
            for model_name, model_info in models.items():
                model_path = os.path.join(category_path, model_name)
                
                if os.path.exists(model_path):
                    file_size = os.path.getsize(model_path)
                    installed[category][model_name] = {
                        "path": model_path,
                        "size": f"{file_size / (1024**3):.2f} GB" if file_size > 1024**3 else f"{file_size / (1024**2):.2f} MB",
                        "status": "installed"
                    }
                    print(f"✅ Found: {model_name}")
                else:
                    missing[category][model_name] = model_info
                    print(f"❌ Missing: {model_name}")
        
        return installed, missing
    
    def download_model(self, url, destination_path, description="Model"):
        """Download a model file with progress tracking"""
        try:
            print(f"📥 Downloading {description}...")
            print(f"   URL: {url}")
            print(f"   Destination: {destination_path}")
            
            # Create destination directory if it doesn't exist
            os.makedirs(os.path.dirname(destination_path), exist_ok=True)
            
            # Start download with streaming
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(destination_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r   Progress: {progress:.1f}% ({downloaded_size / (1024**2):.1f} MB / {total_size / (1024**2):.1f} MB)", end="")
            
            print(f"\n✅ Downloaded: {description}")
            return True
            
        except Exception as e:
            print(f"\n❌ Download failed: {e}")
            # Clean up partial download
            if os.path.exists(destination_path):
                os.remove(destination_path)
            return False
    
    def install_missing_models(self, missing_models, install_large_models=False):
        """Install missing models"""
        installed_count = 0
        
        for category, models in missing_models.items():
            if not models:
                continue
                
            print(f"\n📦 Installing {category} models...")
            category_path = os.path.join(self.models_dir, category)
            
            for model_name, model_info in models.items():
                # Skip large models unless explicitly requested
                if not install_large_models and "GB" in model_info.get("size", ""):
                    print(f"⏭️ Skipping large model: {model_name} ({model_info['size']})")
                    print(f"   Use --install-large to download large models")
                    continue
                
                destination_path = os.path.join(category_path, model_name)
                
                success = self.download_model(
                    model_info["url"],
                    destination_path,
                    f"{model_name} ({model_info['size']})"
                )
                
                if success:
                    installed_count += 1
                else:
                    print(f"❌ Failed to install: {model_name}")
        
        return installed_count
    
    def create_model_info_file(self):
        """Create model information file for reference"""
        model_info = {
            "installation_date": "2025-05-25",
            "comfyui_path": self.comfyui_path,
            "required_models": self.required_models,
            "installation_notes": {
                "checkpoints": "Main models for image generation",
                "vae": "Variational autoencoders for image encoding",
                "clip": "Text encoders for prompt processing",
                "upscale_models": "Models for image upscaling",
                "usage": "These models enable full AI-powered character generation"
            }
        }
        
        info_path = os.path.join(self.models_dir, "model_info.json")
        with open(info_path, 'w') as f:
            json.dump(model_info, f, indent=2)
        
        print(f"✅ Model info saved: {info_path}")
    
    def install_essential_models(self):
        """Install essential models (smaller ones first)"""
        print("🚀 INSTALLING ESSENTIAL MODELS FOR COMFYUI")
        print("=" * 45)
        
        # Check current status
        installed, missing = self.check_installed_models()
        
        # Count missing models
        total_missing = sum(len(models) for models in missing.values())
        
        if total_missing == 0:
            print("🎉 All required models are already installed!")
            return True
        
        print(f"\n📊 Installation Summary:")
        print(f"   Missing models: {total_missing}")
        
        # Install smaller models first (VAE, CLIP, upscalers)
        print(f"\n📥 Installing essential models (VAE, CLIP, upscalers)...")
        essential_installed = self.install_missing_models(missing, install_large_models=False)
        
        # Create model info file
        self.create_model_info_file()
        
        print(f"\n✅ Essential models installation complete!")
        print(f"   Installed: {essential_installed} models")
        
        # Check for large models
        large_models = []
        for category, models in missing.items():
            for model_name, model_info in models.items():
                if "GB" in model_info.get("size", ""):
                    large_models.append(f"{model_name} ({model_info['size']})")
        
        if large_models:
            print(f"\n⚠️ Large models not installed:")
            for model in large_models:
                print(f"   - {model}")
            print(f"\n💡 To install large models:")
            print(f"   1. Ensure you have sufficient disk space (15+ GB)")
            print(f"   2. Run: python model_installer.py --install-large")
            print(f"   3. Or download manually from Hugging Face")
        
        return essential_installed > 0
    
    def install_all_models(self):
        """Install all models including large ones"""
        print("🚀 INSTALLING ALL MODELS FOR COMFYUI")
        print("=" * 40)
        
        installed, missing = self.check_installed_models()
        
        total_missing = sum(len(models) for models in missing.values())
        if total_missing == 0:
            print("🎉 All required models are already installed!")
            return True
        
        # Install all models
        installed_count = self.install_missing_models(missing, install_large_models=True)
        
        # Create model info file
        self.create_model_info_file()
        
        print(f"\n✅ Model installation complete!")
        print(f"   Installed: {installed_count} models")
        
        return installed_count > 0
    
    def verify_installation(self):
        """Verify model installation"""
        print("\n🔍 VERIFYING MODEL INSTALLATION")
        print("=" * 35)
        
        installed, missing = self.check_installed_models()
        
        # Count totals
        total_installed = sum(len(models) for models in installed.values())
        total_missing = sum(len(models) for models in missing.values())
        total_required = total_installed + total_missing
        
        print(f"\n📊 Installation Status:")
        print(f"   Total required: {total_required}")
        print(f"   Installed: {total_installed}")
        print(f"   Missing: {total_missing}")
        print(f"   Success rate: {total_installed/total_required*100:.1f}%")
        
        # Show installed models
        if total_installed > 0:
            print(f"\n✅ Installed Models:")
            for category, models in installed.items():
                if models:
                    print(f"   {category.title()}:")
                    for model_name, model_info in models.items():
                        print(f"     - {model_name} ({model_info['size']})")
        
        # Show missing models
        if total_missing > 0:
            print(f"\n❌ Missing Models:")
            for category, models in missing.items():
                if models:
                    print(f"   {category.title()}:")
                    for model_name, model_info in models.items():
                        print(f"     - {model_name} ({model_info['size']})")
        
        return total_missing == 0
    
    def get_download_instructions(self):
        """Get manual download instructions"""
        instructions = {
            "manual_download": {
                "description": "Manual download instructions for large models",
                "steps": [
                    "1. Visit Hugging Face model pages",
                    "2. Download model files",
                    "3. Place in correct ComfyUI directories",
                    "4. Verify installation"
                ],
                "models": {}
            }
        }
        
        for category, models in self.required_models.items():
            instructions["manual_download"]["models"][category] = {}
            for model_name, model_info in models.items():
                instructions["manual_download"]["models"][category][model_name] = {
                    "download_url": model_info["url"],
                    "destination": f"{self.models_dir}/{category}/{model_name}",
                    "size": model_info["size"],
                    "description": model_info["description"]
                }
        
        return instructions

def main():
    """Main installation function"""
    import sys
    
    comfyui_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\ComfyUI_windows_portable_nvidia (1)"
    
    installer = ComfyUIModelInstaller(comfyui_path)
    
    # Check command line arguments
    install_large = "--install-large" in sys.argv
    
    if install_large:
        success = installer.install_all_models()
    else:
        success = installer.install_essential_models()
    
    # Verify installation
    installer.verify_installation()
    
    if success:
        print(f"\n🎉 MODEL INSTALLATION SUCCESSFUL!")
        print(f"✅ ComfyUI is ready for AI-powered character generation")
    else:
        print(f"\n⚠️ MODEL INSTALLATION INCOMPLETE")
        print(f"Some models may need manual installation")
    
    input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
