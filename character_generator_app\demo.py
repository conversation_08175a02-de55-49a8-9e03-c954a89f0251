"""
Demo script to test the 3D Character Generator App
"""

import requests
import json
import time
import os
from PIL import Image, ImageDraw

# API base URL
BASE_URL = "http://localhost:5000"

def create_demo_image():
    """Create a simple demo image for testing."""
    # Create a simple character image
    img = Image.new('RGB', (512, 512), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # Draw a simple character
    # Head
    draw.ellipse([200, 100, 312, 212], fill='peachpuff', outline='black', width=2)
    
    # Body
    draw.rectangle([225, 212, 287, 350], fill='blue', outline='black', width=2)
    
    # Arms
    draw.rectangle([175, 230, 225, 280], fill='blue', outline='black', width=2)
    draw.rectangle([287, 230, 337, 280], fill='blue', outline='black', width=2)
    
    # Legs
    draw.rectangle([235, 350, 260, 450], fill='darkblue', outline='black', width=2)
    draw.rectangle([252, 350, 277, 450], fill='darkblue', outline='black', width=2)
    
    # Eyes
    draw.ellipse([220, 130, 235, 145], fill='black')
    draw.ellipse([277, 130, 292, 145], fill='black')
    
    # Mouth
    draw.arc([235, 160, 277, 185], start=0, end=180, fill='black', width=2)
    
    # Save the image
    demo_image_path = "demo_character.png"
    img.save(demo_image_path)
    return demo_image_path

def test_api_connection():
    """Test if the API is running."""
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ API is running!")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API. Make sure the backend is running.")
        return False
    except Exception as e:
        print(f"❌ Error connecting to API: {e}")
        return False

def upload_image(image_path):
    """Upload an image to the API."""
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{BASE_URL}/api/upload", files=files)
        
        if response.status_code == 200:
            print("✅ Image uploaded successfully!")
            data = response.json()
            print(f"Uploaded file: {data['filename']}")
            return data['processed_path']
        else:
            print(f"❌ Upload failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error uploading image: {e}")
        return None

def generate_character(text_prompt, image_path=None):
    """Generate a 3D character."""
    try:
        data = {
            "text_prompt": text_prompt,
            "image_path": image_path or "",
            "style_options": {
                "realistic": False,
                "cartoon": True,
                "anime": False,
                "lowPoly": False
            }
        }
        
        response = requests.post(f"{BASE_URL}/api/generate", json=data)
        
        if response.status_code == 200:
            print("✅ Character generation started!")
            result = response.json()
            print(f"Job ID: {result['job_id']}")
            return result['job_id']
        else:
            print(f"❌ Generation failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error generating character: {e}")
        return None

def check_job_status(job_id):
    """Check the status of a job."""
    try:
        response = requests.get(f"{BASE_URL}/api/status/{job_id}")
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Status check failed with status code: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error checking job status: {e}")
        return None

def wait_for_completion(job_id, max_wait_time=300):
    """Wait for a job to complete."""
    print(f"Waiting for job {job_id} to complete...")
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        status = check_job_status(job_id)
        if status:
            print(f"Status: {status['status']} - Progress: {status.get('progress', 0)}%")
            
            if status['status'] == 'completed':
                print("✅ Character generation completed!")
                return True
            elif status['status'] == 'error':
                print(f"❌ Character generation failed: {status.get('error', 'Unknown error')}")
                return False
        
        time.sleep(5)  # Wait 5 seconds before checking again
    
    print("❌ Timeout waiting for job completion")
    return False

def download_file(job_id, file_type):
    """Download a generated file."""
    try:
        response = requests.get(f"{BASE_URL}/api/download/{job_id}/{file_type}")
        
        if response.status_code == 200:
            filename = f"downloaded_{job_id[:8]}_{file_type}"
            if file_type == 'model':
                filename += '.glb'
            elif file_type == 'texture':
                filename += '.png'
            elif file_type == 'animation':
                filename += '.fbx'
            
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Downloaded {file_type}: {filename}")
            return filename
        else:
            print(f"❌ Download failed with status code: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error downloading {file_type}: {e}")
        return None

def list_jobs():
    """List all jobs."""
    try:
        response = requests.get(f"{BASE_URL}/api/jobs")
        
        if response.status_code == 200:
            data = response.json()
            jobs = data.get('jobs', [])
            print(f"Found {len(jobs)} jobs:")
            for job in jobs:
                print(f"  - {job.get('id', 'unknown')[:8]}: {job.get('status', 'unknown')} ({job.get('progress', 0)}%)")
            return jobs
        else:
            print(f"❌ Failed to list jobs with status code: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error listing jobs: {e}")
        return []

def main():
    """Main demo function."""
    print("🎮 3D Character Generator Demo")
    print("=" * 40)
    
    # Test API connection
    if not test_api_connection():
        print("\n❌ Cannot proceed without API connection.")
        print("Make sure to run: python character_generator_app/backend/app.py")
        return
    
    print("\n📋 Listing existing jobs...")
    list_jobs()
    
    print("\n🎨 Creating demo image...")
    demo_image_path = create_demo_image()
    print(f"Created demo image: {demo_image_path}")
    
    print("\n📤 Uploading demo image...")
    uploaded_path = upload_image(demo_image_path)
    
    if not uploaded_path:
        print("Proceeding without image...")
        uploaded_path = None
    
    print("\n🚀 Generating character...")
    text_prompt = "A brave cartoon knight with blue armor and a friendly smile"
    job_id = generate_character(text_prompt, uploaded_path)
    
    if not job_id:
        print("❌ Failed to start character generation")
        return
    
    print("\n⏳ Waiting for completion...")
    if wait_for_completion(job_id):
        print("\n📥 Downloading generated files...")
        
        # Download all file types
        model_file = download_file(job_id, 'model')
        texture_file = download_file(job_id, 'texture')
        animation_file = download_file(job_id, 'animation')
        
        print("\n✅ Demo completed successfully!")
        print(f"Generated files:")
        if model_file:
            print(f"  - 3D Model: {model_file}")
        if texture_file:
            print(f"  - Texture: {texture_file}")
        if animation_file:
            print(f"  - Animation: {animation_file}")
        
        print(f"\n🌐 You can also view the character in the web app:")
        print(f"http://localhost:3000/viewer/{job_id}")
    
    # Cleanup
    if os.path.exists(demo_image_path):
        os.remove(demo_image_path)
        print(f"\nCleaned up demo image: {demo_image_path}")

if __name__ == "__main__":
    main()
