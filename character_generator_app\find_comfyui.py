#!/usr/bin/env python3
"""
Find ComfyUI installation and directory structure
"""

import os

def find_comfyui_structure():
    """Find the actual ComfyUI directory structure."""

    base_path = r"C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)"

    print("🔍 Searching for ComfyUI structure...")
    print(f"Base path: {base_path}")

    if not os.path.exists(base_path):
        print("❌ Base ComfyUI directory not found")
        return

    print("✅ Base directory found")

    # List contents of base directory
    print(f"\n📁 Contents of {base_path}:")
    try:
        items = os.listdir(base_path)
        for item in sorted(items):
            item_path = os.path.join(base_path, item)
            if os.path.isdir(item_path):
                print(f"   📁 {item}/")
            else:
                print(f"   📄 {item}")
    except Exception as e:
        print(f"❌ Error listing directory: {e}")
        return

    # Look for ComfyUI subdirectory
    possible_paths = [
        os.path.join(base_path, "ComfyUI_windows_portable", "ComfyUI"),
        os.path.join(base_path, "ComfyUI_windows_portable"),
        os.path.join(base_path, "ComfyUI"),
        os.path.join(base_path, "comfyui"),
        base_path  # Sometimes it's directly in the base
    ]

    comfyui_main = None

    for path in possible_paths:
        if os.path.exists(path):
            # Check if this looks like the main ComfyUI directory
            main_py = os.path.join(path, "main.py")
            if os.path.exists(main_py):
                comfyui_main = path
                print(f"\n✅ Found ComfyUI main directory: {path}")
                break

    if not comfyui_main:
        print("\n❌ Could not find ComfyUI main directory (looking for main.py)")
        return

    # Look for custom_nodes directory
    custom_nodes_path = os.path.join(comfyui_main, "custom_nodes")

    if os.path.exists(custom_nodes_path):
        print(f"✅ Found custom_nodes directory: {custom_nodes_path}")

        # List existing custom nodes
        print(f"\n📦 Existing custom nodes:")
        try:
            nodes = os.listdir(custom_nodes_path)
            if nodes:
                for node in sorted(nodes):
                    node_path = os.path.join(custom_nodes_path, node)
                    if os.path.isdir(node_path):
                        print(f"   📁 {node}")
            else:
                print("   (No custom nodes installed)")
        except Exception as e:
            print(f"❌ Error listing custom nodes: {e}")
    else:
        print(f"❌ custom_nodes directory not found at: {custom_nodes_path}")

        # Try to create it
        try:
            os.makedirs(custom_nodes_path, exist_ok=True)
            print(f"✅ Created custom_nodes directory: {custom_nodes_path}")
        except Exception as e:
            print(f"❌ Failed to create custom_nodes directory: {e}")

    return comfyui_main, custom_nodes_path

if __name__ == "__main__":
    find_comfyui_structure()
