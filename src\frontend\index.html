<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Content Generation Pipeline</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .header {
            padding-bottom: 1rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e5e5e5;
        }
        .preview-container {
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
        }
        .preview-container img {
            max-width: 100%;
            max-height: 100%;
        }
        .preview-container .placeholder {
            color: #6c757d;
        }
        .progress-container {
            margin-top: 1rem;
            display: none;
        }
        #dropzone {
            border: 2px dashed #0087F7;
            border-radius: 5px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            margin-bottom: 1rem;
        }
        #dropzone.highlight {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI Content Generation Pipeline</h1>
            <p class="lead">Create 3D models, videos, images, characters, and video games from various inputs</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4>Input</h4>
                    </div>
                    <div class="card-body">
                        <div id="dropzone">
                            <p>Drag & drop files here or click to upload</p>
                            <p class="small text-muted">Supported formats: PNG, JPG, MP4, GLB, GLTF, OBJ</p>
                            <input type="file" id="fileInput" style="display: none;" accept=".png,.jpg,.jpeg,.mp4,.glb,.gltf,.obj">
                        </div>
                        
                        <div class="mb-3">
                            <label for="textPrompt" class="form-label">Text Prompt</label>
                            <textarea class="form-control" id="textPrompt" rows="3" placeholder="Enter a text prompt to generate content..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="inputType" class="form-label">Input Type</label>
                            <select class="form-select" id="inputType">
                                <option value="image">Image</option>
                                <option value="text">Text</option>
                                <option value="3d">3D Model</option>
                                <option value="video">Video</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="outputType" class="form-label">Output Type</label>
                            <select class="form-select" id="outputType">
                                <option value="image">Image</option>
                                <option value="3d">3D Model</option>
                                <option value="video">Video</option>
                                <option value="game">Video Game</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="advancedOptions" class="form-label">Advanced Options</label>
                            <div class="accordion" id="advancedOptions">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#advancedOptionsContent">
                                            Advanced Options
                                        </button>
                                    </h2>
                                    <div id="advancedOptionsContent" class="accordion-collapse collapse">
                                        <div class="accordion-body">
                                            <div class="mb-3">
                                                <label for="model" class="form-label">Model</label>
                                                <select class="form-select" id="model">
                                                    <option value="default">Default</option>
                                                    <option value="shap-e">Shap-E</option>
                                                    <option value="zero123">Zero123</option>
                                                    <option value="syncdreamer">SyncDreamer</option>
                                                </select>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="quality" class="form-label">Quality</label>
                                                <select class="form-select" id="quality">
                                                    <option value="low">Low</option>
                                                    <option value="medium" selected>Medium</option>
                                                    <option value="high">High</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="optimize" checked>
                                                <label class="form-check-label" for="optimize">
                                                    Optimize Output
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <button id="generateBtn" class="btn btn-primary">Generate</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4>Output</h4>
                    </div>
                    <div class="card-body">
                        <div class="preview-container">
                            <div class="placeholder">Output preview will appear here</div>
                        </div>
                        
                        <div class="progress-container">
                            <label id="statusLabel" class="form-label">Processing...</label>
                            <div class="progress">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div class="btn-group mt-3" role="group">
                            <button id="downloadBtn" class="btn btn-success" disabled>Download</button>
                            <button id="viewBtn" class="btn btn-secondary" disabled>View in 3D</button>
                            <button id="shareBtn" class="btn btn-info" disabled>Share</button>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h4>Job History</h4>
                    </div>
                    <div class="card-body">
                        <ul id="jobHistory" class="list-group">
                            <!-- Job history items will be added here -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
