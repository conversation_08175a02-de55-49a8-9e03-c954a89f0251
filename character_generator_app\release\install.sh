#!/bin/bash

echo "========================================"
echo "AI-Powered 3D Character Generator"
echo "Automated Installation Script (Linux/Mac)"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}ERROR: Python 3 is not installed${NC}"
    echo "Please install Python 3.11 or 3.12"
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
echo -e "${GREEN}Found Python $PYTHON_VERSION${NC}"

# Check if version is 3.11 or 3.12
MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

if [ "$MAJOR" -ne 3 ] || [ "$MINOR" -lt 11 ] || [ "$MINOR" -gt 12 ]; then
    echo -e "${YELLOW}WARNING: Python 3.11 or 3.12 recommended, found $PYTHON_VERSION${NC}"
fi

# Create virtual environment
echo
echo "Creating virtual environment..."
python3 -m venv venv
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Failed to create virtual environment${NC}"
    exit 1
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo
echo "Upgrading pip..."
python -m pip install --upgrade pip

# Install requirements
echo
echo "Installing Python dependencies..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Failed to install Python dependencies${NC}"
    exit 1
fi

# Download models
echo
echo "Downloading AI models (this may take a while)..."
python download_models.py
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}WARNING: Some models may not have downloaded completely${NC}"
    echo "You can run 'python download_models.py' later to retry"
fi

# Setup configuration
echo
echo "Setting up configuration..."
python setup_config.py

# Create directories
echo
echo "Creating necessary directories..."
mkdir -p outputs uploads models workflows/custom logs

# Set permissions
echo "Setting up permissions..."
chmod 755 outputs uploads models workflows logs
chmod +x start.sh

echo
echo -e "${GREEN}========================================"
echo "Installation completed successfully!"
echo "========================================${NC}"
echo
echo "To start the application:"
echo "  1. Run: ./start.sh"
echo "  2. Open browser to: http://localhost:8080"
echo
echo "For manual start:"
echo "  1. Run: source venv/bin/activate"
echo "  2. Run: python comprehensive_backend.py"
echo
echo "Need help? Check README.md or visit:"
echo "https://github.com/yourusername/ai-3d-character-generator"
echo
