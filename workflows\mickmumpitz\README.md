# mickmumpitz ComfyUI Workflows

This directory contains information about mickmumpitz's ComfyUI workflows that are used in this project. These workflows are powerful tools for 3D rendering, character generation, and more.

## Workflow Files

You need to download the actual workflow files from mickmumpitz's Patreon:

1. **3D Rendering Workflows**: [Free Workflows & Guide: Render your 3D animations with AI!](https://www.patreon.com/posts/free-workflows-104795004)
   - `200524_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v3.json` (for images)
   - `200524_Mickmumpitz_3D-RENDERING_VIDEO_SD15LCM_SIMPLE_v3.json` (for videos)

2. **Character Generation Workflows**: [FREE WORKFLOWS: Create your own CONSISTENT characters with AI!](https://www.patreon.com/posts/free-workflows-113743435)
   - `241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json` (for character sheets)
   - `241007_MICKMUMPITZ_FLUX+LORA.json` (for training custom LoRAs)

## Installation Guides

mickmumpitz provides detailed installation guides for these workflows:

1. **3D Rendering Guide**: [Installation Guide for 3D Rendering](https://docs.google.com/document/d/1lemaWcaSAxn1YsRSkRcCXAT9Nho-tIObcoijPrlDfSs/edit?usp=sharing)
2. **Character Generation Guide**: [Installation Guide for Character Generation](https://docs.google.com/document/d/1PHYMpXqfNKj9dQIMVpXAg7R4FjNFIINok09L9heQTnM/edit?usp=sharing)

## Automated Setup

You can use our automated setup script to help download and set up these workflows:

```bash
python src/scripts/download_mickmumpitz_workflows.py
```

This script will:
1. Create the necessary directories
2. Open mickmumpitz's Patreon pages for you to download the workflow files
3. Open the installation guides
4. Check if the workflow files are present
5. Create a workflow info file

## Required ComfyUI Custom Nodes

These workflows require several custom nodes to be installed in ComfyUI. The main ones include:

- ComfyUI-ControlNet-Aux
- ComfyUI-AnimateDiff-Evolved
- ComfyUI-VideoHelperSuite
- ComfyUI-IPAdapter-Plus
- ComfyUI-UltimateSDUpscale
- ComfyUI-FluxNodes
- And more...

You can use the `setup_required_nodes()` method in the `MickmumpitzWorkflows` class to automatically install these nodes:

```python
from src.integrations.comfyui.mickmumpitz_workflows import MickmumpitzWorkflows

mickmumpitz = MickmumpitzWorkflows()
mickmumpitz.setup_required_nodes()
```

## Demo

To see a demonstration of how to use these workflows in our project, run:

```bash
python src/examples/mickmumpitz_workflow_demo.py --setup
python src/examples/mickmumpitz_workflow_demo.py --render
python src/examples/mickmumpitz_workflow_demo.py --character
python src/examples/mickmumpitz_workflow_demo.py --pipeline
```

## Credits

All workflows are created by mickmumpitz. Please support their work on [Patreon](https://www.patreon.com/Mickmumpitz) and check out their [YouTube channel](https://www.youtube.com/@mickmumpitz) for tutorials and examples.
