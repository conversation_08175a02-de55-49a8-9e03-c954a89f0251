@echo off
echo ========================================
echo  Comprehensive 3D Character Generator
echo ========================================
echo.

:: Set up environment
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

echo [1/5] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.8 or higher.
    pause
    exit /b 1
)
echo ✓ Python found

echo.
echo [2/5] Installing/updating Python dependencies...
pip install fastapi uvicorn aiohttp python-multipart >nul 2>&1
if errorlevel 1 (
    echo WARNING: Some dependencies may not have installed correctly
) else (
    echo ✓ Dependencies installed
)

echo.
echo [3/5] Starting ComfyUI (if available)...
set COMFYUI_PATH=C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)
if exist "%COMFYUI_PATH%" (
    echo ✓ ComfyUI found at %COMFYUI_PATH%
    start "ComfyUI" cmd /c "cd /d "%COMFYUI_PATH%" && python_embeded\python.exe main.py --listen"
    echo ✓ ComfyUI starting in background...
    timeout /t 5 /nobreak >nul
) else (
    echo ⚠ ComfyUI not found at expected location
    echo   System will use fallback generation methods
)

echo.
echo [4/5] Starting AgenticSeek (if available)...
set AGENTIC_PATH=%SCRIPT_DIR%..\agenticSeek
if exist "%AGENTIC_PATH%" (
    echo ✓ AgenticSeek found at %AGENTIC_PATH%
    if exist "%AGENTIC_PATH%\agentic_seek_env\Scripts\python.exe" (
        start "AgenticSeek" cmd /c "cd /d "%AGENTIC_PATH%" && agentic_seek_env\Scripts\python.exe api.py"
        echo ✓ AgenticSeek starting in background...
        timeout /t 3 /nobreak >nul
    ) else (
        echo ⚠ AgenticSeek virtual environment not found
        echo   Installing AgenticSeek...
        cd /d "%AGENTIC_PATH%"
        call install.bat
        cd /d "%SCRIPT_DIR%"
    )
) else (
    echo ⚠ AgenticSeek not found
    echo   System will use fallback AI responses
)

echo.
echo [5/5] Starting Comprehensive Backend Server...
echo ✓ Backend server starting on http://127.0.0.1:8080
echo ✓ Web interface will be available shortly...
echo.
echo ========================================
echo  All Services Status:
echo ========================================
echo  • Backend Server: Starting...
echo  • ComfyUI: %COMFYUI_STATUS%
echo  • AgenticSeek: %AGENTIC_STATUS%
echo  • Web Interface: http://127.0.0.1:8080/static/comprehensive_character_generator.html
echo ========================================
echo.
echo Press Ctrl+C to stop all services
echo.

:: Start the main backend server
python comprehensive_backend.py

pause
