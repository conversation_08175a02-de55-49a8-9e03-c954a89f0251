{"character_name": "PipelineTestCharacter", "export_path": "unity_exports\\PipelineTestCharacter", "glb_file": "unity_exports\\PipelineTestCharacter\\PipelineTestCharacter.glb", "materials": {"skin_material": {"shader": "Standard", "properties": {"_Color": [0.7843137254901961, 0.7058823529411765, 0.6274509803921569, 1.0], "_Metallic": 0.0, "_Smoothness": 0.4, "_BumpScale": 1.0}, "textures": {"_MainTex": "PipelineTestCharacter_diffuse.png", "_BumpMap": "PipelineTestCharacter_normal.png", "_MetallicGlossMap": "PipelineTestCharacter_metallic.png"}}, "clothing_material": {"shader": "Standard", "properties": {"_Color": [0.2, 0.4, 0.8, 1.0], "_Metallic": 0.1, "_Smoothness": 0.7, "_BumpScale": 1.0}, "textures": {"_MainTex": "PipelineTestCharacter_clothing_diffuse.png", "_BumpMap": "PipelineTestCharacter_clothing_normal.png"}}}, "prefab_config": {"prefab_name": "PipelineTestCharacter_Prefab", "components": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "materials": ["skin_material", "clothing_material"]}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesh": "PipelineTestCharacter.glb"}, {"type": "Animator", "controller": "PipelineTestCharacter_AnimatorController"}, {"type": "CapsuleCollider", "height": 2.0, "radius": 0.5}], "tags": ["Character", "Player"], "layer": "<PERSON><PERSON><PERSON>"}, "import_settings": {"model_import_settings": {"scale_factor": 1.0, "mesh_compression": "Medium", "read_write_enabled": false, "optimize_mesh": true, "generate_colliders": false, "keep_quads": false, "weld_vertices": true, "import_visibility": true, "import_cameras": false, "import_lights": false}, "animation_import_settings": {"import_animation": true, "rig_type": "Humanoid", "avatar_definition": "Create From This Model", "optimize_game_objects": true, "motion_node_name": ""}, "material_import_settings": {"material_naming": "By Base Texture Name", "material_search": "Recursive Up", "material_location": "Use External Materials (Legacy)"}}, "documentation": {"setup_instructions": ["1. Import PipelineTestCharacter.glb into your Unity project", "2. Apply the provided import settings", "3. Create materials using the material definitions", "4. Set up the prefab using the prefab configuration", "5. Configure the Animator Controller for animations"], "optimization_notes": ["Character optimized for Unity with 2000 target vertices", "LOD levels generated for performance scaling", "Mobile-optimized materials included", "Standard shader compatibility ensured"], "usage_tips": ["Use the provided prefab for consistent setup", "Adjust LOD distances based on your game's requirements", "Consider using GPU Instancing for multiple characters", "Test performance on target platforms"]}, "optimization_applied": true, "export_date": "2025-05-25T18:20:36.884391"}