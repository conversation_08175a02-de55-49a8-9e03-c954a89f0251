#!/usr/bin/env python3
"""
ComfyUI integration for enhanced character generation using mickmumpitz workflows
"""

import os
import json
import requests
import time
import base64
from PIL import Image
import io

class ComfyUIIntegration:
    """Integration with ComfyUI for advanced character generation"""

    def __init__(self, comfyui_path=None):
        self.comfyui_path = comfyui_path or "C:\\Users\\<USER>\\OneDrive\\Desktop\\ComfyUI_windows_portable_nvidia (1)"
        self.server_url = "http://127.0.0.1:8188"
        self.client_id = "character_generator"

        # Enhanced workflow paths for mickmumpitz integration
        self.workflows = {
            'character_sheet': 'workflows/mickmumpitz_character_sheet.json',
            '3d_rendering': 'workflows/mickmumpitz_3d_rendering.json',
            'flux_lora': 'workflows/mickmumpitz_flux_lora.json',
            'face_enhancement': 'workflows/mickmumpitz_face_enhancement.json',
            'style_transfer': 'workflows/mickmumpitz_style_transfer.json',
            'upscale_enhance': 'workflows/mickmumpitz_upscale.json',
            'character_variations': 'workflows/mickmumpitz_variations.json'
        }

        # Initialize ComfyUI connection
        self.check_comfyui_availability()
        self.setup_mickmumpitz_workflows()

    def check_comfyui_availability(self):
        """Check if ComfyUI is available and running"""
        try:
            response = requests.get(f"{self.server_url}/system_stats", timeout=5)
            if response.status_code == 200:
                print("✅ ComfyUI server is running")
                self.available = True
            else:
                print("⚠️ ComfyUI server not responding")
                self.available = False
        except requests.exceptions.RequestException:
            print("⚠️ ComfyUI server not available")
            self.available = False

    def setup_mickmumpitz_workflows(self):
        """Setup and verify mickmumpitz workflows"""
        try:
            print("🎨 Setting up mickmumpitz workflows...")

            # Create workflows directory if it doesn't exist
            workflows_dir = os.path.join(self.comfyui_path, "workflows")
            os.makedirs(workflows_dir, exist_ok=True)

            # Check for existing workflows
            existing_workflows = []
            missing_workflows = []

            for workflow_name, workflow_file in self.workflows.items():
                workflow_path = os.path.join(workflows_dir, workflow_file)
                if os.path.exists(workflow_path):
                    existing_workflows.append(workflow_name)
                else:
                    missing_workflows.append(workflow_name)

            print(f"✅ Found {len(existing_workflows)} existing workflows")
            if missing_workflows:
                print(f"⚠️ Missing {len(missing_workflows)} workflows, creating defaults...")
                self.create_enhanced_mickmumpitz_workflows()

            return True

        except Exception as e:
            print(f"❌ Workflow setup error: {e}")
            return False

    def generate_character_sheet(self, face_image_path, character_description):
        """Generate character sheet using mickmumpitz workflow"""
        try:
            if not self.available:
                print("❌ ComfyUI not available, using fallback")
                return self.generate_fallback_character_sheet(face_image_path, character_description)

            print("🎨 Generating character sheet with ComfyUI...")

            # Load character sheet workflow
            workflow = self.load_workflow('character_sheet')
            if not workflow:
                return self.generate_fallback_character_sheet(face_image_path, character_description)

            # Prepare inputs
            workflow_inputs = self.prepare_character_sheet_inputs(
                workflow, face_image_path, character_description
            )

            # Execute workflow
            result = self.execute_workflow(workflow_inputs)

            if result and 'images' in result:
                print("✅ Character sheet generated successfully")
                return {
                    'success': True,
                    'images': result['images'],
                    'method': 'comfyui_character_sheet',
                    'workflow': 'mickmumpitz_character_sheet'
                }
            else:
                print("⚠️ Character sheet generation failed")
                return self.generate_fallback_character_sheet(face_image_path, character_description)

        except Exception as e:
            print(f"❌ Character sheet generation error: {e}")
            return self.generate_fallback_character_sheet(face_image_path, character_description)

    def generate_3d_rendering_image(self, character_description, style="realistic"):
        """Generate 3D rendering image for character reference"""
        try:
            if not self.available:
                print("❌ ComfyUI not available for 3D rendering")
                return self.generate_fallback_3d_image(character_description)

            print("🎮 Generating 3D rendering image...")

            # Load 3D rendering workflow
            workflow = self.load_workflow('3d_rendering')
            if not workflow:
                return self.generate_fallback_3d_image(character_description)

            # Prepare inputs
            workflow_inputs = self.prepare_3d_rendering_inputs(
                workflow, character_description, style
            )

            # Execute workflow
            result = self.execute_workflow(workflow_inputs)

            if result and 'images' in result:
                print("✅ 3D rendering image generated")
                return {
                    'success': True,
                    'images': result['images'],
                    'method': 'comfyui_3d_rendering',
                    'style': style
                }
            else:
                return self.generate_fallback_3d_image(character_description)

        except Exception as e:
            print(f"❌ 3D rendering generation error: {e}")
            return self.generate_fallback_3d_image(character_description)

    def enhance_face_image(self, face_image_path):
        """Enhance face image quality for better 3D generation"""
        try:
            if not self.available:
                print("❌ ComfyUI not available for face enhancement")
                return {'success': False, 'enhanced_image': face_image_path}

            print("✨ Enhancing face image quality...")

            # Load face enhancement workflow
            workflow = self.load_workflow('face_enhancement')
            if not workflow:
                return {'success': False, 'enhanced_image': face_image_path}

            # Prepare inputs
            workflow_inputs = self.prepare_face_enhancement_inputs(workflow, face_image_path)

            # Execute workflow
            result = self.execute_workflow(workflow_inputs)

            if result and 'images' in result:
                print("✅ Face image enhanced")
                return {
                    'success': True,
                    'enhanced_image': result['images'][0],
                    'method': 'comfyui_enhancement'
                }
            else:
                return {'success': False, 'enhanced_image': face_image_path}

        except Exception as e:
            print(f"❌ Face enhancement error: {e}")
            return {'success': False, 'enhanced_image': face_image_path}

    def load_workflow(self, workflow_name):
        """Load ComfyUI workflow from JSON file"""
        try:
            workflow_path = os.path.join(self.comfyui_path, self.workflows.get(workflow_name, ''))

            # Check if workflow file exists
            if not os.path.exists(workflow_path):
                print(f"⚠️ Workflow file not found: {workflow_path}")
                # Try to create a basic workflow
                return self.create_basic_workflow(workflow_name)

            with open(workflow_path, 'r') as f:
                workflow = json.load(f)

            print(f"✅ Loaded workflow: {workflow_name}")
            return workflow

        except Exception as e:
            print(f"❌ Error loading workflow {workflow_name}: {e}")
            return self.create_basic_workflow(workflow_name)

    def create_basic_workflow(self, workflow_name):
        """Create a basic workflow if mickmumpitz workflows are not available"""
        print(f"🔧 Creating basic {workflow_name} workflow...")

        if workflow_name == 'character_sheet':
            return {
                "1": {
                    "inputs": {
                        "text": "character sheet, multiple views, front view, side view, back view",
                        "clip": ["4", 1]
                    },
                    "class_type": "CLIPTextEncode"
                },
                "2": {
                    "inputs": {
                        "samples": ["3", 0],
                        "vae": ["4", 2]
                    },
                    "class_type": "VAEDecode"
                },
                "3": {
                    "inputs": {
                        "seed": 42,
                        "steps": 20,
                        "cfg": 7.0,
                        "sampler_name": "euler",
                        "scheduler": "normal",
                        "denoise": 1.0,
                        "model": ["4", 0],
                        "positive": ["1", 0],
                        "negative": ["5", 0],
                        "latent_image": ["6", 0]
                    },
                    "class_type": "KSampler"
                },
                "4": {
                    "inputs": {
                        "ckpt_name": "sd_xl_base_1.0.safetensors"
                    },
                    "class_type": "CheckpointLoaderSimple"
                },
                "5": {
                    "inputs": {
                        "text": "blurry, low quality, distorted",
                        "clip": ["4", 1]
                    },
                    "class_type": "CLIPTextEncode"
                },
                "6": {
                    "inputs": {
                        "width": 1024,
                        "height": 1024,
                        "batch_size": 1
                    },
                    "class_type": "EmptyLatentImage"
                }
            }

        elif workflow_name == '3d_rendering':
            return {
                "1": {
                    "inputs": {
                        "text": "3D rendered character, high quality, detailed",
                        "clip": ["4", 1]
                    },
                    "class_type": "CLIPTextEncode"
                },
                "2": {
                    "inputs": {
                        "samples": ["3", 0],
                        "vae": ["4", 2]
                    },
                    "class_type": "VAEDecode"
                },
                "3": {
                    "inputs": {
                        "seed": 42,
                        "steps": 25,
                        "cfg": 8.0,
                        "sampler_name": "dpmpp_2m",
                        "scheduler": "karras",
                        "denoise": 1.0,
                        "model": ["4", 0],
                        "positive": ["1", 0],
                        "negative": ["5", 0],
                        "latent_image": ["6", 0]
                    },
                    "class_type": "KSampler"
                },
                "4": {
                    "inputs": {
                        "ckpt_name": "sd_xl_base_1.0.safetensors"
                    },
                    "class_type": "CheckpointLoaderSimple"
                },
                "5": {
                    "inputs": {
                        "text": "blurry, low quality, 2D, flat",
                        "clip": ["4", 1]
                    },
                    "class_type": "CLIPTextEncode"
                },
                "6": {
                    "inputs": {
                        "width": 768,
                        "height": 768,
                        "batch_size": 1
                    },
                    "class_type": "EmptyLatentImage"
                }
            }

        return None

    def prepare_character_sheet_inputs(self, workflow, face_image_path, character_description):
        """Prepare inputs for character sheet workflow"""
        try:
            # Encode face image
            face_image_data = self.encode_image(face_image_path)

            # Update workflow with inputs
            if "1" in workflow and "inputs" in workflow["1"]:
                # Update text prompt
                prompt = f"character sheet, multiple views, {character_description}, front view, side view, back view, reference sheet"
                workflow["1"]["inputs"]["text"] = prompt

            # Add face image input if supported
            if face_image_data:
                workflow["face_input"] = {
                    "inputs": {
                        "image": face_image_data
                    },
                    "class_type": "LoadImage"
                }

            return workflow

        except Exception as e:
            print(f"Error preparing character sheet inputs: {e}")
            return workflow

    def prepare_3d_rendering_inputs(self, workflow, character_description, style):
        """Prepare inputs for 3D rendering workflow"""
        try:
            if "1" in workflow and "inputs" in workflow["1"]:
                prompt = f"3D rendered {character_description}, {style} style, high quality, detailed modeling, professional lighting"
                workflow["1"]["inputs"]["text"] = prompt

            if "5" in workflow and "inputs" in workflow["5"]:
                negative_prompt = "2D, flat, cartoon, low quality, blurry, distorted, amateur"
                workflow["5"]["inputs"]["text"] = negative_prompt

            return workflow

        except Exception as e:
            print(f"Error preparing 3D rendering inputs: {e}")
            return workflow

    def prepare_face_enhancement_inputs(self, workflow, face_image_path):
        """Prepare inputs for face enhancement workflow"""
        try:
            face_image_data = self.encode_image(face_image_path)

            if face_image_data:
                workflow["image_input"] = {
                    "inputs": {
                        "image": face_image_data
                    },
                    "class_type": "LoadImage"
                }

            return workflow

        except Exception as e:
            print(f"Error preparing face enhancement inputs: {e}")
            return workflow

    def encode_image(self, image_path):
        """Encode image to base64 for ComfyUI"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()

            encoded = base64.b64encode(image_data).decode('utf-8')
            return encoded

        except Exception as e:
            print(f"Error encoding image: {e}")
            return None

    def execute_workflow(self, workflow):
        """Execute workflow on ComfyUI server"""
        try:
            # Queue prompt
            prompt_data = {
                "prompt": workflow,
                "client_id": self.client_id
            }

            response = requests.post(
                f"{self.server_url}/prompt",
                json=prompt_data,
                timeout=30
            )

            if response.status_code != 200:
                print(f"❌ Failed to queue workflow: {response.status_code}")
                return None

            prompt_id = response.json()["prompt_id"]
            print(f"🔄 Workflow queued with ID: {prompt_id}")

            # Wait for completion
            return self.wait_for_completion(prompt_id)

        except Exception as e:
            print(f"❌ Workflow execution error: {e}")
            return None

    def wait_for_completion(self, prompt_id, timeout=300):
        """Wait for workflow completion"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # Check queue status
                response = requests.get(f"{self.server_url}/queue", timeout=10)
                if response.status_code == 200:
                    queue_data = response.json()

                    # Check if our prompt is still in queue
                    running = any(item[1]["prompt_id"] == prompt_id for item in queue_data.get("queue_running", []))
                    pending = any(item[1]["prompt_id"] == prompt_id for item in queue_data.get("queue_pending", []))

                    if not running and not pending:
                        # Workflow completed, get results
                        return self.get_workflow_results(prompt_id)

                time.sleep(2)

            except Exception as e:
                print(f"Error checking workflow status: {e}")
                time.sleep(5)

        print("⏰ Workflow timeout")
        return None

    def get_workflow_results(self, prompt_id):
        """Get results from completed workflow"""
        try:
            response = requests.get(f"{self.server_url}/history/{prompt_id}", timeout=10)

            if response.status_code == 200:
                history = response.json()

                if prompt_id in history:
                    outputs = history[prompt_id].get("outputs", {})
                    images = []

                    # Extract images from outputs
                    for node_id, output in outputs.items():
                        if "images" in output:
                            for image_info in output["images"]:
                                image_url = f"{self.server_url}/view?filename={image_info['filename']}&subfolder={image_info.get('subfolder', '')}&type={image_info.get('type', 'output')}"
                                images.append(image_url)

                    return {"images": images}

            return None

        except Exception as e:
            print(f"Error getting workflow results: {e}")
            return None

    def generate_fallback_character_sheet(self, face_image_path, character_description):
        """Generate fallback character sheet when ComfyUI is not available"""
        print("🔧 Generating fallback character sheet...")

        return {
            'success': True,
            'images': [face_image_path],  # Use original image as fallback
            'method': 'fallback_character_sheet',
            'note': 'ComfyUI not available, using original image'
        }

    def generate_fallback_3d_image(self, character_description):
        """Generate fallback 3D image when ComfyUI is not available"""
        print("🔧 Generating fallback 3D reference...")

        return {
            'success': False,
            'images': [],
            'method': 'fallback_3d_rendering',
            'note': 'ComfyUI not available for 3D rendering'
        }

    def create_enhanced_mickmumpitz_workflows(self):
        """Create enhanced mickmumpitz-style workflows"""
        print("🎨 Creating enhanced mickmumpitz workflows...")

        workflows_dir = os.path.join(self.comfyui_path, "workflows")
        os.makedirs(workflows_dir, exist_ok=True)

        # Create all enhanced workflows
        workflows_created = 0

        # 1. Enhanced Character Sheet Workflow
        workflows_created += self.create_character_sheet_workflow(workflows_dir)

        # 2. 3D Rendering Workflow
        workflows_created += self.create_3d_rendering_workflow(workflows_dir)

        # 3. Face Enhancement Workflow
        workflows_created += self.create_face_enhancement_workflow(workflows_dir)

        # 4. Style Transfer Workflow
        workflows_created += self.create_style_transfer_workflow(workflows_dir)

        # 5. Upscale Enhancement Workflow
        workflows_created += self.create_upscale_workflow(workflows_dir)

        # 6. Character Variations Workflow
        workflows_created += self.create_variations_workflow(workflows_dir)

        print(f"✅ Created {workflows_created} enhanced workflows")
        return workflows_created > 0

    def create_enhanced_mickmumpitz_workflows(self):
        """Create enhanced mickmumpitz workflows using the workflow creator"""
        try:
            from enhanced_comfyui_workflows import EnhancedWorkflowCreator

            workflow_creator = EnhancedWorkflowCreator(self.comfyui_path)
            workflows_created = workflow_creator.create_all_workflows()

            return workflows_created > 0

        except Exception as e:
            print(f"❌ Enhanced workflow creation failed: {e}")
            return False
