"""
Pipeline manager for coordinating content generation tasks.
"""

import uuid
import threading
import time
import json
import os
from enum import Enum

# Import pipeline modules
from pipelines.image_to_3d import ImageTo3DPipeline
from pipelines.text_to_3d import TextTo3DPipeline
from pipelines.text_to_image import TextToImagePipeline
from pipelines.image_to_video import ImageToVideoPipeline
from pipelines.text_to_game import TextToGamePipeline

class JobStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class PipelineManager:
    """
    Manages content generation pipelines and job processing.
    """
    
    def __init__(self):
        """Initialize the pipeline manager."""
        self.jobs = {}
        self.pipelines = {
            ('image', '3d'): ImageTo3DPipeline(),
            ('text', '3d'): TextTo3DPipeline(),
            ('text', 'image'): TextToImagePipeline(),
            ('image', 'video'): ImageToVideoPipeline(),
            ('text', 'game'): TextToGamePipeline(),
            # Add more pipeline combinations as needed
        }
    
    def create_job(self, input_type, output_type, input_data, parameters=None):
        """
        Create a new content generation job.
        
        Args:
            input_type: Type of input (image, text, 3d, video)
            output_type: Type of output (image, 3d, video, game)
            input_data: Input data (base64 encoded or text)
            parameters: Additional parameters for the generation task
            
        Returns:
            job_id: Unique identifier for the job
        """
        job_id = str(uuid.uuid4())
        
        # Create job record
        self.jobs[job_id] = {
            'id': job_id,
            'input_type': input_type,
            'output_type': output_type,
            'status': JobStatus.PENDING.value,
            'created_at': time.time(),
            'updated_at': time.time(),
            'result': None,
            'error': None
        }
        
        # Start processing in a separate thread
        threading.Thread(
            target=self._process_job,
            args=(job_id, input_type, output_type, input_data, parameters)
        ).start()
        
        return job_id
    
    def _process_job(self, job_id, input_type, output_type, input_data, parameters):
        """
        Process a content generation job.
        
        Args:
            job_id: Unique identifier for the job
            input_type: Type of input
            output_type: Type of output
            input_data: Input data
            parameters: Additional parameters
        """
        try:
            # Update job status
            self.jobs[job_id]['status'] = JobStatus.PROCESSING.value
            self.jobs[job_id]['updated_at'] = time.time()
            
            # Get appropriate pipeline
            pipeline_key = (input_type, output_type)
            if pipeline_key not in self.pipelines:
                raise ValueError(f"No pipeline available for {input_type} to {output_type} conversion")
            
            pipeline = self.pipelines[pipeline_key]
            
            # Process the job
            result = pipeline.process(input_data, parameters or {})
            
            # Update job with result
            self.jobs[job_id]['status'] = JobStatus.COMPLETED.value
            self.jobs[job_id]['result'] = result
            self.jobs[job_id]['updated_at'] = time.time()
            
        except Exception as e:
            # Update job with error
            self.jobs[job_id]['status'] = JobStatus.FAILED.value
            self.jobs[job_id]['error'] = str(e)
            self.jobs[job_id]['updated_at'] = time.time()
    
    def get_job_status(self, job_id):
        """
        Get the status of a job.
        
        Args:
            job_id: Unique identifier for the job
            
        Returns:
            dict: Job status information
        """
        if job_id not in self.jobs:
            raise ValueError(f"Job {job_id} not found")
        
        job = self.jobs[job_id]
        return {
            'id': job['id'],
            'status': job['status'],
            'created_at': job['created_at'],
            'updated_at': job['updated_at'],
            'error': job['error']
        }
    
    def get_job_result(self, job_id):
        """
        Get the result of a completed job.
        
        Args:
            job_id: Unique identifier for the job
            
        Returns:
            dict: Job result
        """
        if job_id not in self.jobs:
            raise ValueError(f"Job {job_id} not found")
        
        job = self.jobs[job_id]
        
        if job['status'] != JobStatus.COMPLETED.value:
            raise ValueError(f"Job {job_id} is not completed")
        
        return {
            'id': job['id'],
            'result': job['result']
        }
