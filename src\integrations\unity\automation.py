"""
Unity automation module for game asset and game creation.
"""

import os
import subprocess
import json
import tempfile
import time
import shutil

class UnityAutomation:
    """
    Automation class for Unity operations.
    
    This class provides methods to automate various Unity operations
    for game asset processing and game creation.
    """
    
    def __init__(self, unity_path=None, project_path=None):
        """
        Initialize the Unity automation.
        
        Args:
            unity_path: Path to the Unity executable (default: from environment variable)
            project_path: Path to the Unity project (default: from environment variable)
        """
        self.unity_path = unity_path or os.environ.get('UNITY_PATH', 'Unity.exe')
        self.project_path = project_path or os.environ.get('UNITY_PROJECT_PATH')
        
        # Create scripts directory if it doesn't exist
        self.scripts_dir = os.path.join(os.path.dirname(__file__), 'scripts')
        os.makedirs(self.scripts_dir, exist_ok=True)
        
        # Create the Unity scripts if they don't exist
        self._create_unity_scripts()
    
    def _create_unity_scripts(self):
        """Create Unity C# scripts for automation."""
        # Script for importing assets
        import_script = """
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using System;

public class AssetImporter
{
    [MenuItem("Tools/Import Assets")]
    public static void ImportAssets()
    {
        // Read parameters from command line arguments
        string[] args = System.Environment.GetCommandLineArgs();
        string paramsJson = "";
        
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i] == "-params" && i + 1 < args.Length)
            {
                paramsJson = args[i + 1];
                break;
            }
        }
        
        if (string.IsNullOrEmpty(paramsJson))
        {
            Debug.LogError("No parameters provided");
            return;
        }
        
        // Parse parameters
        Dictionary<string, object> parameters = JsonUtility.FromJson<Dictionary<string, object>>(paramsJson);
        
        string assetPath = parameters["assetPath"].ToString();
        string outputPath = parameters["outputPath"].ToString();
        bool optimize = (bool)parameters["optimize"];
        
        // Import the asset
        AssetDatabase.ImportAsset(assetPath, ImportAssetOptions.ForceUpdate);
        
        // Get the imported asset
        UnityEngine.Object asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(assetPath);
        
        if (asset == null)
        {
            Debug.LogError("Failed to import asset: " + assetPath);
            return;
        }
        
        // Optimize if requested
        if (optimize)
        {
            // Apply optimization based on asset type
            if (asset is GameObject)
            {
                OptimizeModel(assetPath);
            }
            else if (asset is Texture2D)
            {
                OptimizeTexture(assetPath);
            }
        }
        
        // Export the asset if needed
        if (!string.IsNullOrEmpty(outputPath))
        {
            // Create a prefab or export as needed
            if (asset is GameObject)
            {
                GameObject go = asset as GameObject;
                string prefabPath = outputPath;
                
                if (!prefabPath.EndsWith(".prefab"))
                {
                    prefabPath += ".prefab";
                }
                
                PrefabUtility.SaveAsPrefabAsset(go, prefabPath);
                Debug.Log("Asset exported as prefab: " + prefabPath);
            }
            else
            {
                // For other asset types, just copy the file
                File.Copy(assetPath, outputPath, true);
                Debug.Log("Asset exported: " + outputPath);
            }
        }
        
        // Save changes
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        
        Debug.Log("Asset import completed: " + assetPath);
    }
    
    private static void OptimizeModel(string assetPath)
    {
        // Get model importer
        ModelImporter importer = AssetImporter.GetAtPath(assetPath) as ModelImporter;
        
        if (importer != null)
        {
            // Set optimization options
            importer.meshCompression = ModelImporterMeshCompression.Medium;
            importer.optimizeMeshVertices = true;
            importer.optimizeMeshPolygons = true;
            importer.importBlendShapes = false;
            
            // Apply changes
            importer.SaveAndReimport();
            Debug.Log("Model optimized: " + assetPath);
        }
    }
    
    private static void OptimizeTexture(string assetPath)
    {
        // Get texture importer
        TextureImporter importer = AssetImporter.GetAtPath(assetPath) as TextureImporter;
        
        if (importer != null)
        {
            // Set optimization options
            importer.maxTextureSize = 1024;
            importer.compressionQuality = 50;
            importer.crunchedCompression = true;
            
            // Apply changes
            importer.SaveAndReimport();
            Debug.Log("Texture optimized: " + assetPath);
        }
    }
}
"""
        
        # Script for creating a simple game
        game_script = """
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using System;

public class GameCreator
{
    [MenuItem("Tools/Create Game")]
    public static void CreateGame()
    {
        // Read parameters from command line arguments
        string[] args = System.Environment.GetCommandLineArgs();
        string paramsJson = "";
        
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i] == "-params" && i + 1 < args.Length)
            {
                paramsJson = args[i + 1];
                break;
            }
        }
        
        if (string.IsNullOrEmpty(paramsJson))
        {
            Debug.LogError("No parameters provided");
            return;
        }
        
        // Parse parameters
        Dictionary<string, object> parameters = JsonUtility.FromJson<Dictionary<string, object>>(paramsJson);
        
        string gameName = parameters["gameName"].ToString();
        string outputPath = parameters["outputPath"].ToString();
        List<string> assetPaths = parameters["assetPaths"] as List<string>;
        
        // Create a new scene
        EditorSceneManager.NewScene(NewSceneSetup.DefaultGameObjects);
        
        // Add assets to the scene
        foreach (string assetPath in assetPaths)
        {
            GameObject asset = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
            
            if (asset != null)
            {
                GameObject instance = PrefabUtility.InstantiatePrefab(asset) as GameObject;
                instance.transform.position = Vector3.zero;
            }
        }
        
        // Save the scene
        string scenePath = "Assets/Scenes/" + gameName + ".unity";
        EditorSceneManager.SaveScene(EditorSceneManager.GetActiveScene(), scenePath);
        
        // Build the game
        if (!string.IsNullOrEmpty(outputPath))
        {
            BuildPlayerOptions buildOptions = new BuildPlayerOptions();
            buildOptions.scenes = new string[] { scenePath };
            buildOptions.locationPathName = outputPath;
            buildOptions.target = BuildTarget.StandaloneWindows64;
            buildOptions.options = BuildOptions.None;
            
            BuildPipeline.BuildPlayer(buildOptions);
            Debug.Log("Game built: " + outputPath);
        }
        
        Debug.Log("Game creation completed: " + gameName);
    }
}
"""
        
        # Save the scripts
        with open(os.path.join(self.scripts_dir, 'AssetImporter.cs'), 'w') as f:
            f.write(import_script)
        
        with open(os.path.join(self.scripts_dir, 'GameCreator.cs'), 'w') as f:
            f.write(game_script)
    
    def import_asset(self, asset_path, output_path=None, optimize=True):
        """
        Import an asset into Unity.
        
        Args:
            asset_path: Path to the asset file
            output_path: Path to save the processed asset (default: None)
            optimize: Whether to optimize the asset (default: True)
            
        Returns:
            str: Path to the imported asset in the Unity project
        """
        if self.project_path is None:
            raise ValueError("Unity project path not specified")
        
        # Copy the asset to the Unity project if it's not already there
        unity_asset_path = asset_path
        if not asset_path.startswith(self.project_path):
            # Determine asset type and destination folder
            ext = os.path.splitext(asset_path)[1].lower()
            if ext in ['.fbx', '.obj', '.glb', '.gltf']:
                dest_folder = os.path.join(self.project_path, 'Assets', 'Models')
            elif ext in ['.png', '.jpg', '.jpeg', '.tga']:
                dest_folder = os.path.join(self.project_path, 'Assets', 'Textures')
            else:
                dest_folder = os.path.join(self.project_path, 'Assets', 'Other')
            
            # Create destination folder if it doesn't exist
            os.makedirs(dest_folder, exist_ok=True)
            
            # Copy the asset
            unity_asset_path = os.path.join(dest_folder, os.path.basename(asset_path))
            shutil.copy2(asset_path, unity_asset_path)
        
        # Prepare parameters
        params = {
            'assetPath': os.path.relpath(unity_asset_path, self.project_path).replace('\\', '/'),
            'outputPath': output_path or '',
            'optimize': optimize
        }
        
        # Run Unity with the import script
        cmd = [
            self.unity_path,
            '-projectPath', self.project_path,
            '-executeMethod', 'AssetImporter.ImportAssets',
            '-batchmode',
            '-nographics',
            '-quit',
            '-params', json.dumps(params)
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(result.stdout)
            return unity_asset_path
        except subprocess.CalledProcessError as e:
            print(f"Error importing asset: {e}")
            print(f"Unity output: {e.stdout}")
            print(f"Unity error: {e.stderr}")
            raise
    
    def create_game(self, game_name, asset_paths, output_path=None):
        """
        Create a simple game in Unity.
        
        Args:
            game_name: Name of the game
            asset_paths: List of paths to assets to include in the game
            output_path: Path to save the built game (default: None)
            
        Returns:
            str: Path to the built game
        """
        if self.project_path is None:
            raise ValueError("Unity project path not specified")
        
        # Ensure all assets are imported into the Unity project
        unity_asset_paths = []
        for asset_path in asset_paths:
            unity_asset_path = self.import_asset(asset_path)
            unity_asset_paths.append(os.path.relpath(unity_asset_path, self.project_path).replace('\\', '/'))
        
        # Prepare parameters
        params = {
            'gameName': game_name,
            'outputPath': output_path or '',
            'assetPaths': unity_asset_paths
        }
        
        # Run Unity with the game creation script
        cmd = [
            self.unity_path,
            '-projectPath', self.project_path,
            '-executeMethod', 'GameCreator.CreateGame',
            '-batchmode',
            '-nographics',
            '-quit',
            '-params', json.dumps(params)
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(result.stdout)
            return output_path
        except subprocess.CalledProcessError as e:
            print(f"Error creating game: {e}")
            print(f"Unity output: {e.stdout}")
            print(f"Unity error: {e.stderr}")
            raise
