#!/usr/bin/env python3
"""
Enhanced face processing with improved algorithms and better 3D mapping
"""

import cv2
import numpy as np
import os
import json
from PIL import Image
import math

class EnhancedFaceProcessor:
    """Enhanced face processing with better algorithms for realistic character generation"""
    
    def __init__(self):
        self.face_cascade = None
        self.eye_cascade = None
        self.init_opencv_cascades()
        
        # Try to load advanced libraries if available
        self.trimesh_available = self.check_trimesh()
        self.scipy_available = self.check_scipy()
    
    def init_opencv_cascades(self):
        """Initialize OpenCV cascades with better error handling"""
        try:
            cascade_path = cv2.data.haarcascades
            
            self.face_cascade = cv2.CascadeClassifier(
                os.path.join(cascade_path, 'haarcascade_frontalface_default.xml')
            )
            self.eye_cascade = cv2.CascadeClassifier(
                os.path.join(cascade_path, 'haarcascade_eye.xml')
            )
            
            print("✅ Enhanced OpenCV cascades loaded")
            
        except Exception as e:
            print(f"⚠️ OpenCV cascade loading error: {e}")
    
    def check_trimesh(self):
        """Check if trimesh is available for advanced 3D processing"""
        try:
            import trimesh
            print("✅ Trimesh available for 3D mesh processing")
            return True
        except ImportError:
            print("⚠️ Trimesh not available")
            return False
    
    def check_scipy(self):
        """Check if scipy is available for advanced calculations"""
        try:
            import scipy
            print("✅ SciPy available for advanced calculations")
            return True
        except ImportError:
            print("⚠️ SciPy not available")
            return False
    
    def extract_enhanced_face_features(self, image_path):
        """Extract enhanced face features with better algorithms"""
        try:
            # Load and preprocess image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Could not load image")
            
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Enhanced face detection
            faces = self.detect_faces_enhanced(gray)
            
            if len(faces) == 0:
                print("⚠️ No faces detected, analyzing full image")
                return self.analyze_full_image_enhanced(image_rgb)
            
            # Use the largest face
            face = max(faces, key=lambda f: f[2] * f[3])
            x, y, w, h = face
            
            # Extract face region with padding
            padding = int(min(w, h) * 0.1)
            x_start = max(0, x - padding)
            y_start = max(0, y - padding)
            x_end = min(image.shape[1], x + w + padding)
            y_end = min(image.shape[0], y + h + padding)
            
            face_region = image_rgb[y_start:y_end, x_start:x_end]
            face_gray = gray[y_start:y_end, x_start:x_end]
            
            # Enhanced feature analysis
            features = {
                'face_detected': True,
                'method': 'enhanced_opencv',
                'face_bounds': {'x': x, 'y': y, 'width': w, 'height': h},
                'image_size': {'width': image.shape[1], 'height': image.shape[0]},
                
                # Enhanced facial analysis
                'face_shape': self.analyze_face_shape_enhanced(face_region),
                'skin_tone': self.extract_skin_tone_enhanced(face_region),
                'facial_features': self.detect_facial_features_enhanced(face_gray),
                'facial_structure': self.analyze_facial_structure_enhanced(face_region),
                'color_analysis': self.analyze_colors_enhanced(face_region),
                'texture_analysis': self.analyze_texture_enhanced(face_gray),
                'symmetry_analysis': self.analyze_symmetry_enhanced(face_region),
                'depth_estimation': self.estimate_depth_enhanced(face_gray),
                'expression_analysis': self.analyze_expression_enhanced(face_region),
                
                # 3D mapping data
                'face_landmarks_estimated': self.estimate_face_landmarks(face_gray),
                'facial_planes': self.estimate_facial_planes(face_region),
                'head_pose': self.estimate_head_pose(face_gray)
            }
            
            return features
            
        except Exception as e:
            print(f"Enhanced face feature extraction error: {e}")
            return self.get_default_enhanced_features()
    
    def detect_faces_enhanced(self, gray_image):
        """Enhanced face detection with multiple scales"""
        if self.face_cascade is None:
            return []
        
        try:
            # Multi-scale detection for better accuracy
            faces = self.face_cascade.detectMultiScale(
                gray_image,
                scaleFactor=1.05,  # Smaller scale factor for better detection
                minNeighbors=6,    # More neighbors for better accuracy
                minSize=(50, 50),  # Larger minimum size
                flags=cv2.CASCADE_SCALE_IMAGE
            )
            return faces
        except Exception as e:
            print(f"Enhanced face detection error: {e}")
            return []
    
    def analyze_face_shape_enhanced(self, face_region):
        """Enhanced face shape analysis"""
        h, w = face_region.shape[:2]
        aspect_ratio = w / h
        
        # More detailed face shape classification
        if aspect_ratio > 0.85:
            if aspect_ratio > 0.95:
                shape = "round"
                shape_score = 0.9
            else:
                shape = "square"
                shape_score = 0.8
        elif aspect_ratio < 0.75:
            if aspect_ratio < 0.65:
                shape = "long"
                shape_score = 0.85
            else:
                shape = "oval"
                shape_score = 0.95
        else:
            shape = "heart"
            shape_score = 0.8
        
        return {
            'shape': shape,
            'aspect_ratio': float(aspect_ratio),
            'confidence': shape_score,
            'width': w,
            'height': h,
            'face_area': w * h
        }
    
    def extract_skin_tone_enhanced(self, face_region):
        """Enhanced skin tone extraction with better sampling"""
        try:
            # Convert to different color spaces for better analysis
            hsv = cv2.cvtColor(face_region, cv2.COLOR_RGB2HSV)
            lab = cv2.cvtColor(face_region, cv2.COLOR_RGB2LAB)
            
            # Define better skin detection range in HSV
            lower_skin = np.array([0, 20, 70], dtype=np.uint8)
            upper_skin = np.array([20, 255, 255], dtype=np.uint8)
            
            # Create skin mask
            skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            # Morphological operations to clean up mask
            kernel = np.ones((3,3), np.uint8)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
            skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)
            
            # Extract skin pixels
            skin_pixels = face_region[skin_mask > 0]
            
            if len(skin_pixels) > 100:  # Need sufficient samples
                # Calculate statistics
                avg_color = np.mean(skin_pixels, axis=0)
                std_color = np.std(skin_pixels, axis=0)
                
                # Analyze in LAB space for better categorization
                lab_pixels = lab[skin_mask > 0]
                avg_lab = np.mean(lab_pixels, axis=0)
                
                return {
                    'rgb': avg_color.tolist(),
                    'rgb_std': std_color.tolist(),
                    'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
                    'lab': avg_lab.tolist(),
                    'tone_category': self.categorize_skin_tone_enhanced(avg_color, avg_lab),
                    'uniformity': float(1.0 / (1.0 + np.mean(std_color))),
                    'sample_count': len(skin_pixels),
                    'method': 'enhanced_hsv_mask'
                }
            else:
                # Fallback to center region sampling
                h, w = face_region.shape[:2]
                center_region = face_region[h//3:2*h//3, w//3:2*w//3]
                avg_color = np.mean(center_region.reshape(-1, 3), axis=0)
                
                return {
                    'rgb': avg_color.tolist(),
                    'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
                    'tone_category': self.categorize_skin_tone_enhanced(avg_color),
                    'method': 'center_fallback'
                }
                
        except Exception as e:
            print(f"Enhanced skin tone extraction error: {e}")
            return {'rgb': [200, 180, 160], 'hex': '#c8b4a0', 'tone_category': 'medium'}
    
    def categorize_skin_tone_enhanced(self, rgb_color, lab_color=None):
        """Enhanced skin tone categorization"""
        if lab_color is not None:
            # Use LAB L* channel for better categorization
            lightness = lab_color[0]
            
            if lightness < 40:
                return 'very_dark'
            elif lightness < 55:
                return 'dark'
            elif lightness < 70:
                return 'medium_dark'
            elif lightness < 85:
                return 'medium'
            elif lightness < 95:
                return 'light'
            else:
                return 'very_light'
        else:
            # Fallback to RGB-based categorization
            brightness = np.mean(rgb_color)
            
            if brightness < 80:
                return 'very_dark'
            elif brightness < 120:
                return 'dark'
            elif brightness < 160:
                return 'medium'
            elif brightness < 200:
                return 'light'
            else:
                return 'very_light'
    
    def detect_facial_features_enhanced(self, face_gray):
        """Enhanced facial feature detection"""
        features = {
            'eyes': [],
            'eye_count': 0,
            'eye_regions': [],
            'facial_landmarks': []
        }
        
        try:
            if self.eye_cascade is not None:
                # Detect eyes with multiple parameters
                eyes = self.eye_cascade.detectMultiScale(
                    face_gray,
                    scaleFactor=1.1,
                    minNeighbors=5,
                    minSize=(20, 20)
                )
                
                features['eyes'] = eyes.tolist()
                features['eye_count'] = len(eyes)
                
                # Analyze eye regions
                for (ex, ey, ew, eh) in eyes:
                    eye_region = face_gray[ey:ey+eh, ex:ex+ew]
                    eye_analysis = self.analyze_eye_region(eye_region)
                    features['eye_regions'].append(eye_analysis)
            
            # Estimate additional landmarks using edge detection
            features['facial_landmarks'] = self.estimate_facial_landmarks(face_gray)
            
        except Exception as e:
            print(f"Facial feature detection error: {e}")
        
        return features
    
    def analyze_eye_region(self, eye_region):
        """Analyze individual eye region"""
        try:
            # Calculate eye openness and characteristics
            h, w = eye_region.shape
            
            # Use edge detection to find eye shape
            edges = cv2.Canny(eye_region, 50, 150)
            edge_density = np.sum(edges > 0) / (h * w)
            
            # Estimate eye openness based on vertical edge distribution
            vertical_profile = np.sum(edges, axis=1)
            openness = np.max(vertical_profile) / np.sum(vertical_profile) if np.sum(vertical_profile) > 0 else 0.5
            
            return {
                'width': w,
                'height': h,
                'aspect_ratio': w / h,
                'edge_density': float(edge_density),
                'estimated_openness': float(openness),
                'brightness': float(np.mean(eye_region))
            }
        except Exception as e:
            print(f"Eye region analysis error: {e}")
            return {'width': 0, 'height': 0, 'aspect_ratio': 1.0}
    
    def estimate_facial_landmarks(self, face_gray):
        """Estimate facial landmarks using edge detection and contours"""
        try:
            # Apply edge detection
            edges = cv2.Canny(face_gray, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            landmarks = []
            
            # Extract key points from contours
            for contour in contours:
                if cv2.contourArea(contour) > 50:  # Filter small contours
                    # Get extreme points
                    leftmost = tuple(contour[contour[:,:,0].argmin()][0])
                    rightmost = tuple(contour[contour[:,:,0].argmax()][0])
                    topmost = tuple(contour[contour[:,:,1].argmin()][0])
                    bottommost = tuple(contour[contour[:,:,1].argmax()][0])
                    
                    landmarks.extend([leftmost, rightmost, topmost, bottommost])
            
            return landmarks[:20]  # Limit to 20 landmarks
            
        except Exception as e:
            print(f"Landmark estimation error: {e}")
            return []
    
    def analyze_facial_structure_enhanced(self, face_region):
        """Enhanced facial structure analysis"""
        h, w = face_region.shape[:2]
        
        # Divide face into regions for detailed analysis
        upper_third = face_region[:h//3, :]
        middle_third = face_region[h//3:2*h//3, :]
        lower_third = face_region[2*h//3:, :]
        
        # Analyze each region
        structure = {
            'proportions': {
                'upper_height': h//3,
                'middle_height': h//3,
                'lower_height': h - 2*(h//3),
                'width': w
            },
            'region_analysis': {
                'forehead': self.analyze_region_characteristics(upper_third),
                'midface': self.analyze_region_characteristics(middle_third),
                'lower_face': self.analyze_region_characteristics(lower_third)
            },
            'overall_symmetry': self.calculate_symmetry_enhanced(face_region),
            'facial_angles': self.estimate_facial_angles(face_region)
        }
        
        return structure
    
    def analyze_region_characteristics(self, region):
        """Analyze characteristics of a facial region"""
        try:
            # Calculate various metrics
            brightness = float(np.mean(region))
            contrast = float(np.std(region))
            
            # Edge density
            gray_region = cv2.cvtColor(region, cv2.COLOR_RGB2GRAY) if len(region.shape) == 3 else region
            edges = cv2.Canny(gray_region, 50, 150)
            edge_density = float(np.sum(edges > 0) / edges.size)
            
            return {
                'brightness': brightness,
                'contrast': contrast,
                'edge_density': edge_density,
                'texture_complexity': contrast * edge_density
            }
        except Exception as e:
            print(f"Region analysis error: {e}")
            return {'brightness': 128, 'contrast': 30, 'edge_density': 0.1}
    
    def calculate_symmetry_enhanced(self, face_region):
        """Enhanced symmetry calculation"""
        try:
            h, w = face_region.shape[:2]
            
            # Convert to grayscale if needed
            if len(face_region.shape) == 3:
                gray_face = cv2.cvtColor(face_region, cv2.COLOR_RGB2GRAY)
            else:
                gray_face = face_region
            
            # Split face in half
            left_half = gray_face[:, :w//2]
            right_half = gray_face[:, w//2:]
            
            # Flip right half
            right_half_flipped = cv2.flip(right_half, 1)
            
            # Resize to match if needed
            min_w = min(left_half.shape[1], right_half_flipped.shape[1])
            left_half = left_half[:, :min_w]
            right_half_flipped = right_half_flipped[:, :min_w]
            
            # Calculate structural similarity
            diff = cv2.absdiff(left_half, right_half_flipped)
            symmetry_score = 1.0 - (np.mean(diff) / 255.0)
            
            return {
                'symmetry_score': float(max(0, min(1, symmetry_score))),
                'asymmetry_regions': self.identify_asymmetry_regions(diff)
            }
            
        except Exception as e:
            print(f"Symmetry calculation error: {e}")
            return {'symmetry_score': 0.8, 'asymmetry_regions': []}
    
    def identify_asymmetry_regions(self, diff_image):
        """Identify regions with high asymmetry"""
        try:
            # Find regions with high difference
            threshold = np.mean(diff_image) + np.std(diff_image)
            high_diff_mask = diff_image > threshold
            
            # Find contours of asymmetric regions
            contours, _ = cv2.findContours(
                high_diff_mask.astype(np.uint8), 
                cv2.RETR_EXTERNAL, 
                cv2.CHAIN_APPROX_SIMPLE
            )
            
            asymmetry_regions = []
            for contour in contours:
                if cv2.contourArea(contour) > 100:  # Filter small regions
                    x, y, w, h = cv2.boundingRect(contour)
                    asymmetry_regions.append({
                        'x': int(x), 'y': int(y), 
                        'width': int(w), 'height': int(h),
                        'area': float(cv2.contourArea(contour))
                    })
            
            return asymmetry_regions
            
        except Exception as e:
            print(f"Asymmetry region identification error: {e}")
            return []
    
    def analyze_full_image_enhanced(self, image_rgb):
        """Enhanced analysis when no face is detected"""
        return {
            'face_detected': False,
            'method': 'enhanced_full_image',
            'face_shape': {'shape': 'unknown', 'aspect_ratio': 1.0, 'confidence': 0.0},
            'skin_tone': self.extract_average_color_enhanced(image_rgb),
            'facial_features': {'eyes': [], 'eye_count': 0},
            'facial_structure': {'overall_symmetry': {'symmetry_score': 0.5}},
            'full_image_analysis': True
        }
    
    def extract_average_color_enhanced(self, image):
        """Enhanced average color extraction"""
        # Sample from center region for better skin tone estimation
        h, w = image.shape[:2]
        center_region = image[h//4:3*h//4, w//4:3*w//4]
        avg_color = np.mean(center_region.reshape(-1, 3), axis=0)
        
        return {
            'rgb': avg_color.tolist(),
            'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
            'tone_category': self.categorize_skin_tone_enhanced(avg_color),
            'method': 'center_region_enhanced'
        }
    
    def get_default_enhanced_features(self):
        """Return enhanced default features"""
        return {
            'face_detected': False,
            'method': 'enhanced_fallback',
            'analysis_failed': True,
            'face_shape': {'shape': 'oval', 'aspect_ratio': 0.75, 'confidence': 0.5},
            'skin_tone': {'rgb': [200, 180, 160], 'hex': '#c8b4a0', 'tone_category': 'medium'},
            'facial_features': {'eyes': [], 'eye_count': 0},
            'facial_structure': {'overall_symmetry': {'symmetry_score': 0.8}},
            'enhanced_processing': True
        }
