using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

public class SceneSetup : EditorWindow
{
    // Prefabs
    private GameObject playerPrefab;
    private GameObject enemyPrefab;
    private GameObject collectiblePrefab;
    
    // Scene parameters
    private int terrainSize = 100;
    private int numEnemies = 5;
    private int numCollectibles = 10;
    
    // UI elements
    private bool showAdvancedOptions = false;
    
    [MenuItem("Tools/Scene Setup")]
    public static void ShowWindow()
    {
        GetWindow<SceneSetup>("Scene Setup");
    }
    
    void OnGUI()
    {
        GUILayout.Label("Scene Setup Tool", EditorStyles.boldLabel);
        
        EditorGUILayout.Space();
        
        // Prefab fields
        playerPrefab = (GameObject)EditorGUILayout.ObjectField("Player Prefab", playerPrefab, typeof(GameObject), false);
        enemyPrefab = (GameObject)EditorGUILayout.ObjectField("Enemy Prefab", enemyPrefab, typeof(GameObject), false);
        collectiblePrefab = (GameObject)EditorGUILayout.ObjectField("Collectible Prefab", collectiblePrefab, typeof(GameObject), false);
        
        EditorGUILayout.Space();
        
        // Scene parameters
        terrainSize = EditorGUILayout.IntSlider("Terrain Size", terrainSize, 50, 500);
        numEnemies = EditorGUILayout.IntSlider("Number of Enemies", numEnemies, 0, 20);
        numCollectibles = EditorGUILayout.IntSlider("Number of Collectibles", numCollectibles, 0, 50);
        
        EditorGUILayout.Space();
        
        // Advanced options
        showAdvancedOptions = EditorGUILayout.Foldout(showAdvancedOptions, "Advanced Options");
        if (showAdvancedOptions)
        {
            EditorGUI.indentLevel++;
            
            // Add advanced options here
            
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space();
        
        // Create scene button
        if (GUILayout.Button("Create Scene"))
        {
            CreateScene();
        }
    }
    
    void CreateScene()
    {
        // Check if prefabs are assigned
        if (playerPrefab == null)
        {
            Debug.LogError("Player prefab is not assigned!");
            return;
        }
        
        // Create terrain
        CreateTerrain();
        
        // Create player
        CreatePlayer();
        
        // Create enemies
        if (enemyPrefab != null)
        {
            CreateEnemies();
        }
        
        // Create collectibles
        if (collectiblePrefab != null)
        {
            CreateCollectibles();
        }
        
        // Create lighting
        CreateLighting();
        
        // Create UI
        CreateUI();
        
        Debug.Log("Scene created successfully!");
    }
    
    void CreateTerrain()
    {
        // Create terrain game object
        GameObject terrainObj = new GameObject("Terrain");
        Terrain terrain = terrainObj.AddComponent<Terrain>();
        TerrainCollider terrainCollider = terrainObj.AddComponent<TerrainCollider>();
        
        // Configure terrain
        TerrainData terrainData = new TerrainData();
        terrainData.size = new Vector3(terrainSize, 50, terrainSize);
        terrainData.SetHeights(0, 0, new float[513, 513]);
        
        // Assign terrain data
        terrain.terrainData = terrainData;
        terrainCollider.terrainData = terrainData;
        
        // Position terrain
        terrainObj.transform.position = new Vector3(-terrainSize / 2, 0, -terrainSize / 2);
        
        // Create a simple terrain
        float[,] heights = new float[513, 513];
        for (int i = 0; i < 513; i++)
        {
            for (int j = 0; j < 513; j++)
            {
                heights[i, j] = Random.Range(0f, 0.1f);
            }
        }
        terrainData.SetHeights(0, 0, heights);
        
        // Save terrain data asset
        AssetDatabase.CreateAsset(terrainData, "Assets/TerrainData.asset");
    }
    
    void CreatePlayer()
    {
        // Instantiate player prefab
        GameObject player = Instantiate(playerPrefab);
        player.name = "Player";
        
        // Position player
        player.transform.position = new Vector3(0, 2, 0);
        
        // Add required components if they don't exist
        if (player.GetComponent<CharacterController>() == null)
        {
            player.AddComponent<CharacterController>();
        }
        
        if (player.GetComponent<PlayerController>() == null)
        {
            player.AddComponent<PlayerController>();
        }
        
        if (player.GetComponent<PlayerHealth>() == null)
        {
            player.AddComponent<PlayerHealth>();
        }
        
        // Create camera
        GameObject cameraObj = new GameObject("Main Camera");
        Camera camera = cameraObj.AddComponent<Camera>();
        cameraObj.tag = "MainCamera";
        
        // Position camera
        cameraObj.transform.position = new Vector3(0, 1.6f, 0);
        cameraObj.transform.parent = player.transform;
        
        // Add camera controller
        CameraController cameraController = cameraObj.AddComponent<CameraController>();
        cameraController.target = player.transform;
        
        // Add audio listener
        cameraObj.AddComponent<AudioListener>();
    }
    
    void CreateEnemies()
    {
        // Create enemy container
        GameObject enemyContainer = new GameObject("Enemies");
        
        // Create patrol points container
        GameObject patrolPointsContainer = new GameObject("PatrolPoints");
        
        // Create enemies
        for (int i = 0; i < numEnemies; i++)
        {
            // Calculate random position
            float x = Random.Range(-terrainSize / 2 + 10, terrainSize / 2 - 10);
            float z = Random.Range(-terrainSize / 2 + 10, terrainSize / 2 - 10);
            
            // Instantiate enemy prefab
            GameObject enemy = Instantiate(enemyPrefab);
            enemy.name = "Enemy_" + i;
            
            // Position enemy
            enemy.transform.position = new Vector3(x, 1, z);
            
            // Set parent
            enemy.transform.parent = enemyContainer.transform;
            
            // Add required components if they don't exist
            if (enemy.GetComponent<Enemy>() == null)
            {
                Enemy enemyScript = enemy.AddComponent<Enemy>();
                
                // Create patrol points
                Transform[] patrolPoints = new Transform[3];
                for (int j = 0; j < 3; j++)
                {
                    GameObject patrolPoint = new GameObject("PatrolPoint_" + i + "_" + j);
                    patrolPoint.transform.position = new Vector3(
                        x + Random.Range(-10f, 10f),
                        1,
                        z + Random.Range(-10f, 10f)
                    );
                    patrolPoint.transform.parent = patrolPointsContainer.transform;
                    patrolPoints[j] = patrolPoint.transform;
                }
                
                // Assign patrol points
                enemyScript.patrolPoints = patrolPoints;
            }
            
            // Add NavMeshAgent if it doesn't exist
            if (enemy.GetComponent<UnityEngine.AI.NavMeshAgent>() == null)
            {
                enemy.AddComponent<UnityEngine.AI.NavMeshAgent>();
            }
        }
    }
    
    void CreateCollectibles()
    {
        // Create collectible container
        GameObject collectibleContainer = new GameObject("Collectibles");
        
        // Create collectibles
        for (int i = 0; i < numCollectibles; i++)
        {
            // Calculate random position
            float x = Random.Range(-terrainSize / 2 + 10, terrainSize / 2 - 10);
            float z = Random.Range(-terrainSize / 2 + 10, terrainSize / 2 - 10);
            
            // Instantiate collectible prefab
            GameObject collectible = Instantiate(collectiblePrefab);
            collectible.name = "Collectible_" + i;
            
            // Position collectible
            collectible.transform.position = new Vector3(x, 1, z);
            
            // Set parent
            collectible.transform.parent = collectibleContainer.transform;
            
            // Add required components if they don't exist
            if (collectible.GetComponent<Collectible>() == null)
            {
                collectible.AddComponent<Collectible>();
            }
            
            // Add collider if it doesn't exist
            if (collectible.GetComponent<Collider>() == null)
            {
                SphereCollider collider = collectible.AddComponent<SphereCollider>();
                collider.isTrigger = true;
            }
        }
    }
    
    void CreateLighting()
    {
        // Create directional light
        GameObject lightObj = new GameObject("Directional Light");
        Light light = lightObj.AddComponent<Light>();
        
        // Configure light
        light.type = LightType.Directional;
        light.intensity = 1f;
        light.shadows = LightShadows.Soft;
        
        // Position light
        lightObj.transform.rotation = Quaternion.Euler(50, -30, 0);
    }
    
    void CreateUI()
    {
        // Create canvas
        GameObject canvasObj = new GameObject("Canvas");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
        canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
        
        // Configure canvas
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        
        // Create event system
        GameObject eventSystemObj = new GameObject("EventSystem");
        eventSystemObj.AddComponent<UnityEngine.EventSystems.EventSystem>();
        eventSystemObj.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
        
        // Create game manager
        GameObject gameManagerObj = new GameObject("GameManager");
        GameManager gameManager = gameManagerObj.AddComponent<GameManager>();
        
        // Create UI elements
        CreateUIElements(canvasObj, gameManager);
    }
    
    void CreateUIElements(GameObject canvas, GameManager gameManager)
    {
        // Create main menu UI
        GameObject mainMenuUI = new GameObject("MainMenuUI");
        mainMenuUI.transform.parent = canvas.transform;
        
        // Create game UI
        GameObject gameUI = new GameObject("GameUI");
        gameUI.transform.parent = canvas.transform;
        
        // Create pause menu UI
        GameObject pauseMenuUI = new GameObject("PauseMenuUI");
        pauseMenuUI.transform.parent = canvas.transform;
        
        // Create game over UI
        GameObject gameOverUI = new GameObject("GameOverUI");
        gameOverUI.transform.parent = canvas.transform;
        
        // Assign UI elements to game manager
        gameManager.mainMenuUI = mainMenuUI;
        gameManager.gameUI = gameUI;
        gameManager.pauseMenuUI = pauseMenuUI;
        gameManager.gameOverUI = gameOverUI;
        
        // Create score text
        GameObject scoreTextObj = new GameObject("ScoreText");
        scoreTextObj.transform.parent = gameUI.transform;
        UnityEngine.UI.Text scoreText = scoreTextObj.AddComponent<UnityEngine.UI.Text>();
        
        // Configure score text
        scoreText.text = "Score: 0";
        scoreText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        scoreText.fontSize = 24;
        scoreText.color = Color.white;
        
        // Position score text
        UnityEngine.UI.RectTransform scoreTextRect = scoreText.GetComponent<UnityEngine.UI.RectTransform>();
        scoreTextRect.anchorMin = new Vector2(0, 1);
        scoreTextRect.anchorMax = new Vector2(0, 1);
        scoreTextRect.pivot = new Vector2(0, 1);
        scoreTextRect.anchoredPosition = new Vector2(20, -20);
        scoreTextRect.sizeDelta = new Vector2(200, 30);
        
        // Assign score text to game manager
        gameManager.scoreText = scoreText;
    }
}
