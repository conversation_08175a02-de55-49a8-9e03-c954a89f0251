#!/usr/bin/env python3
"""
Professional 3D mesh generator for realistic character creation
"""

import numpy as np
import math
import json
import struct

class ProfessionalMeshGenerator:
    """Generate high-quality 3D meshes for realistic characters"""
    
    def __init__(self):
        self.trimesh_available = self.check_trimesh()
        self.pymeshlab_available = self.check_pymeshlab()
    
    def check_trimesh(self):
        """Check if trimesh is available"""
        try:
            import trimesh
            return True
        except ImportError:
            return False
    
    def check_pymeshlab(self):
        """Check if pymeshlab is available"""
        try:
            import pymeshlab
            return True
        except ImportError:
            return False
    
    def generate_realistic_character_mesh(self, face_features, character_params):
        """Generate a realistic character mesh based on face features"""
        try:
            print("🎨 Generating professional character mesh...")
            
            # Extract key parameters
            proportions = character_params.get('proportions', {})
            colors = character_params.get('colors', {})
            
            # Generate high-quality head mesh
            head_vertices, head_indices, head_normals = self.generate_detailed_head(face_features, proportions)
            
            # Generate body mesh
            body_vertices, body_indices, body_normals = self.generate_detailed_body(proportions)
            
            # Combine meshes
            vertices, indices, normals = self.combine_meshes(
                [(head_vertices, head_indices, head_normals),
                 (body_vertices, body_indices, body_normals)]
            )
            
            # Apply smoothing if available
            if self.trimesh_available or self.pymeshlab_available:
                vertices, indices, normals = self.smooth_mesh(vertices, indices, normals)
            
            # Optimize mesh
            vertices, indices, normals = self.optimize_mesh(vertices, indices, normals)
            
            print(f"✅ Generated mesh: {len(vertices)//3} vertices, {len(indices)//3} faces")
            
            return vertices, indices, normals
            
        except Exception as e:
            print(f"❌ Professional mesh generation failed: {e}")
            # Fallback to basic mesh
            return self.generate_fallback_mesh(character_params)
    
    def generate_detailed_head(self, face_features, proportions):
        """Generate detailed head mesh based on face analysis"""
        try:
            # Base head parameters
            head_center = [0, 1.6, 0]
            base_radius = 0.15
            
            # Apply face proportions
            width_scale = proportions.get('head_width', 1.0)
            height_scale = proportions.get('head_height', 1.0)
            depth_scale = proportions.get('head_depth', 1.0)
            
            # Higher resolution for realistic appearance
            rings = 24  # Increased from 12
            sectors = 32  # Increased from 16
            
            vertices = []
            indices = []
            normals = []
            
            # Generate ellipsoid head with face-specific deformations
            for r in range(rings + 1):
                lat = math.pi * (-0.5 + r / rings)
                y = math.sin(lat) * height_scale
                xz = math.cos(lat)
                
                for s in range(sectors + 1):
                    lon = 2 * math.pi * s / sectors
                    x = xz * math.cos(lon) * width_scale
                    z = xz * math.sin(lon) * depth_scale
                    
                    # Apply facial asymmetry if detected
                    symmetry = proportions.get('facial_symmetry', 1.0)
                    if x > 0:  # Right side of face
                        x *= symmetry
                    
                    # Add facial feature deformations
                    vertex_pos = self.apply_facial_deformations(
                        [x, y, z], face_features, base_radius, head_center
                    )
                    
                    vertices.extend(vertex_pos)
                    
                    # Calculate normal
                    normal = self.calculate_vertex_normal(vertex_pos, head_center)
                    normals.extend(normal)
            
            # Generate indices for triangular faces
            for r in range(rings):
                for s in range(sectors):
                    current = r * (sectors + 1) + s
                    next_ring = (r + 1) * (sectors + 1) + s
                    
                    # Two triangles per quad
                    indices.extend([current, next_ring, current + 1])
                    indices.extend([current + 1, next_ring, next_ring + 1])
            
            return vertices, indices, normals
            
        except Exception as e:
            print(f"Detailed head generation error: {e}")
            return self.generate_basic_head(proportions)
    
    def apply_facial_deformations(self, vertex_pos, face_features, base_radius, head_center):
        """Apply facial feature-based deformations to vertex positions"""
        try:
            x, y, z = vertex_pos
            
            # Scale by base radius and translate to head center
            world_pos = [
                head_center[0] + base_radius * x,
                head_center[1] + base_radius * y,
                head_center[2] + base_radius * z
            ]
            
            # Apply face shape deformations
            face_shape = face_features.get('face_shape', {})
            if face_shape:
                shape_type = face_shape.get('shape', 'oval')
                
                if shape_type == 'round':
                    # Make face rounder
                    world_pos[0] *= 1.05
                    world_pos[2] *= 1.05
                elif shape_type == 'square':
                    # Make jawline more angular
                    if world_pos[1] < head_center[1] - 0.05:  # Lower face
                        world_pos[0] *= 1.1
                elif shape_type == 'long':
                    # Elongate face
                    world_pos[1] *= 1.1
            
            # Apply eye region deformations
            facial_features = face_features.get('facial_features', {})
            if facial_features.get('eye_count', 0) >= 2:
                # Adjust eye socket regions
                if 0.05 < world_pos[1] - head_center[1] < 0.1:  # Eye level
                    if abs(world_pos[0] - head_center[0]) > 0.05:  # Eye socket area
                        world_pos[2] -= 0.01  # Indent eye sockets slightly
            
            return world_pos
            
        except Exception as e:
            print(f"Facial deformation error: {e}")
            return [
                head_center[0] + base_radius * vertex_pos[0],
                head_center[1] + base_radius * vertex_pos[1],
                head_center[2] + base_radius * vertex_pos[2]
            ]
    
    def calculate_vertex_normal(self, vertex_pos, center):
        """Calculate normal vector for a vertex"""
        try:
            # For ellipsoid, normal points outward from center
            normal = [
                vertex_pos[0] - center[0],
                vertex_pos[1] - center[1],
                vertex_pos[2] - center[2]
            ]
            
            # Normalize
            length = math.sqrt(sum(n*n for n in normal))
            if length > 0:
                normal = [n/length for n in normal]
            else:
                normal = [0, 1, 0]  # Default upward normal
            
            return normal
            
        except Exception as e:
            print(f"Normal calculation error: {e}")
            return [0, 1, 0]
    
    def generate_detailed_body(self, proportions):
        """Generate detailed body mesh"""
        try:
            vertices = []
            indices = []
            normals = []
            
            # Body parameters
            body_center = [0, 0.8, 0]
            body_radius = 0.2 * proportions.get('body_scale', 1.0)
            body_height = 0.8
            
            # Higher resolution cylinder for body
            sectors = 16
            
            # Generate body cylinder
            for i in range(sectors + 1):
                angle = 2 * math.pi * i / sectors
                x = body_radius * math.cos(angle)
                z = body_radius * math.sin(angle)
                
                # Bottom vertex
                vertices.extend([body_center[0] + x, body_center[1] - body_height/2, body_center[2] + z])
                normals.extend([x/body_radius, 0, z/body_radius])
                
                # Top vertex
                vertices.extend([body_center[0] + x, body_center[1] + body_height/2, body_center[2] + z])
                normals.extend([x/body_radius, 0, z/body_radius])
            
            # Generate indices
            for i in range(sectors):
                base = i * 2
                next_base = ((i + 1) % (sectors + 1)) * 2
                
                # Two triangles per quad
                indices.extend([base, base + 1, next_base])
                indices.extend([base + 1, next_base + 1, next_base])
            
            # Add limbs
            limb_vertices, limb_indices, limb_normals = self.generate_limbs(proportions)
            
            # Combine body and limbs
            vertex_offset = len(vertices) // 3
            vertices.extend(limb_vertices)
            normals.extend(limb_normals)
            
            # Adjust limb indices
            for idx in limb_indices:
                indices.append(idx + vertex_offset)
            
            return vertices, indices, normals
            
        except Exception as e:
            print(f"Detailed body generation error: {e}")
            return self.generate_basic_body(proportions)
    
    def generate_limbs(self, proportions):
        """Generate arm and leg meshes"""
        vertices = []
        indices = []
        normals = []
        
        limb_radius = 0.08 * proportions.get('limb_scale', 1.0)
        arm_length = 0.6
        leg_length = 0.8
        
        # Arm positions
        arm_positions = [
            [-0.3, 0.8, 0],  # Left arm
            [0.3, 0.8, 0]    # Right arm
        ]
        
        # Leg positions
        leg_positions = [
            [-0.1, 0.0, 0],  # Left leg
            [0.1, 0.0, 0]    # Right leg
        ]
        
        # Generate arms
        for pos in arm_positions:
            limb_verts, limb_inds, limb_norms = self.generate_limb_cylinder(
                pos, [0, -arm_length, 0], limb_radius
            )
            
            vertex_offset = len(vertices) // 3
            vertices.extend(limb_verts)
            normals.extend(limb_norms)
            
            for idx in limb_inds:
                indices.append(idx + vertex_offset)
        
        # Generate legs
        for pos in leg_positions:
            limb_verts, limb_inds, limb_norms = self.generate_limb_cylinder(
                pos, [0, -leg_length, 0], limb_radius
            )
            
            vertex_offset = len(vertices) // 3
            vertices.extend(limb_verts)
            normals.extend(limb_norms)
            
            for idx in limb_inds:
                indices.append(idx + vertex_offset)
        
        return vertices, indices, normals
    
    def generate_limb_cylinder(self, start_pos, direction, radius):
        """Generate a cylindrical limb"""
        vertices = []
        indices = []
        normals = []
        
        sectors = 8
        end_pos = [start_pos[i] + direction[i] for i in range(3)]
        
        for i in range(sectors + 1):
            angle = 2 * math.pi * i / sectors
            x_offset = radius * math.cos(angle)
            z_offset = radius * math.sin(angle)
            
            # Start vertex
            vertices.extend([start_pos[0] + x_offset, start_pos[1], start_pos[2] + z_offset])
            normals.extend([x_offset/radius, 0, z_offset/radius])
            
            # End vertex
            vertices.extend([end_pos[0] + x_offset, end_pos[1], end_pos[2] + z_offset])
            normals.extend([x_offset/radius, 0, z_offset/radius])
        
        # Generate indices
        for i in range(sectors):
            base = i * 2
            next_base = ((i + 1) % (sectors + 1)) * 2
            
            indices.extend([base, base + 1, next_base])
            indices.extend([base + 1, next_base + 1, next_base])
        
        return vertices, indices, normals
    
    def combine_meshes(self, mesh_list):
        """Combine multiple meshes into one"""
        combined_vertices = []
        combined_indices = []
        combined_normals = []
        
        vertex_offset = 0
        
        for vertices, indices, normals in mesh_list:
            # Add vertices and normals
            combined_vertices.extend(vertices)
            combined_normals.extend(normals)
            
            # Add indices with offset
            for idx in indices:
                combined_indices.append(idx + vertex_offset)
            
            vertex_offset += len(vertices) // 3
        
        return combined_vertices, combined_indices, combined_normals
    
    def smooth_mesh(self, vertices, indices, normals):
        """Apply mesh smoothing if advanced libraries are available"""
        try:
            if self.trimesh_available:
                return self.smooth_with_trimesh(vertices, indices, normals)
            elif self.pymeshlab_available:
                return self.smooth_with_pymeshlab(vertices, indices, normals)
            else:
                # Basic smoothing
                return self.basic_smooth(vertices, indices, normals)
        except Exception as e:
            print(f"Mesh smoothing error: {e}")
            return vertices, indices, normals
    
    def basic_smooth(self, vertices, indices, normals):
        """Basic mesh smoothing using averaging"""
        try:
            # Simple Laplacian smoothing
            vertex_count = len(vertices) // 3
            smoothed_vertices = vertices.copy()
            
            # Build adjacency list
            adjacency = [[] for _ in range(vertex_count)]
            
            for i in range(0, len(indices), 3):
                v1, v2, v3 = indices[i], indices[i+1], indices[i+2]
                adjacency[v1].extend([v2, v3])
                adjacency[v2].extend([v1, v3])
                adjacency[v3].extend([v1, v2])
            
            # Apply smoothing
            for v in range(vertex_count):
                if adjacency[v]:
                    neighbors = list(set(adjacency[v]))  # Remove duplicates
                    
                    avg_x = sum(vertices[n*3] for n in neighbors) / len(neighbors)
                    avg_y = sum(vertices[n*3+1] for n in neighbors) / len(neighbors)
                    avg_z = sum(vertices[n*3+2] for n in neighbors) / len(neighbors)
                    
                    # Blend with original position
                    blend_factor = 0.3
                    smoothed_vertices[v*3] = vertices[v*3] * (1-blend_factor) + avg_x * blend_factor
                    smoothed_vertices[v*3+1] = vertices[v*3+1] * (1-blend_factor) + avg_y * blend_factor
                    smoothed_vertices[v*3+2] = vertices[v*3+2] * (1-blend_factor) + avg_z * blend_factor
            
            # Recalculate normals
            smoothed_normals = self.recalculate_normals(smoothed_vertices, indices)
            
            return smoothed_vertices, indices, smoothed_normals
            
        except Exception as e:
            print(f"Basic smoothing error: {e}")
            return vertices, indices, normals
    
    def recalculate_normals(self, vertices, indices):
        """Recalculate vertex normals"""
        vertex_count = len(vertices) // 3
        normals = [0.0] * len(vertices)
        
        # Calculate face normals and accumulate
        for i in range(0, len(indices), 3):
            v1_idx, v2_idx, v3_idx = indices[i], indices[i+1], indices[i+2]
            
            # Get vertices
            v1 = [vertices[v1_idx*3], vertices[v1_idx*3+1], vertices[v1_idx*3+2]]
            v2 = [vertices[v2_idx*3], vertices[v2_idx*3+1], vertices[v2_idx*3+2]]
            v3 = [vertices[v3_idx*3], vertices[v3_idx*3+1], vertices[v3_idx*3+2]]
            
            # Calculate face normal
            edge1 = [v2[j] - v1[j] for j in range(3)]
            edge2 = [v3[j] - v1[j] for j in range(3)]
            
            # Cross product
            normal = [
                edge1[1]*edge2[2] - edge1[2]*edge2[1],
                edge1[2]*edge2[0] - edge1[0]*edge2[2],
                edge1[0]*edge2[1] - edge1[1]*edge2[0]
            ]
            
            # Normalize
            length = math.sqrt(sum(n*n for n in normal))
            if length > 0:
                normal = [n/length for n in normal]
            
            # Add to vertex normals
            for v_idx in [v1_idx, v2_idx, v3_idx]:
                normals[v_idx*3] += normal[0]
                normals[v_idx*3+1] += normal[1]
                normals[v_idx*3+2] += normal[2]
        
        # Normalize vertex normals
        for v in range(vertex_count):
            length = math.sqrt(
                normals[v*3]**2 + normals[v*3+1]**2 + normals[v*3+2]**2
            )
            if length > 0:
                normals[v*3] /= length
                normals[v*3+1] /= length
                normals[v*3+2] /= length
        
        return normals
    
    def optimize_mesh(self, vertices, indices, normals):
        """Optimize mesh for better performance"""
        # Remove duplicate vertices (simplified)
        # In a full implementation, this would be more sophisticated
        return vertices, indices, normals
    
    def generate_basic_head(self, proportions):
        """Fallback basic head generation"""
        head_center = [0, 1.6, 0]
        base_radius = 0.15
        
        vertices = []
        indices = []
        normals = []
        
        # Simple sphere
        rings = 12
        sectors = 16
        
        for r in range(rings + 1):
            lat = math.pi * (-0.5 + r / rings)
            y = math.sin(lat)
            xz = math.cos(lat)
            
            for s in range(sectors + 1):
                lon = 2 * math.pi * s / sectors
                x = xz * math.cos(lon)
                z = xz * math.sin(lon)
                
                vertices.extend([
                    head_center[0] + base_radius * x,
                    head_center[1] + base_radius * y,
                    head_center[2] + base_radius * z
                ])
                normals.extend([x, y, z])
        
        # Generate indices
        for r in range(rings):
            for s in range(sectors):
                current = r * (sectors + 1) + s
                next_ring = (r + 1) * (sectors + 1) + s
                
                indices.extend([current, next_ring, current + 1])
                indices.extend([current + 1, next_ring, next_ring + 1])
        
        return vertices, indices, normals
    
    def generate_basic_body(self, proportions):
        """Fallback basic body generation"""
        # Simple cylinder body
        vertices = []
        indices = []
        normals = []
        
        body_center = [0, 0.8, 0]
        body_radius = 0.2
        body_height = 0.8
        sectors = 12
        
        for i in range(sectors + 1):
            angle = 2 * math.pi * i / sectors
            x = body_radius * math.cos(angle)
            z = body_radius * math.sin(angle)
            
            vertices.extend([body_center[0] + x, body_center[1] - body_height/2, body_center[2] + z])
            vertices.extend([body_center[0] + x, body_center[1] + body_height/2, body_center[2] + z])
            
            normals.extend([x/body_radius, 0, z/body_radius])
            normals.extend([x/body_radius, 0, z/body_radius])
        
        for i in range(sectors):
            base = i * 2
            next_base = ((i + 1) % (sectors + 1)) * 2
            
            indices.extend([base, base + 1, next_base])
            indices.extend([base + 1, next_base + 1, next_base])
        
        return vertices, indices, normals
    
    def generate_fallback_mesh(self, character_params):
        """Generate fallback mesh when professional generation fails"""
        print("⚠️ Using fallback mesh generation")
        
        proportions = character_params.get('proportions', {})
        
        head_vertices, head_indices, head_normals = self.generate_basic_head(proportions)
        body_vertices, body_indices, body_normals = self.generate_basic_body(proportions)
        
        vertices, indices, normals = self.combine_meshes([
            (head_vertices, head_indices, head_normals),
            (body_vertices, body_indices, body_normals)
        ])
        
        return vertices, indices, normals
