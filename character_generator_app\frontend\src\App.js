import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import io from 'socket.io-client';

// Import components
import CharacterGenerator from './components/CharacterGenerator';
import CharacterViewer from './components/CharacterViewer';
import JobsList from './components/JobsList';
import Header from './components/Header';

// Initialize socket connection
const socket = io('http://localhost:5000');

function App() {
  const [jobs, setJobs] = useState([]);
  const [currentJob, setCurrentJob] = useState(null);

  useEffect(() => {
    // Socket event listeners
    socket.on('connect', () => {
      console.log('Connected to server');
      toast.success('Connected to 3D Character Generator');
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from server');
      toast.warning('Disconnected from server');
    });

    socket.on('job_update', (jobData) => {
      console.log('Job update:', jobData);
      
      // Update jobs list
      setJobs(prevJobs => {
        const existingJobIndex = prevJobs.findIndex(job => job.id === jobData.id);
        if (existingJobIndex >= 0) {
          const updatedJobs = [...prevJobs];
          updatedJobs[existingJobIndex] = jobData;
          return updatedJobs;
        } else {
          return [...prevJobs, jobData];
        }
      });

      // Update current job if it matches
      if (currentJob && currentJob.id === jobData.id) {
        setCurrentJob(jobData);
      }

      // Show toast notifications for status changes
      if (jobData.status === 'completed') {
        toast.success(`Character generation completed for job ${jobData.id.slice(0, 8)}`);
      } else if (jobData.status === 'error') {
        toast.error(`Error in job ${jobData.id.slice(0, 8)}: ${jobData.error}`);
      }
    });

    // Cleanup
    return () => {
      socket.off('connect');
      socket.off('disconnect');
      socket.off('job_update');
    };
  }, [currentJob]);

  const handleJobCreated = (jobData) => {
    setJobs(prevJobs => [...prevJobs, jobData]);
    setCurrentJob(jobData);
    
    // Join the job room for updates
    socket.emit('join_job', { job_id: jobData.job_id });
    
    toast.info(`Character generation started: ${jobData.job_id.slice(0, 8)}`);
  };

  const handleJobSelected = (job) => {
    setCurrentJob(job);
  };

  return (
    <Router>
      <div className="App">
        <Header />
        
        <div className="container">
          <Routes>
            <Route 
              path="/" 
              element={
                <CharacterGenerator 
                  onJobCreated={handleJobCreated}
                  currentJob={currentJob}
                />
              } 
            />
            <Route 
              path="/viewer/:jobId?" 
              element={
                <CharacterViewer 
                  job={currentJob}
                />
              } 
            />
            <Route 
              path="/jobs" 
              element={
                <JobsList 
                  jobs={jobs}
                  onJobSelected={handleJobSelected}
                />
              } 
            />
          </Routes>
        </div>

        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
      </div>
    </Router>
  );
}

export default App;
