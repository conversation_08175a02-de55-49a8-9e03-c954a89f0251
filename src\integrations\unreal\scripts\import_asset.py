
import unreal
import sys
import os
import json

# Get arguments
args = sys.argv
if len(args) > 1:
    params_json = args[1]
    params = json.loads(params_json)
else:
    params = {}

asset_path = params.get('asset_path', '')
destination_path = params.get('destination_path', '/Game/ImportedAssets')
asset_type = params.get('asset_type', 'StaticMesh')

# Import the asset
if asset_path and os.path.exists(asset_path):
    file_ext = os.path.splitext(asset_path)[1].lower()
    
    # Create import task
    import_task = unreal.AssetImportTask()
    import_task.filename = asset_path
    import_task.destination_path = destination_path
    import_task.replace_existing = True
    import_task.automated = True
    
    # Set import options based on asset type
    if asset_type == 'StaticMesh' and (file_ext == '.fbx' or file_ext == '.obj' or file_ext == '.glb' or file_ext == '.gltf'):
        import_task.options = unreal.FbxImportUI()
        import_task.options.static_mesh_import_data.combine_meshes = True
        import_task.options.static_mesh_import_data.generate_lightmap_u_vs = True
        import_task.options.static_mesh_import_data.auto_generate_collision = True
    elif asset_type == 'SkeletalMesh' and file_ext == '.fbx':
        import_task.options = unreal.FbxImportUI()
        import_task.options.skeletal_mesh_import_data.update_skeleton_reference_pose = True
        import_task.options.skeletal_mesh_import_data.use_t0_as_ref_pose = True
    elif asset_type == 'Animation' and file_ext == '.fbx':
        import_task.options = unreal.FbxImportUI()
        import_task.options.mesh_type_to_import = unreal.FBXImportType.FBXIT_ANIMATION
    elif asset_type == 'Texture' and (file_ext == '.png' or file_ext == '.jpg' or file_ext == '.jpeg' or file_ext == '.tga'):
        # Texture import doesn't need special options
        pass
    
    # Execute the import task
    unreal.AssetToolsHelpers.get_asset_tools().import_asset_tasks([import_task])
    
    # Log the imported assets
    imported_assets = []
    for path in import_task.imported_object_paths:
        imported_assets.append(path)
        print(f"Imported: {path}")
    
    # Save the imported assets
    unreal.EditorAssetLibrary.save_loaded_assets(imported_assets)
    
    print(f"Asset import complete: {asset_path}")
else:
    print(f"Asset not found or path not provided: {asset_path}")
