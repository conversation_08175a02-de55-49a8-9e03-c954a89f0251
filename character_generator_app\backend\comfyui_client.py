"""
ComfyUI API Client for 3D Character Generation
Handles communication with ComfyUI for AI-powered 3D generation
"""

import json
import uuid
import urllib.request
import urllib.parse
import websocket
import threading
import time
import requests
from typing import Dict, Any, Optional, Callable

class ComfyUIClient:
    """Client for communicating with ComfyUI API."""
    
    def __init__(self, server_address="127.0.0.1:8188"):
        self.server_address = server_address
        self.client_id = str(uuid.uuid4())
        self.ws = None
        self.ws_thread = None
        self.callbacks = {}
        
    def connect(self):
        """Connect to ComfyUI WebSocket."""
        try:
            ws_url = f"ws://{self.server_address}/ws?clientId={self.client_id}"
            self.ws = websocket.WebSocketApp(
                ws_url,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_open=self._on_open
            )
            
            self.ws_thread = threading.Thread(target=self.ws.run_forever)
            self.ws_thread.daemon = True
            self.ws_thread.start()
            
            # Wait for connection
            time.sleep(2)
            return True
            
        except Exception as e:
            print(f"Failed to connect to ComfyUI: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from ComfyUI."""
        if self.ws:
            self.ws.close()
        if self.ws_thread:
            self.ws_thread.join(timeout=5)
    
    def _on_message(self, ws, message):
        """Handle WebSocket messages."""
        try:
            data = json.loads(message)
            msg_type = data.get('type')
            
            if msg_type == 'executing':
                node_id = data.get('data', {}).get('node')
                prompt_id = data.get('data', {}).get('prompt_id')
                
                if prompt_id in self.callbacks:
                    callback = self.callbacks[prompt_id]
                    if node_id is None:
                        # Execution finished
                        callback('completed', {'prompt_id': prompt_id})
                    else:
                        # Node executing
                        callback('executing', {'node_id': node_id, 'prompt_id': prompt_id})
            
            elif msg_type == 'progress':
                prompt_id = data.get('data', {}).get('prompt_id')
                if prompt_id in self.callbacks:
                    callback = self.callbacks[prompt_id]
                    callback('progress', data.get('data', {}))
                    
        except Exception as e:
            print(f"Error processing WebSocket message: {e}")
    
    def _on_error(self, ws, error):
        """Handle WebSocket errors."""
        print(f"ComfyUI WebSocket error: {error}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close."""
        print("ComfyUI WebSocket connection closed")
    
    def _on_open(self, ws):
        """Handle WebSocket open."""
        print("Connected to ComfyUI WebSocket")
    
    def queue_prompt(self, prompt: Dict[str, Any], callback: Optional[Callable] = None) -> str:
        """Queue a prompt for execution."""
        try:
            prompt_id = str(uuid.uuid4())
            
            if callback:
                self.callbacks[prompt_id] = callback
            
            data = {
                "prompt": prompt,
                "client_id": self.client_id,
                "extra_data": {"extra_pnginfo": {"workflow": prompt}}
            }
            
            response = requests.post(
                f"http://{self.server_address}/prompt",
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                actual_prompt_id = result.get('prompt_id', prompt_id)
                
                # Update callback mapping with actual prompt ID
                if callback and prompt_id != actual_prompt_id:
                    self.callbacks[actual_prompt_id] = self.callbacks.pop(prompt_id)
                
                return actual_prompt_id
            else:
                print(f"Failed to queue prompt: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Error queuing prompt: {e}")
            return None
    
    def get_queue(self):
        """Get current queue status."""
        try:
            response = requests.get(f"http://{self.server_address}/queue")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting queue: {e}")
            return None
    
    def get_history(self, prompt_id: str):
        """Get execution history for a prompt."""
        try:
            response = requests.get(f"http://{self.server_address}/history/{prompt_id}")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting history: {e}")
            return None
    
    def get_image(self, filename: str, subfolder: str = "", folder_type: str = "output"):
        """Download an image from ComfyUI."""
        try:
            url = f"http://{self.server_address}/view"
            params = {
                "filename": filename,
                "subfolder": subfolder,
                "type": folder_type
            }
            
            response = requests.get(url, params=params)
            if response.status_code == 200:
                return response.content
            return None
        except Exception as e:
            print(f"Error getting image: {e}")
            return None
    
    def upload_image(self, image_path: str, overwrite: bool = True):
        """Upload an image to ComfyUI."""
        try:
            url = f"http://{self.server_address}/upload/image"
            
            with open(image_path, 'rb') as f:
                files = {'image': f}
                data = {'overwrite': str(overwrite).lower()}
                
                response = requests.post(url, files=files, data=data)
                
            if response.status_code == 200:
                result = response.json()
                return result.get('name')  # Returns the filename in ComfyUI
            return None
        except Exception as e:
            print(f"Error uploading image: {e}")
            return None
    
    def interrupt(self):
        """Interrupt current execution."""
        try:
            response = requests.post(f"http://{self.server_address}/interrupt")
            return response.status_code == 200
        except Exception as e:
            print(f"Error interrupting: {e}")
            return False
    
    def get_system_stats(self):
        """Get system statistics."""
        try:
            response = requests.get(f"http://{self.server_address}/system_stats")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting system stats: {e}")
            return None

def test_comfyui_connection():
    """Test connection to ComfyUI."""
    client = ComfyUIClient()
    
    try:
        # Test basic HTTP connection
        response = requests.get("http://127.0.0.1:8188/system_stats", timeout=5)
        if response.status_code == 200:
            print("✅ ComfyUI HTTP API is accessible")
            
            # Test WebSocket connection
            if client.connect():
                print("✅ ComfyUI WebSocket connection successful")
                client.disconnect()
                return True
            else:
                print("❌ ComfyUI WebSocket connection failed")
                return False
        else:
            print("❌ ComfyUI HTTP API not accessible")
            return False
            
    except Exception as e:
        print(f"❌ ComfyUI connection test failed: {e}")
        return False

if __name__ == "__main__":
    # Test the connection
    test_comfyui_connection()
