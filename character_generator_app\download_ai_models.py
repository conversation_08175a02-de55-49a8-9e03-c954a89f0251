#!/usr/bin/env python3
"""
AI Model Downloader for 3D Character Generator
Downloads all necessary AI models for standalone operation
"""

import os
import sys
import requests
import hashlib
from pathlib import Path
from tqdm import tqdm
import json

def create_directories():
    """Create necessary model directories"""
    dirs = [
        "models/checkpoints",
        "models/controlnet", 
        "models/insightface",
        "models/comfyui/clip_vision",
        "models/comfyui/vae",
        "models/embeddings",
        "models/loras",
        "comfyui/models/checkpoints",
        "comfyui/models/controlnet",
        "comfyui/models/clip_vision",
        "comfyui/models/vae"
    ]
    
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {dir_path}")

def download_file_with_progress(url, filepath, expected_size=None):
    """Download file with progress bar"""
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    if expected_size and total_size != expected_size:
        print(f"Warning: Expected size {expected_size}, got {total_size}")
    
    with open(filepath, 'wb') as file, tqdm(
        desc=os.path.basename(filepath),
        total=total_size,
        unit='B',
        unit_scale=True,
        unit_divisor=1024,
    ) as pbar:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                file.write(chunk)
                pbar.update(len(chunk))

def download_essential_models():
    """Download essential models for basic functionality"""
    print("\n📦 Downloading essential models...")
    
    essential_downloads = [
        {
            "name": "Stable Diffusion 1.5",
            "url": "https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.ckpt",
            "path": "models/checkpoints/v1-5-pruned-emaonly.ckpt",
            "size": 4265380512  # ~4GB
        },
        {
            "name": "VAE Model",
            "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors",
            "path": "models/comfyui/vae/vae-ft-mse-840000-ema-pruned.safetensors",
            "size": 334695179
        }
    ]
    
    for download in essential_downloads:
        filepath = Path(download["path"])
        
        if filepath.exists():
            print(f"✓ {download['name']} already exists")
            continue
            
        print(f"Downloading {download['name']}...")
        try:
            filepath.parent.mkdir(parents=True, exist_ok=True)
            download_file_with_progress(
                download["url"], 
                filepath, 
                download.get("size")
            )
            print(f"✓ Downloaded {download['name']}")
        except Exception as e:
            print(f"❌ Failed to download {download['name']}: {e}")

def setup_comfyui_symlinks():
    """Create symlinks for ComfyUI model access"""
    print("\n🔗 Setting up ComfyUI model symlinks...")
    
    symlinks = [
        ("models/checkpoints", "comfyui/models/checkpoints"),
        ("models/controlnet", "comfyui/models/controlnet"),
        ("models/comfyui/vae", "comfyui/models/vae"),
        ("models/comfyui/clip_vision", "comfyui/models/clip_vision")
    ]
    
    for source, target in symlinks:
        source_path = Path(source)
        target_path = Path(target)
        
        if source_path.exists() and not target_path.exists():
            try:
                target_path.parent.mkdir(parents=True, exist_ok=True)
                if os.name == 'nt':  # Windows
                    os.system(f'mklink /D "{target_path}" "{source_path.absolute()}"')
                else:  # Unix/Linux/Mac
                    target_path.symlink_to(source_path.absolute())
                print(f"✓ Created symlink: {target} -> {source}")
            except Exception as e:
                print(f"❌ Failed to create symlink {target}: {e}")

def create_model_manifest():
    """Create a manifest of downloaded models"""
    print("\n📋 Creating model manifest...")
    
    manifest = {
        "version": "1.0.0",
        "models": {},
        "total_size": 0
    }
    
    for root, dirs, files in os.walk("models"):
        for file in files:
            filepath = Path(root) / file
            if filepath.suffix in ['.ckpt', '.safetensors', '.pth', '.bin']:
                size = filepath.stat().st_size
                manifest["models"][str(filepath)] = {
                    "size": size,
                    "hash": hashlib.sha256(filepath.read_bytes()).hexdigest()[:16]
                }
                manifest["total_size"] += size
    
    with open("models/manifest.json", "w") as f:
        json.dump(manifest, f, indent=2)
    
    print(f"✓ Model manifest created with {len(manifest['models'])} models")
    print(f"✓ Total size: {manifest['total_size'] / (1024**3):.2f} GB")

def main():
    """Main download function"""
    print("🚀 AI-Powered 3D Character Generator")
    print("📥 Model Download Script")
    print("=" * 50)
    
    # Check available disk space
    import shutil
    free_space = shutil.disk_usage(".").free
    required_space = 10 * 1024**3  # 10GB minimum
    
    if free_space < required_space:
        print(f"❌ Insufficient disk space. Need at least 10GB, have {free_space/(1024**3):.2f}GB")
        sys.exit(1)
    
    print(f"✓ Available disk space: {free_space/(1024**3):.2f}GB")
    
    try:
        # Step 1: Create directories
        create_directories()
        
        # Step 2: Download essential models
        download_essential_models()
        
        # Step 3: Setup symlinks
        setup_comfyui_symlinks()
        
        # Step 4: Create manifest
        create_model_manifest()
        
        print("\n🎉 Model download completed successfully!")
        print("✓ All essential models are ready")
        print("✓ ComfyUI integration configured")
        print("✓ You can now run the application")
        
    except KeyboardInterrupt:
        print("\n❌ Download interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Download failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
