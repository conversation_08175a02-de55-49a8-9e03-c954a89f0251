"""
Pipeline for converting text prompts to 3D models.
"""

import os
import tempfile
import json
import time

# Import integration modules
from integrations.huggingface.text_to_3d import HuggingFaceTextTo3D
from integrations.comfyui.api import ComfyUIAPI
from integrations.blender.automation import BlenderAutomation

class TextTo3DPipeline:
    """
    Pipeline for converting text prompts to 3D models.
    
    This pipeline uses:
    1. HuggingFace models for initial 3D mesh generation from text
    2. ComfyUI for texture generation and refinement
    3. Blender for post-processing and optimization
    """
    
    def __init__(self):
        """Initialize the text to 3D pipeline."""
        self.huggingface = HuggingFaceTextTo3D()
        self.comfyui = ComfyUIAPI()
        self.blender = BlenderAutomation()
        
        # Create output directory if it doesn't exist
        os.makedirs('output/3d_models', exist_ok=True)
    
    def process(self, input_data, parameters):
        """
        Process a text prompt to generate a 3D model.
        
        Args:
            input_data: Text prompt
            parameters: Additional parameters for the generation
                - model: Model to use for 3D generation (default: 'shap-e')
                - texture_quality: Quality of textures (default: 'medium')
                - optimize_mesh: Whether to optimize the mesh (default: True)
                
        Returns:
            dict: Result containing paths to generated files
        """
        try:
            # Get parameters
            model = parameters.get('model', 'shap-e')
            texture_quality = parameters.get('texture_quality', 'medium')
            optimize_mesh = parameters.get('optimize_mesh', True)
            
            # Step 1: Generate initial 3D mesh using HuggingFace
            print(f"Generating 3D mesh from text using {model}...")
            mesh_path = self.huggingface.generate_mesh_from_text(
                text_prompt=input_data,
                model=model
            )
            
            # Step 2: Generate a reference image using ComfyUI
            print("Generating reference image from text...")
            reference_image_path = self.comfyui.generate_image(
                prompt=input_data,
                negative_prompt="low quality, bad anatomy, worst quality, low resolution",
                width=512,
                height=512,
                steps=30
            )
            
            # Step 3: Generate textures using ComfyUI
            print(f"Generating textures with quality {texture_quality}...")
            texture_paths = self.comfyui.generate_textures(
                mesh_path=mesh_path,
                reference_image_path=reference_image_path,
                quality=texture_quality
            )
            
            # Step 4: Post-process in Blender
            print("Post-processing in Blender...")
            output_path = os.path.join('output/3d_models', f"text_to_3d_{int(time.time())}.glb")
            final_model_path = self.blender.process_model(
                mesh_path=mesh_path,
                texture_paths=texture_paths,
                output_path=output_path,
                optimize=optimize_mesh
            )
            
            # Return results
            return {
                'model_path': final_model_path,
                'texture_paths': texture_paths,
                'preview_image': self.blender.render_preview(final_model_path)
            }
            
        except Exception as e:
            print(f"Error in text to 3D pipeline: {str(e)}")
            raise
