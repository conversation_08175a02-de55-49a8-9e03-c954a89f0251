import React, { useState, useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment, PerspectiveCamera } from '@react-three/drei';
import axios from 'axios';
import { toast } from 'react-toastify';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import * as THREE from 'three';

const ViewerContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled.h1`
  text-align: center;
  color: white;
  margin-bottom: 2rem;
  font-size: 2.5rem;
  font-weight: 700;
`;

const ViewerCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const CanvasContainer = styled.div`
  width: 100%;
  height: 500px;
  border-radius: 10px;
  overflow: hidden;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  margin-bottom: 2rem;
`;

const ControlsPanel = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
`;

const ControlGroup = styled.div`
  background: rgba(102, 126, 234, 0.1);
  padding: 1rem;
  border-radius: 8px;
`;

const ControlLabel = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
`;

const Slider = styled.input`
  width: 100%;
  margin: 0.5rem 0;
`;

const AnimationButton = styled.button`
  background: ${props => props.active ? 'linear-gradient(45deg, #667eea, #764ba2)' : '#f8f9fa'};
  color: ${props => props.active ? 'white' : '#333'};
  border: 2px solid ${props => props.active ? 'transparent' : '#e0e0e0'};
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin: 0.25rem;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
`;

const DownloadSection = styled.div`
  background: rgba(40, 167, 69, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border: 2px solid rgba(40, 167, 69, 0.2);
`;

const DownloadButton = styled.button`
  background: linear-gradient(45deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 0.25rem;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

// 3D Character Component
const Character3D = ({ position, rotation, scale, animation }) => {
  const meshRef = useRef();
  
  useFrame((state) => {
    if (meshRef.current) {
      // Simple rotation animation
      if (animation === 'rotate') {
        meshRef.current.rotation.y += 0.01;
      } else if (animation === 'bounce') {
        meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.2;
      } else if (animation === 'idle') {
        meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      }
    }
  });

  return (
    <mesh ref={meshRef} position={position} rotation={rotation} scale={scale}>
      {/* Simple character representation - in real app this would load the actual GLB model */}
      <group>
        {/* Body */}
        <mesh position={[0, 0, 0]}>
          <cylinderGeometry args={[0.3, 0.4, 1.2, 8]} />
          <meshStandardMaterial color="#4a90e2" />
        </mesh>
        
        {/* Head */}
        <mesh position={[0, 0.8, 0]}>
          <sphereGeometry args={[0.25, 16, 16]} />
          <meshStandardMaterial color="#f4c2a1" />
        </mesh>
        
        {/* Arms */}
        <mesh position={[-0.5, 0.2, 0]}>
          <cylinderGeometry args={[0.08, 0.08, 0.8, 8]} />
          <meshStandardMaterial color="#4a90e2" />
        </mesh>
        <mesh position={[0.5, 0.2, 0]}>
          <cylinderGeometry args={[0.08, 0.08, 0.8, 8]} />
          <meshStandardMaterial color="#4a90e2" />
        </mesh>
        
        {/* Legs */}
        <mesh position={[-0.15, -0.8, 0]}>
          <cylinderGeometry args={[0.08, 0.08, 0.8, 8]} />
          <meshStandardMaterial color="#2c3e50" />
        </mesh>
        <mesh position={[0.15, -0.8, 0]}>
          <cylinderGeometry args={[0.08, 0.08, 0.8, 8]} />
          <meshStandardMaterial color="#2c3e50" />
        </mesh>
      </group>
    </mesh>
  );
};

const CharacterViewer = ({ job }) => {
  const [characterSettings, setCharacterSettings] = useState({
    position: [0, 0, 0],
    rotation: [0, 0, 0],
    scale: [1, 1, 1]
  });
  
  const [currentAnimation, setCurrentAnimation] = useState('idle');
  const [isDownloading, setIsDownloading] = useState(false);

  const handleSettingChange = (setting, axis, value) => {
    setCharacterSettings(prev => ({
      ...prev,
      [setting]: prev[setting].map((val, index) => 
        index === axis ? parseFloat(value) : val
      )
    }));
  };

  const handleDownload = async (fileType) => {
    if (!job || job.status !== 'completed') {
      toast.error('No completed character to download');
      return;
    }

    setIsDownloading(true);
    
    try {
      const response = await axios.get(`/api/download/${job.id || job.job_id}/${fileType}`, {
        responseType: 'blob'
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      
      // Set filename based on file type
      let filename = `character_${(job.id || job.job_id).slice(0, 8)}`;
      switch (fileType) {
        case 'model':
          filename += '.glb';
          break;
        case 'texture':
          filename += '_texture.png';
          break;
        case 'animation':
          filename += '_idle.fbx';
          break;
        default:
          filename += '.file';
      }
      
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success(`${fileType} downloaded successfully!`);
    } catch (error) {
      console.error('Download error:', error);
      toast.error(`Failed to download ${fileType}`);
    } finally {
      setIsDownloading(false);
    }
  };

  if (!job) {
    return (
      <ViewerContainer>
        <Title>3D Character Viewer</Title>
        <ViewerCard
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div style={{ textAlign: 'center', padding: '3rem' }}>
            <h3>No Character Selected</h3>
            <p>Generate a character first or select one from your jobs list.</p>
            <a href="/" className="btn">
              Generate Character
            </a>
          </div>
        </ViewerCard>
      </ViewerContainer>
    );
  }

  if (job.status !== 'completed') {
    return (
      <ViewerContainer>
        <Title>3D Character Viewer</Title>
        <ViewerCard
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div style={{ textAlign: 'center', padding: '3rem' }}>
            <h3>Character Not Ready</h3>
            <p>Your character is still being generated. Current status: <strong>{job.status}</strong></p>
            {job.progress && (
              <div>
                <div className="progress-bar">
                  <div className="progress-fill" style={{ width: `${job.progress}%` }}></div>
                </div>
                <p>{job.progress}% complete</p>
              </div>
            )}
          </div>
        </ViewerCard>
      </ViewerContainer>
    );
  }

  return (
    <ViewerContainer>
      <Title>3D Character Viewer</Title>
      
      <ViewerCard
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h3>Character Preview</h3>
        <p>Job ID: {(job.id || job.job_id).slice(0, 8)}</p>
        
        <CanvasContainer>
          <Canvas>
            <PerspectiveCamera makeDefault position={[3, 2, 3]} />
            <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
            
            {/* Lighting */}
            <ambientLight intensity={0.4} />
            <directionalLight position={[10, 10, 5]} intensity={1} />
            <pointLight position={[-10, -10, -5]} intensity={0.5} />
            
            {/* Environment */}
            <Environment preset="studio" />
            
            {/* Character */}
            <Character3D 
              position={characterSettings.position}
              rotation={characterSettings.rotation}
              scale={characterSettings.scale}
              animation={currentAnimation}
            />
            
            {/* Ground plane */}
            <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1.5, 0]}>
              <planeGeometry args={[10, 10]} />
              <meshStandardMaterial color="#e0e0e0" />
            </mesh>
          </Canvas>
        </CanvasContainer>
        
        <ControlsPanel>
          <ControlGroup>
            <ControlLabel>Position</ControlLabel>
            <div>
              <label>X: </label>
              <Slider
                type="range"
                min="-3"
                max="3"
                step="0.1"
                value={characterSettings.position[0]}
                onChange={(e) => handleSettingChange('position', 0, e.target.value)}
              />
            </div>
            <div>
              <label>Y: </label>
              <Slider
                type="range"
                min="-2"
                max="2"
                step="0.1"
                value={characterSettings.position[1]}
                onChange={(e) => handleSettingChange('position', 1, e.target.value)}
              />
            </div>
            <div>
              <label>Z: </label>
              <Slider
                type="range"
                min="-3"
                max="3"
                step="0.1"
                value={characterSettings.position[2]}
                onChange={(e) => handleSettingChange('position', 2, e.target.value)}
              />
            </div>
          </ControlGroup>
          
          <ControlGroup>
            <ControlLabel>Scale</ControlLabel>
            <div>
              <label>Size: </label>
              <Slider
                type="range"
                min="0.5"
                max="2"
                step="0.1"
                value={characterSettings.scale[0]}
                onChange={(e) => {
                  const value = e.target.value;
                  setCharacterSettings(prev => ({
                    ...prev,
                    scale: [value, value, value]
                  }));
                }}
              />
            </div>
          </ControlGroup>
          
          <ControlGroup>
            <ControlLabel>Animation</ControlLabel>
            <div>
              <AnimationButton
                active={currentAnimation === 'idle'}
                onClick={() => setCurrentAnimation('idle')}
              >
                Idle
              </AnimationButton>
              <AnimationButton
                active={currentAnimation === 'rotate'}
                onClick={() => setCurrentAnimation('rotate')}
              >
                Rotate
              </AnimationButton>
              <AnimationButton
                active={currentAnimation === 'bounce'}
                onClick={() => setCurrentAnimation('bounce')}
              >
                Bounce
              </AnimationButton>
              <AnimationButton
                active={currentAnimation === 'none'}
                onClick={() => setCurrentAnimation('none')}
              >
                None
              </AnimationButton>
            </div>
          </ControlGroup>
        </ControlsPanel>
        
        <DownloadSection>
          <h4>Download Character Files</h4>
          <p>Download your generated 3D character files for use in your projects:</p>
          
          <div>
            <DownloadButton
              onClick={() => handleDownload('model')}
              disabled={isDownloading}
            >
              {isDownloading ? 'Downloading...' : 'Download 3D Model (.glb)'}
            </DownloadButton>
            
            <DownloadButton
              onClick={() => handleDownload('texture')}
              disabled={isDownloading}
            >
              {isDownloading ? 'Downloading...' : 'Download Texture (.png)'}
            </DownloadButton>
            
            <DownloadButton
              onClick={() => handleDownload('animation')}
              disabled={isDownloading}
            >
              {isDownloading ? 'Downloading...' : 'Download Animation (.fbx)'}
            </DownloadButton>
          </div>
          
          <p style={{ fontSize: '0.9rem', color: '#666', marginTop: '1rem' }}>
            💡 Tip: The GLB file can be imported directly into Unity, Unreal Engine, or Blender.
          </p>
        </DownloadSection>
      </ViewerCard>
    </ViewerContainer>
  );
};

export default CharacterViewer;
