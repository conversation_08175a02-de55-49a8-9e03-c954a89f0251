#!/usr/bin/env python3
"""
Simple API server using Python's built-in HTTP server
Avoids Flask import issues
"""

import http.server
import socketserver
import json
import urllib.parse
import os
import uuid
from datetime import datetime

class CharacterAPIHandler(http.server.BaseHTTPRequestHandler):
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/':
            self.send_json_response({
                'message': '3D Character Generator API',
                'status': 'running',
                'version': '2.0.0',
                'endpoints': ['/api/test', '/api/status', '/api/generate']
            })
        
        elif self.path == '/api/test':
            self.send_json_response({
                'test': 'success',
                'backend': 'working',
                'timestamp': datetime.now().isoformat()
            })
        
        elif self.path == '/api/status':
            self.send_json_response({
                'server': 'running',
                'character_generation': 'available',
                'timestamp': datetime.now().isoformat()
            })
        
        elif self.path.startswith('/api/download/'):
            filename = self.path.split('/')[-1]
            self.serve_file(filename)
        
        else:
            self.send_json_response({
                'error': 'Endpoint not found',
                'path': self.path
            }, 404)
    
    def do_POST(self):
        """Handle POST requests"""
        if self.path == '/api/generate':
            self.handle_generate()
        else:
            self.send_json_response({
                'error': 'POST endpoint not found',
                'path': self.path
            }, 404)
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def handle_generate(self):
        """Handle character generation"""
        try:
            # Read request data
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            text_prompt = data.get('prompt', 'A 3D character')
            
            print(f"🎯 Generating character: {text_prompt}")
            
            # Try to use real character generation
            try:
                import sys
                sys.path.append('backend')
                from app import create_character_glb, analyze_character_prompt
                
                # Generate character
                character_type = analyze_character_prompt(text_prompt)
                glb_content = create_character_glb(text_prompt)
                
                # Save file
                job_id = str(uuid.uuid4())[:8]
                filename = f"api_character_{job_id}.glb"
                
                # Ensure directory exists
                os.makedirs('backend/outputs/models', exist_ok=True)
                filepath = os.path.join('backend', 'outputs', 'models', filename)
                
                with open(filepath, 'wb') as f:
                    f.write(glb_content)
                
                print(f"✅ Real character generated: {filename} ({len(glb_content)} bytes)")
                
                response = {
                    'success': True,
                    'job_id': job_id,
                    'filename': filename,
                    'character_type': character_type['name'],
                    'file_size': len(glb_content),
                    'download_url': f'/api/download/{filename}',
                    'method': 'real_generation',
                    'timestamp': datetime.now().isoformat()
                }
                
            except Exception as e:
                print(f"⚠️ Real generation failed, using simulation: {e}")
                
                # Simulation mode
                job_id = str(uuid.uuid4())[:8]
                filename = f"simulated_character_{job_id}.json"
                
                # Analyze character type (simple)
                character_type = 'humanoid'
                if 'robot' in text_prompt.lower():
                    character_type = 'robot'
                elif 'alien' in text_prompt.lower():
                    character_type = 'alien'
                elif 'warrior' in text_prompt.lower():
                    character_type = 'warrior'
                
                # Create simulation data
                sim_data = {
                    'prompt': text_prompt,
                    'character_type': character_type,
                    'generated_at': datetime.now().isoformat(),
                    'vertices': 417,
                    'faces': 560,
                    'materials': 1,
                    'file_format': 'GLB',
                    'note': 'This is a simulation. In the real system, a GLB file would be generated.'
                }
                
                # Save simulation file
                os.makedirs('backend/outputs/models', exist_ok=True)
                filepath = os.path.join('backend', 'outputs', 'models', filename)
                
                with open(filepath, 'w') as f:
                    json.dump(sim_data, f, indent=2)
                
                print(f"✅ Simulation generated: {filename}")
                
                response = {
                    'success': True,
                    'job_id': job_id,
                    'filename': filename,
                    'character_type': character_type,
                    'file_size': len(json.dumps(sim_data)),
                    'download_url': f'/api/download/{filename}',
                    'method': 'simulation',
                    'note': 'Simulation mode - real GLB generation available when backend is fully connected',
                    'timestamp': datetime.now().isoformat()
                }
            
            self.send_json_response(response)
            
        except Exception as e:
            print(f"❌ Generation error: {e}")
            self.send_json_response({
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }, 500)
    
    def serve_file(self, filename):
        """Serve a file for download"""
        try:
            filepath = os.path.join('backend', 'outputs', 'models', filename)
            
            if os.path.exists(filepath):
                self.send_response(200)
                self.send_header('Access-Control-Allow-Origin', '*')
                
                # Determine content type
                if filename.endswith('.glb'):
                    self.send_header('Content-Type', 'model/gltf-binary')
                elif filename.endswith('.json'):
                    self.send_header('Content-Type', 'application/json')
                else:
                    self.send_header('Content-Type', 'application/octet-stream')
                
                self.send_header('Content-Disposition', f'attachment; filename="{filename}"')
                self.end_headers()
                
                with open(filepath, 'rb') as f:
                    self.wfile.write(f.read())
            else:
                self.send_json_response({
                    'error': 'File not found',
                    'filename': filename
                }, 404)
                
        except Exception as e:
            self.send_json_response({
                'error': str(e),
                'filename': filename
            }, 500)
    
    def send_json_response(self, data, status_code=200):
        """Send a JSON response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response = json.dumps(data, indent=2)
        self.wfile.write(response.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Custom log format"""
        print(f"🌐 {self.address_string()} - {format % args}")

def start_server():
    """Start the API server"""
    PORT = 5000
    
    print("🚀 Starting 3D Character Generator API Server")
    print("=" * 45)
    print(f"📱 API: http://localhost:{PORT}")
    print(f"🧪 Test: http://localhost:{PORT}/api/test")
    print(f"🎯 Generate: POST http://localhost:{PORT}/api/generate")
    print("\n💡 This server works with your web interface!")
    print("⌨️ Press Ctrl+C to stop the server")
    print("=" * 45)
    
    try:
        with socketserver.TCPServer(("", PORT), CharacterAPIHandler) as httpd:
            print(f"✅ Server started on port {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")

if __name__ == "__main__":
    start_server()
