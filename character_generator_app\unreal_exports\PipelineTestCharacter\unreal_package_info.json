{"character_name": "PipelineTestCharacter", "export_path": "unreal_exports\\PipelineTestCharacter", "glb_file": "unreal_exports\\PipelineTestCharacter\\PipelineTestCharacter.glb", "materials": {"M_Character_Skin": {"material_domain": "Surface", "blend_mode": "Opaque", "shading_model": "DefaultLit", "parameters": {"BaseColor": [0.7843137254901961, 0.7058823529411765, 0.6274509803921569], "Metallic": 0.0, "Roughness": 0.6, "Subsurface": 0.3, "SubsurfaceColor": [0.9, 0.7, 0.6]}, "textures": {"BaseColorTexture": "T_PipelineTestCharacter_Diffuse", "NormalTexture": "T_PipelineTestCharacter_Normal", "RoughnessTexture": "T_PipelineTestCharacter_Roughness", "SubsurfaceTexture": "T_PipelineTestCharacter_Subsurface"}}, "M_Character_Clothing": {"material_domain": "Surface", "blend_mode": "Opaque", "shading_model": "DefaultLit", "parameters": {"BaseColor": [0.2, 0.4, 0.8], "Metallic": 0.1, "Roughness": 0.8}, "textures": {"BaseColorTexture": "T_PipelineTestCharacter_Clothing_Diffuse", "NormalTexture": "T_PipelineTestCharacter_Clothing_Normal"}}}, "blueprint_config": {"blueprint_name": "BP_PipelineTestCharacter", "parent_class": "Character", "components": [{"type": "SkeletalMeshComponent", "mesh": "SK_PipelineTestCharacter", "materials": ["M_Character_Skin", "M_Character_Clothing"]}, {"type": "CapsuleComponent", "height": 192.0, "radius": 34.0}, {"type": "CharacterMovementComponent", "max_walk_speed": 600.0, "jump_z_velocity": 420.0}], "animation_blueprint": "ABP_PipelineTestCharacter", "physics_asset": "PA_PipelineTestCharacter"}, "import_settings": {"mesh_import_settings": {"import_mesh": true, "import_textures": true, "import_materials": true, "import_animations": true, "skeletal_mesh": true, "create_physics_asset": true, "auto_generate_collision": true, "vertex_color_import_option": "Replace", "vertex_override_color": [1.0, 1.0, 1.0, 1.0]}, "animation_import_settings": {"import_bone_tracks": true, "import_custom_attribute": true, "delete_existing_morph_target_curves": false, "do_not_import_curve_with_zero": false, "preserve_local_transform": false}, "material_import_settings": {"base_material_name": "M_PipelineTestCharacter_Master", "texture_import_data": {"material_search_location": "Local", "base_color_name": "BaseColor", "normal_name": "Normal"}}}, "documentation": {"setup_instructions": ["1. Import PipelineTestCharacter.glb into your Unreal project", "2. Apply the provided import settings", "3. Create materials using the material definitions", "4. Set up the Blueprint using the blueprint configuration", "5. Configure the Animation Blueprint for character movement"], "optimization_notes": ["Character optimized for Unreal with 3000 target vertices", "Nanite virtualized geometry ready", "LOD levels generated for performance", "PBR materials with subsurface scattering"], "usage_tips": ["Use the provided Blueprint for consistent setup", "Enable Nanite for automatic LOD management", "Utilize the Character Movement Component for gameplay", "Test with Lumen global illumination"]}, "optimization_applied": true, "export_date": "2025-05-25T18:20:36.890281"}