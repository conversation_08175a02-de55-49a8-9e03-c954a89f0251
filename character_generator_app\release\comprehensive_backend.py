#!/usr/bin/env python3
"""
Comprehensive Backend Server for 3D Character Generator
Integrates ComfyUI, AgenticSeek, HuggingFace, and other services
"""

import os
import sys
import json
import time
import asyncio
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CharacterRequest(BaseModel):
    prompt: str
    style: str = "realistic"
    quality: str = "medium"
    workflow_type: str = "standard"

class AgenticRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None

class ComprehensiveBackend:
    def __init__(self):
        # Paths first
        self.project_root = Path(__file__).parent
        self.uploads_dir = self.project_root / "uploads"
        self.outputs_dir = self.project_root / "outputs"
        self.agentic_seek_dir = self.project_root.parent / "agenticSeek"

        # Create directories
        self.uploads_dir.mkdir(exist_ok=True)
        self.outputs_dir.mkdir(exist_ok=True)

        # Service configurations
        self.services = {
            'comfyui': {
                'url': 'http://127.0.0.1:8188',
                'connected': False
            },
            'agentic_seek': {
                'url': 'http://127.0.0.1:8000',
                'connected': False
            },
            'huggingface': {
                'url': 'https://huggingface.co/api',
                'token': os.getenv('HF_TOKEN', '*************************************'),
                'connected': False
            }
        }

        # Store processes separately to avoid serialization issues
        self.processes = {}

        # Initialize FastAPI
        self.app = FastAPI(title="Comprehensive Character Generator Backend")
        self.setup_cors()
        self.setup_routes()
        self.setup_static_files()
        self.setup_startup_events()

        # Services will be initialized when the server starts

    def setup_cors(self):
        """Setup CORS middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    def setup_static_files(self):
        """Setup static file serving"""
        self.app.mount("/static", StaticFiles(directory=str(self.project_root)), name="static")
        self.app.mount("/outputs", StaticFiles(directory=str(self.outputs_dir)), name="outputs")

    def setup_startup_events(self):
        """Setup startup events"""
        @self.app.on_event("startup")
        async def startup_event():
            logger.info("Starting up comprehensive backend...")
            await self.initialize_services()

    def setup_routes(self):
        """Setup API routes"""

        @self.app.get("/")
        async def root():
            return {"message": "Comprehensive Character Generator Backend", "status": "running"}

        @self.app.get("/health")
        async def health_check():
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "services": self.services
            }

        @self.app.get("/services/status")
        async def get_services_status():
            """Get status of all integrated services"""
            await self.check_service_connections()
            # Return serializable version of services
            return {
                service_name: {
                    'url': service_config['url'],
                    'connected': service_config['connected']
                }
                for service_name, service_config in self.services.items()
            }

        @self.app.post("/services/start/{service_name}")
        async def start_service(service_name: str, background_tasks: BackgroundTasks):
            """Start a specific service"""
            if service_name not in self.services:
                raise HTTPException(status_code=404, detail="Service not found")

            background_tasks.add_task(self.start_service_process, service_name)
            return {"message": f"Starting {service_name}...", "status": "initiated"}

        @self.app.post("/generate/character")
        async def generate_character(request: CharacterRequest, background_tasks: BackgroundTasks):
            """Generate a 3D character"""
            try:
                # Start generation process in background
                task_id = f"char_{int(time.time())}"
                background_tasks.add_task(self.process_character_generation, task_id, request)

                return {
                    "task_id": task_id,
                    "status": "started",
                    "message": "Character generation initiated"
                }
            except Exception as e:
                logger.error(f"Character generation error: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/upload/image")
        async def upload_image(file: UploadFile = File(...)):
            """Upload reference image"""
            try:
                # Validate file type
                if not file.content_type.startswith('image/'):
                    raise HTTPException(status_code=400, detail="File must be an image")

                # Save file
                timestamp = int(time.time())
                filename = f"{timestamp}_{file.filename}"
                file_path = self.uploads_dir / filename

                with open(file_path, "wb") as buffer:
                    content = await file.read()
                    buffer.write(content)

                return {
                    "filename": filename,
                    "path": str(file_path),
                    "size": len(content),
                    "url": f"/static/uploads/{filename}"
                }
            except Exception as e:
                logger.error(f"Image upload error: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/agentic/chat")
        async def agentic_chat(request: AgenticRequest):
            """Chat with AgenticSeek"""
            try:
                if not self.services['agentic_seek']['connected']:
                    # Fallback response
                    response = self.generate_fallback_response(request.message)
                else:
                    # Call actual AgenticSeek API
                    response = await self.call_agentic_seek(request.message, request.context)

                return {
                    "response": response,
                    "timestamp": datetime.now().isoformat(),
                    "source": "agentic_seek" if self.services['agentic_seek']['connected'] else "fallback"
                }
            except Exception as e:
                logger.error(f"AgenticSeek chat error: {e}")
                return {
                    "response": "I'm experiencing some technical difficulties. Please try again later.",
                    "timestamp": datetime.now().isoformat(),
                    "source": "error"
                }

        @self.app.get("/workflows/list")
        async def list_workflows():
            """List available workflows"""
            workflows_dir = self.project_root / "workflows"
            workflows = []

            if workflows_dir.exists():
                for workflow_file in workflows_dir.rglob("*.json"):
                    workflows.append({
                        "name": workflow_file.stem,
                        "path": str(workflow_file.relative_to(self.project_root)),
                        "type": workflow_file.parent.name
                    })

            return {"workflows": workflows}

        @self.app.get("/models/list")
        async def list_models():
            """List generated models"""
            models = []

            if self.outputs_dir.exists():
                for model_file in self.outputs_dir.rglob("*.glb"):
                    stat = model_file.stat()
                    models.append({
                        "name": model_file.stem,
                        "filename": model_file.name,
                        "size": stat.st_size,
                        "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "url": f"/outputs/{model_file.name}"
                    })

            return {"models": models}

        @self.app.get("/download/{filename}")
        async def download_file(filename: str):
            """Download generated file"""
            file_path = self.outputs_dir / filename
            if not file_path.exists():
                raise HTTPException(status_code=404, detail="File not found")

            return FileResponse(
                path=str(file_path),
                filename=filename,
                media_type='application/octet-stream'
            )

    async def initialize_services(self):
        """Initialize all services"""
        logger.info("Initializing services...")

        # Check existing connections
        await self.check_service_connections()

        # Start services if needed
        if not self.services['comfyui']['connected']:
            await self.start_service_process('comfyui')

        if not self.services['agentic_seek']['connected']:
            await self.start_service_process('agentic_seek')

    async def check_service_connections(self):
        """Check connections to all services"""
        import aiohttp

        # Check ComfyUI
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.services['comfyui']['url']}/system_stats", timeout=5) as response:
                    self.services['comfyui']['connected'] = response.status == 200
        except:
            self.services['comfyui']['connected'] = False

        # Check AgenticSeek
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.services['agentic_seek']['url']}/health", timeout=5) as response:
                    self.services['agentic_seek']['connected'] = response.status == 200
        except:
            self.services['agentic_seek']['connected'] = False

        # Check HuggingFace
        self.services['huggingface']['connected'] = bool(self.services['huggingface']['token'])

    async def start_service_process(self, service_name: str):
        """Start a service process"""
        try:
            if service_name == 'comfyui':
                await self.start_comfyui()
            elif service_name == 'agentic_seek':
                await self.start_agentic_seek()

            # Wait a bit and check connection
            await asyncio.sleep(5)
            await self.check_service_connections()

        except Exception as e:
            logger.error(f"Failed to start {service_name}: {e}")

    async def start_comfyui(self):
        """Start ComfyUI service"""
        comfyui_path = Path("C:/Users/<USER>/OneDrive/Desktop/ComfyUI_windows_portable_nvidia (1)")

        if comfyui_path.exists():
            cmd = [str(comfyui_path / "python_embeded" / "python.exe"), "main.py", "--listen"]

            process = subprocess.Popen(
                cmd,
                cwd=str(comfyui_path),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            self.processes['comfyui'] = process
            logger.info("ComfyUI started")
        else:
            logger.error("ComfyUI path not found")

    async def start_agentic_seek(self):
        """Start AgenticSeek service"""
        if self.agentic_seek_dir.exists():
            # Use the existing virtual environment
            python_exe = self.agentic_seek_dir / "agentic_seek_env" / "Scripts" / "python.exe"

            if python_exe.exists():
                cmd = [str(python_exe), "api.py"]

                process = subprocess.Popen(
                    cmd,
                    cwd=str(self.agentic_seek_dir),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                self.processes['agentic_seek'] = process
                logger.info("AgenticSeek started")
            else:
                logger.error("AgenticSeek Python environment not found")
        else:
            logger.error("AgenticSeek directory not found")

    async def process_character_generation(self, task_id: str, request: CharacterRequest):
        """Process character generation in background"""
        try:
            logger.info(f"Starting character generation task {task_id}")

            # Check if ComfyUI is available
            if self.services['comfyui']['connected']:
                await self.execute_comfyui_workflow(task_id, request)
            else:
                await self.execute_fallback_generation(task_id, request)

            logger.info(f"Character generation task {task_id} completed")

        except Exception as e:
            logger.error(f"Character generation task {task_id} failed: {e}")
            # Create fallback model on error
            await self.create_fallback_model(task_id)

    async def execute_comfyui_workflow(self, task_id: str, request: CharacterRequest):
        """Execute ComfyUI workflow"""
        import aiohttp

        try:
            # Load appropriate workflow
            workflow_path = self.get_workflow_path(request.workflow_type)

            if workflow_path and workflow_path.exists():
                with open(workflow_path, 'r') as f:
                    workflow_data = json.load(f)

                # Customize workflow with request parameters
                workflow_data = self.customize_workflow(workflow_data, request)

                # Submit to ComfyUI
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.services['comfyui']['url']}/prompt",
                        json={"prompt": workflow_data}
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            prompt_id = result.get('prompt_id')

                            if prompt_id:
                                # Monitor workflow execution
                                await self.monitor_comfyui_execution(task_id, prompt_id)
                            else:
                                raise Exception("No prompt ID returned from ComfyUI")
                        else:
                            raise Exception(f"ComfyUI API error: {response.status}")
            else:
                logger.warning(f"Workflow not found: {request.workflow_type}")
                await self.execute_fallback_generation(task_id, request)

        except Exception as e:
            logger.error(f"ComfyUI workflow execution failed: {e}")
            await self.execute_fallback_generation(task_id, request)

    def get_workflow_path(self, workflow_type: str) -> Optional[Path]:
        """Get path to workflow file"""
        workflows_dir = self.project_root / "workflows"

        workflow_files = {
            'standard': workflows_dir / "working_character.json",
            'mickmumpitz': workflows_dir / "working_character.json",
            'triposr': workflows_dir / "working_character.json",
            'wonder3d': workflows_dir / "working_character.json"
        }

        return workflow_files.get(workflow_type)

    def customize_workflow(self, workflow_data: dict, request: CharacterRequest) -> dict:
        """Customize workflow with request parameters"""
        # This would customize the workflow based on the request
        # For now, just return the workflow as-is
        # In a real implementation, you'd modify nodes based on:
        # - request.prompt (text prompts)
        # - request.style (style parameters)
        # - request.quality (quality settings)

        return workflow_data

    async def monitor_comfyui_execution(self, task_id: str, prompt_id: str):
        """Monitor ComfyUI execution and retrieve results"""
        import aiohttp

        max_attempts = 60  # 5 minutes max
        attempt = 0

        while attempt < max_attempts:
            try:
                async with aiohttp.ClientSession() as session:
                    # Check execution status
                    async with session.get(f"{self.services['comfyui']['url']}/history/{prompt_id}") as response:
                        if response.status == 200:
                            history = await response.json()

                            if prompt_id in history:
                                execution_data = history[prompt_id]

                                if 'outputs' in execution_data:
                                    # Execution completed, retrieve outputs
                                    await self.retrieve_comfyui_outputs(task_id, execution_data['outputs'])
                                    return
                                elif 'status' in execution_data and execution_data['status'].get('completed', False):
                                    # Completed but no outputs
                                    logger.warning(f"ComfyUI execution completed but no outputs for {prompt_id}")
                                    break

                # Wait before next check
                await asyncio.sleep(5)
                attempt += 1

            except Exception as e:
                logger.error(f"Error monitoring ComfyUI execution: {e}")
                break

        # If we get here, execution failed or timed out
        logger.error(f"ComfyUI execution failed or timed out for task {task_id}")
        await self.execute_fallback_generation(task_id, None)

    async def retrieve_comfyui_outputs(self, task_id: str, outputs: dict):
        """Retrieve and save ComfyUI outputs"""
        import aiohttp

        try:
            # Look for 3D model outputs (GLB, OBJ, etc.)
            for node_id, node_outputs in outputs.items():
                if 'gltf' in node_outputs or 'glb' in node_outputs:
                    # Found 3D model output
                    files = node_outputs.get('gltf', node_outputs.get('glb', []))

                    for file_info in files:
                        filename = file_info.get('filename')
                        if filename:
                            # Download the file
                            async with aiohttp.ClientSession() as session:
                                async with session.get(f"{self.services['comfyui']['url']}/view?filename={filename}") as response:
                                    if response.status == 200:
                                        content = await response.read()

                                        # Save to outputs directory
                                        output_path = self.outputs_dir / f"{task_id}.glb"
                                        with open(output_path, 'wb') as f:
                                            f.write(content)

                                        logger.info(f"ComfyUI output saved: {output_path}")
                                        return

            # If no 3D model found, create fallback
            logger.warning("No 3D model output found from ComfyUI")
            await self.create_fallback_model(task_id)

        except Exception as e:
            logger.error(f"Error retrieving ComfyUI outputs: {e}")
            await self.create_fallback_model(task_id)

    async def execute_fallback_generation(self, task_id: str, request: CharacterRequest):
        """Execute fallback procedural generation"""
        steps = [
            "Validating inputs",
            "Processing image",
            "Generating procedural mesh",
            "Creating materials",
            "Optimizing model",
            "Exporting GLB"
        ]

        for i, step in enumerate(steps):
            logger.info(f"Task {task_id}: {step}")
            await asyncio.sleep(1)  # Faster for fallback

        # Create a simple procedural model
        await self.create_fallback_model(task_id)

    async def create_fallback_model(self, task_id: str):
        """Create a simple fallback GLB model"""
        # Create a simple cube GLB (using the working code from the step generator)
        glb_data = self.create_simple_glb()

        output_path = self.outputs_dir / f"{task_id}.glb"
        with open(output_path, 'wb') as f:
            f.write(glb_data)

        logger.info(f"Fallback model created: {output_path}")

    def create_simple_glb(self) -> bytes:
        """Create a simple GLB file"""
        import struct

        # Simple cube vertices
        vertices = [
            -1.0, -1.0, -1.0,  1.0, -1.0, -1.0,  1.0,  1.0, -1.0, -1.0,  1.0, -1.0,
            -1.0, -1.0,  1.0,  1.0, -1.0,  1.0,  1.0,  1.0,  1.0, -1.0,  1.0,  1.0
        ]

        indices = [
            0, 1, 2, 0, 2, 3,  4, 7, 6, 4, 6, 5,  0, 4, 5, 0, 5, 1,
            2, 6, 7, 2, 7, 3,  0, 3, 7, 0, 7, 4,  1, 5, 6, 1, 6, 2
        ]

        # Create GLTF JSON
        gltf_data = {
            "asset": {"version": "2.0", "generator": "Comprehensive Character Generator"},
            "scene": 0,
            "scenes": [{"nodes": [0]}],
            "nodes": [{"mesh": 0}],
            "meshes": [{"primitives": [{"attributes": {"POSITION": 0}, "indices": 1}]}],
            "accessors": [
                {"bufferView": 0, "componentType": 5126, "count": 8, "type": "VEC3"},
                {"bufferView": 1, "componentType": 5123, "count": 36, "type": "SCALAR"}
            ],
            "bufferViews": [
                {"buffer": 0, "byteOffset": 0, "byteLength": 96},
                {"buffer": 0, "byteOffset": 96, "byteLength": 72}
            ],
            "buffers": [{"byteLength": 168}]
        }

        # Create binary data
        binary_data = bytearray(168)

        # Pack vertices
        for i, v in enumerate(vertices):
            struct.pack_into('<f', binary_data, i * 4, v)

        # Pack indices
        for i, idx in enumerate(indices):
            struct.pack_into('<H', binary_data, 96 + i * 2, idx)

        # Create GLB
        import json
        json_str = json.dumps(gltf_data, separators=(',', ':'))
        json_bytes = json_str.encode('utf-8')

        # Pad JSON to 4-byte boundary
        json_padding = (4 - (len(json_bytes) % 4)) % 4
        json_bytes += b' ' * json_padding

        # Pad binary to 4-byte boundary
        binary_padding = (4 - (len(binary_data) % 4)) % 4
        binary_data += b'\x00' * binary_padding

        # Create GLB header
        total_length = 12 + 8 + len(json_bytes) + 8 + len(binary_data)

        glb = bytearray()
        glb.extend(struct.pack('<I', 0x46546C67))  # magic
        glb.extend(struct.pack('<I', 2))           # version
        glb.extend(struct.pack('<I', total_length)) # length

        # JSON chunk
        glb.extend(struct.pack('<I', len(json_bytes)))  # chunk length
        glb.extend(struct.pack('<I', 0x4E4F534A))       # chunk type "JSON"
        glb.extend(json_bytes)

        # Binary chunk
        glb.extend(struct.pack('<I', len(binary_data))) # chunk length
        glb.extend(struct.pack('<I', 0x004E4942))       # chunk type "BIN\0"
        glb.extend(binary_data)

        return bytes(glb)

    async def call_agentic_seek(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Call AgenticSeek API"""
        import aiohttp

        try:
            async with aiohttp.ClientSession() as session:
                payload = {"message": message}
                if context:
                    payload["context"] = context

                async with session.post(
                    f"{self.services['agentic_seek']['url']}/chat",
                    json=payload,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("response", "No response received")
                    else:
                        return "AgenticSeek is currently unavailable"
        except Exception as e:
            logger.error(f"AgenticSeek API call failed: {e}")
            return self.generate_fallback_response(message)

    def generate_fallback_response(self, message: str) -> str:
        """Generate fallback response when AgenticSeek is unavailable"""
        message_lower = message.lower()

        responses = {
            'help': 'I can help you with character generation, workflow optimization, and troubleshooting. What specific area would you like assistance with?',
            'workflow': 'For best results, I recommend using the Mickmumpitz workflow for high-quality character generation. Make sure your input image has good lighting and clear facial features.',
            'optimize': 'To optimize your model: 1) Use medium quality for balanced results, 2) Ensure your reference image is high resolution, 3) Choose the appropriate art style for your use case.',
            'error': 'If you\'re experiencing errors, check that ComfyUI is running and all required models are installed. I can help diagnose specific issues.',
            'export': 'You can export your character in multiple formats: GLB for web use, FBX for game engines, or OBJ for general 3D software.',
        }

        for keyword, response in responses.items():
            if keyword in message_lower:
                return response

        return 'I understand you\'re asking about character generation. Could you be more specific about what you need help with?'

def main():
    """Main entry point"""
    backend = ComprehensiveBackend()

    # Run the server
    uvicorn.run(
        backend.app,
        host="127.0.0.1",
        port=8080,
        log_level="info"
    )

if __name__ == "__main__":
    main()
