@echo off
echo 🚀 Starting ComfyUI with 3D Pack Support
echo ==========================================

echo.
echo 📍 Navigating to ComfyUI directory...
cd "C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)\ComfyUI_windows_portable"

echo.
echo 🔍 Checking if port 8188 is free...
netstat -an | findstr :8188
if %errorlevel% == 0 (
    echo ⚠️ Port 8188 is in use. Attempting to free it...
    taskkill /f /im python.exe 2>nul
    timeout /t 3 /nobreak > nul
)

echo.
echo 🎮 Starting ComfyUI...
echo 💡 This will take a moment to load all nodes...
echo.

.\python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build

echo.
echo 🛑 ComfyUI has stopped
pause
