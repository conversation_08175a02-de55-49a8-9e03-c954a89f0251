#!/usr/bin/env python3
"""
Simple test to see if Python is working
"""

print("🔍 Testing Python execution...")
print("Python is working!")

import sys
print(f"Python version: {sys.version}")

try:
    import http.server
    print("✅ http.server imported")
    
    import socketserver
    print("✅ socketserver imported")
    
    print("🚀 Starting simple server...")
    
    class TestHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            print(f"📥 Request received: {self.path}")
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <html>
            <head><title>Test Server</title></head>
            <body>
                <h1>🎉 Server is Working!</h1>
                <p>The Python HTTP server is running successfully.</p>
                <p>Time: <span id="time"></span></p>
                <script>
                    document.getElementById('time').textContent = new Date().toLocaleString();
                </script>
            </body>
            </html>
            """
            self.wfile.write(html.encode())
    
    PORT = 5000
    print(f"🌐 Starting server on port {PORT}...")
    
    with socketserver.TCPServer(("", PORT), TestHandler) as httpd:
        print(f"✅ Server started successfully!")
        print(f"🔗 Open http://localhost:{PORT} in your browser")
        print("⚠️  Server is running... Press Ctrl+C to stop")
        httpd.serve_forever()
        
except KeyboardInterrupt:
    print("\n🛑 Server stopped by user")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
