@echo off
title AI Character Generator - Diagnostic Server
color 0A

echo.
echo ================================================
echo    AI CHARACTER GENERATOR - DIAGNOSTIC MODE
echo ================================================
echo.

echo 🔍 Checking Python installation...
python --version
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo.
echo 🔍 Checking current directory...
echo Working Directory: %CD%
echo.

echo 🔍 Checking if diagnostic_server.py exists...
if not exist "diagnostic_server.py" (
    echo ❌ diagnostic_server.py not found!
    echo Please make sure you're in the correct directory.
    pause
    exit /b 1
)

echo ✅ diagnostic_server.py found
echo.

echo 🚀 Starting diagnostic server...
echo ⚠️  If the server starts successfully, open http://localhost:5000 in your browser
echo ⚠️  Press Ctrl+C to stop the server
echo.

python diagnostic_server.py

echo.
echo 🛑 Server has stopped.
echo.
pause
