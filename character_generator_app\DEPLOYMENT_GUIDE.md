# 🚀 Deployment Guide - AI-Powered 3D Character Generator

This guide covers deploying the AI-Powered 3D Character Generator as a standalone application.

## 📋 Prerequisites

### System Requirements
- **OS**: Windows 10/11, Ubuntu 20.04+, macOS 10.15+
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 50GB free space (for models and outputs)
- **GPU**: NVIDIA GPU with 6GB+ VRAM (recommended)
- **Python**: 3.11 or 3.12

### Software Dependencies
- Git
- Python 3.11/3.12
- CUDA Toolkit (for NVIDIA GPUs)
- Visual Studio Build Tools (Windows)

## 🔧 Installation Methods

### Method 1: One-Click Installation (Recommended)

#### Windows
```bash
# Clone repository
git clone https://github.com/yourusername/ai-3d-character-generator.git
cd ai-3d-character-generator

# Run installer
./install.bat

# Start application
./start.bat
```

#### Linux/macOS
```bash
# Clone repository
git clone https://github.com/yourusername/ai-3d-character-generator.git
cd ai-3d-character-generator

# Make scripts executable
chmod +x install.sh start_app.sh

# Run installer
./install.sh

# Start application
./start_app.sh
```

### Method 2: Manual Installation

#### Step 1: Environment Setup
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate.bat
# Linux/macOS:
source venv/bin/activate

# Upgrade pip
python -m pip install --upgrade pip
```

#### Step 2: Install Dependencies
```bash
# Install Python packages
pip install -r requirements.txt

# For GPU support (NVIDIA)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### Step 3: Download AI Models
```bash
# Download essential models
python download_ai_models.py

# This will download:
# - Stable Diffusion 1.5 (~4GB)
# - VAE models (~300MB)
# - Additional models as needed
```

#### Step 4: Configure Application
```bash
# Setup configuration
python setup_config.py

# Edit config.json to customize settings
# Add your Hugging Face API token
```

#### Step 5: Start Application
```bash
# Start the application
python comprehensive_backend.py

# Open browser to http://localhost:8080
```

## 🐳 Docker Deployment

### Build Docker Image
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p models outputs uploads temp logs

# Expose port
EXPOSE 8080

# Start command
CMD ["python", "comprehensive_backend.py"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  character-generator:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./models:/app/models
      - ./outputs:/app/outputs
      - ./uploads:/app/uploads
    environment:
      - CUDA_VISIBLE_DEVICES=0
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

### Run with Docker
```bash
# Build and run
docker-compose up --build

# Or run directly
docker build -t ai-character-generator .
docker run -p 8080:8080 -v $(pwd)/models:/app/models ai-character-generator
```

## ☁️ Cloud Deployment

### AWS EC2 Deployment

#### Instance Requirements
- **Instance Type**: g4dn.xlarge or better (GPU instances)
- **AMI**: Deep Learning AMI (Ubuntu)
- **Storage**: 100GB EBS volume
- **Security Group**: Allow port 8080

#### Setup Script
```bash
#!/bin/bash
# AWS EC2 setup script

# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y git python3-pip python3-venv

# Clone repository
git clone https://github.com/yourusername/ai-3d-character-generator.git
cd ai-3d-character-generator

# Install and start
./install.sh
./start_app.sh
```

### Google Cloud Platform

#### Compute Engine Setup
```bash
# Create instance with GPU
gcloud compute instances create ai-character-generator \
    --zone=us-central1-a \
    --machine-type=n1-standard-4 \
    --accelerator=type=nvidia-tesla-t4,count=1 \
    --image-family=pytorch-latest-gpu \
    --image-project=deeplearning-platform-release \
    --boot-disk-size=100GB \
    --maintenance-policy=TERMINATE
```

### Azure VM Deployment

#### Create VM with GPU
```bash
# Create resource group
az group create --name ai-character-gen --location eastus

# Create VM
az vm create \
    --resource-group ai-character-gen \
    --name ai-character-vm \
    --image UbuntuLTS \
    --size Standard_NC6 \
    --admin-username azureuser \
    --generate-ssh-keys
```

## 🔒 Production Configuration

### Security Settings
```json
{
  "security": {
    "allowed_origins": ["https://yourdomain.com"],
    "max_requests_per_minute": 30,
    "enable_https": true,
    "ssl_cert_path": "/path/to/cert.pem",
    "ssl_key_path": "/path/to/key.pem"
  }
}
```

### Performance Optimization
```json
{
  "performance": {
    "max_workers": 4,
    "batch_processing": true,
    "model_caching": true,
    "gpu_memory_fraction": 0.8
  }
}
```

### Monitoring Setup
```bash
# Install monitoring tools
pip install prometheus-client grafana-api

# Setup logging
mkdir -p /var/log/ai-character-generator
```

## 🔧 Troubleshooting

### Common Issues

#### CUDA Not Found
```bash
# Check CUDA installation
nvidia-smi
nvcc --version

# Install CUDA toolkit
# Follow: https://developer.nvidia.com/cuda-downloads
```

#### Out of Memory Errors
```bash
# Reduce batch size in config.json
"generation": {
  "max_batch_size": 1,
  "gpu_memory_fraction": 0.6
}
```

#### Model Download Failures
```bash
# Manual model download
python download_ai_models.py --retry --verbose

# Check disk space
df -h
```

### Performance Tuning

#### GPU Optimization
```python
# In config.json
{
  "gpu": {
    "enable_mixed_precision": true,
    "memory_growth": true,
    "allow_memory_growth": true
  }
}
```

#### CPU Optimization
```python
{
  "cpu": {
    "num_threads": 8,
    "enable_mkldnn": true
  }
}
```

## 📊 Monitoring and Maintenance

### Health Checks
```bash
# Check application status
curl http://localhost:8080/health

# Check GPU usage
nvidia-smi

# Check disk usage
du -sh models/ outputs/
```

### Log Management
```bash
# View logs
tail -f logs/app.log

# Rotate logs
logrotate /etc/logrotate.d/ai-character-generator
```

### Backup Strategy
```bash
# Backup configuration
tar -czf config-backup.tar.gz config.json workflows/

# Backup outputs
rsync -av outputs/ backup/outputs/
```

## 🔄 Updates and Maintenance

### Update Application
```bash
# Pull latest changes
git pull origin main

# Update dependencies
pip install -r requirements.txt --upgrade

# Restart application
./start.bat  # or ./start_app.sh
```

### Model Updates
```bash
# Update models
python download_ai_models.py --update

# Clear cache
rm -rf temp/* logs/*
```

## 📞 Support

For deployment issues:
- Check the [Troubleshooting Guide](TROUBLESHOOTING.md)
- Open an issue on GitHub
- Join our Discord community

---

**Happy Deploying! 🚀**
