"""
Script to extract workflow URLs from .url files.
"""

import os
import sys
import argparse
import webbrowser
from pathlib import Path

def extract_urls_from_url_files(directory):
    """
    Extract URLs from .url files in the specified directory.
    
    Args:
        directory: Directory containing .url files
    
    Returns:
        dict: Dictionary mapping file names to URLs
    """
    urls = {}
    
    for file_path in Path(directory).glob('*.url'):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Extract URL from the file
                for line in content.splitlines():
                    if line.startswith('URL='):
                        url = line[4:].strip()
                        urls[file_path.stem] = url
                        break
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
    
    return urls

def open_urls_in_browser(urls):
    """
    Open URLs in the browser.
    
    Args:
        urls: Dictionary mapping file names to URLs
    """
    for name, url in urls.items():
        print(f"Opening {name}...")
        webbrowser.open(url)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Extract workflow URLs from .url files')
    parser.add_argument('--directory', type=str, default='workflows/mickmumpitz',
                        help='Directory containing .url files')
    parser.add_argument('--open', action='store_true',
                        help='Open URLs in browser')
    args = parser.parse_args()
    
    # Extract URLs from .url files
    urls = extract_urls_from_url_files(args.directory)
    
    if not urls:
        print("No URLs found in the specified directory.")
        return
    
    # Print URLs
    print("Extracted URLs:")
    for name, url in urls.items():
        print(f"{name}: {url}")
    
    # Open URLs in browser if requested
    if args.open:
        open_urls_in_browser(urls)
    
    print("\nInstructions for downloading workflow files:")
    print("1. When the browser opens, right-click on the download link")
    print("2. Select 'Save link as...' or 'Save target as...'")
    print("3. Navigate to the workflows/mickmumpitz directory")
    print("4. Save the file with the .json extension")
    print("5. Repeat for all workflow files")

if __name__ == '__main__':
    main()
