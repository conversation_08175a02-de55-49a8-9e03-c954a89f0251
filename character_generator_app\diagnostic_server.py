#!/usr/bin/env python3
"""
Diagnostic server with comprehensive error handling
"""

import sys
import os
import traceback
import time

def main():
    try:
        print("=" * 50)
        print("🔍 DIAGNOSTIC SERVER STARTING")
        print("=" * 50)
        print(f"Python version: {sys.version}")
        print(f"Working directory: {os.getcwd()}")
        print(f"Script location: {__file__}")
        print()
        
        print("📦 Testing imports...")
        
        try:
            import http.server
            print("✅ http.server imported successfully")
        except Exception as e:
            print(f"❌ Failed to import http.server: {e}")
            return
        
        try:
            import socketserver
            print("✅ socketserver imported successfully")
        except Exception as e:
            print(f"❌ Failed to import socketserver: {e}")
            return
        
        print()
        print("🌐 Creating server...")
        
        class DiagnosticHandler(http.server.SimpleHTTPRequestHandler):
            def do_GET(self):
                print(f"📥 Received request: {self.path}")
                
                if self.path == '/':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    
                    html = """
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>🎉 Server Working!</title>
                        <style>
                            body {
                                font-family: Arial, sans-serif;
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                color: white;
                                text-align: center;
                                padding: 50px;
                                margin: 0;
                            }
                            .container {
                                background: rgba(255, 255, 255, 0.1);
                                padding: 40px;
                                border-radius: 15px;
                                max-width: 600px;
                                margin: 0 auto;
                            }
                            h1 { font-size: 3em; margin-bottom: 20px; }
                            p { font-size: 1.2em; margin: 10px 0; }
                            .status { background: rgba(40, 167, 69, 0.3); padding: 20px; border-radius: 10px; margin: 20px 0; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>🎉 Server is Working!</h1>
                            <div class="status">
                                <h2>✅ Connection Successful</h2>
                                <p>The Python HTTP server is running correctly!</p>
                            </div>
                            <p><strong>Server Status:</strong> Online</p>
                            <p><strong>Port:</strong> 5000</p>
                            <p><strong>Time:</strong> <span id="time"></span></p>
                            <p><strong>Ready for:</strong> Character Generation</p>
                        </div>
                        <script>
                            document.getElementById('time').textContent = new Date().toLocaleString();
                        </script>
                    </body>
                    </html>
                    """
                    self.wfile.write(html.encode())
                    
                elif self.path == '/test':
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    response = '{"status": "working", "message": "Server is operational"}'
                    self.wfile.write(response.encode())
                    
                else:
                    self.send_response(404)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    self.wfile.write(b'<h1>404 - Page not found</h1>')
            
            def log_message(self, format, *args):
                print(f"🌐 {format % args}")
        
        PORT = 5000
        print(f"🚀 Starting server on port {PORT}...")
        
        # Try to bind to the port
        try:
            server = socketserver.TCPServer(("", PORT), DiagnosticHandler)
            print(f"✅ Server bound to port {PORT} successfully!")
        except OSError as e:
            if "Address already in use" in str(e):
                print(f"❌ Port {PORT} is already in use!")
                print("   Try a different port or stop the existing server.")
                return
            else:
                print(f"❌ Failed to bind to port {PORT}: {e}")
                return
        
        print()
        print("🎉 SERVER STARTED SUCCESSFULLY!")
        print("=" * 30)
        print(f"🌐 Web Interface: http://localhost:{PORT}")
        print(f"🧪 Test Endpoint: http://localhost:{PORT}/test")
        print("⚠️  Press Ctrl+C to stop the server")
        print()
        
        # Start serving
        try:
            server.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped by user (Ctrl+C)")
        finally:
            server.server_close()
            print("✅ Server closed cleanly")
    
    except Exception as e:
        print(f"\n❌ CRITICAL ERROR: {e}")
        print("\n📋 Full traceback:")
        traceback.print_exc()
        print("\n" + "=" * 50)
        input("Press Enter to exit...")

if __name__ == '__main__':
    main()
