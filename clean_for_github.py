#!/usr/bin/env python3
"""
Clean Repository for GitHub Upload
Removes problematic files and creates a clean version for upload
"""

import os
import shutil
import glob
from pathlib import Path

def clean_repository():
    """Remove files that might cause GitHub upload issues"""
    
    print("🧹 Cleaning repository for GitHub upload...")
    
    # Files and patterns to remove
    remove_patterns = [
        # Large binary files
        "*.glb",
        "*.fbx", 
        "*.ckpt",
        "*.safetensors",
        "*.pth",
        "*.bin",
        "*.db",
        
        # Generated outputs
        "backend/outputs/**/*",
        "character_library/**/*",
        "unity_exports/**/*",
        "unreal_exports/**/*",
        "test_results/**/*",
        
        # Uploaded files
        "backend/uploads/**/*",
        "uploads/**/*",
        
        # Test files
        "test_*.glb",
        "test_*.png",
        "enhanced_test_face.png",
        "pipeline_test_face.png",
        
        # Temporary files
        "*.tmp",
        "*.temp",
        "*.log",
        
        # Cache files
        "__pycache__/**/*",
        "*.pyc",
        "*.pyo",
        
        # Large test files
        "manual_test_results.json",
        
        # Duplicate release folder
        "release/**/*"
    ]
    
    removed_count = 0
    
    for pattern in remove_patterns:
        files = glob.glob(pattern, recursive=True)
        for file_path in files:
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"  Removed file: {file_path}")
                    removed_count += 1
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"  Removed directory: {file_path}")
                    removed_count += 1
            except Exception as e:
                print(f"  Warning: Could not remove {file_path}: {e}")
    
    # Remove empty directories
    for root, dirs, files in os.walk(".", topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            try:
                if not os.listdir(dir_path):
                    os.rmdir(dir_path)
                    print(f"  Removed empty directory: {dir_path}")
                    removed_count += 1
            except:
                pass
    
    print(f"✓ Cleaned {removed_count} files/directories")
    
    # Update .gitignore to prevent future issues
    gitignore_content = """# AI Models (too large for git)
models/
*.ckpt
*.safetensors
*.pth
*.bin

# Generated outputs
outputs/
backend/outputs/
character_library/
unity_exports/
unreal_exports/
test_results/

# Uploads
uploads/
backend/uploads/

# Virtual environment
venv/
env/
.venv/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment variables
.env
.env.local
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Database
*.db
*.sqlite3

# Temporary files
*.tmp
*.temp

# Large test files
test_*.glb
test_*.png
enhanced_test_face.png
pipeline_test_face.png
manual_test_results.json

# ComfyUI specific
comfyui/output/
comfyui/input/
comfyui/temp/

# Release folder (use GitHub releases instead)
release/
"""
    
    with open(".gitignore", "w") as f:
        f.write(gitignore_content)
    
    print("✓ Updated .gitignore")

def create_placeholder_files():
    """Create placeholder files for important directories"""
    
    placeholder_dirs = [
        "models",
        "outputs", 
        "uploads",
        "logs",
        "temp"
    ]
    
    for dir_name in placeholder_dirs:
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        
        placeholder_file = dir_path / ".gitkeep"
        with open(placeholder_file, "w") as f:
            f.write(f"# Placeholder file to keep {dir_name} directory in git\n")
        
        print(f"✓ Created placeholder: {placeholder_file}")

def main():
    """Main cleaning function"""
    print("🚀 AI-Powered 3D Character Generator")
    print("🧹 Repository Cleaning for GitHub Upload")
    print("=" * 50)
    
    try:
        clean_repository()
        create_placeholder_files()
        
        print("\n🎉 Repository cleaned successfully!")
        print("\n📋 Next steps:")
        print("1. Run: git add .")
        print("2. Run: git commit -m 'Clean repository for GitHub upload'")
        print("3. Run: git push -u origin main")
        
        print("\n⚠️  Important notes:")
        print("- Large AI models are excluded (users download separately)")
        print("- Generated outputs are excluded (created at runtime)")
        print("- Test files and uploads are excluded")
        print("- This keeps repository size under GitHub limits")
        
    except Exception as e:
        print(f"\n❌ Cleaning failed: {e}")

if __name__ == "__main__":
    main()
