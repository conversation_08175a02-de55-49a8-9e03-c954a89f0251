#!/usr/bin/env python3
"""Debug Hugging Face token format"""

def debug_token():
    token = "*************************************"
    
    print("🔍 Token Analysis:")
    print(f"Length: {len(token)}")
    print(f"Starts with 'hf_': {token.startswith('hf_')}")
    print(f"Contains spaces: {' ' in token}")
    print(f"Contains newlines: {'\\n' in token or '\\r' in token}")
    print(f"First 10 chars: '{token[:10]}'")
    print(f"Last 10 chars: '{token[-10:]}'")
    print(f"Full token: '{token}'")
    
    # Check for hidden characters
    print(f"Token bytes: {token.encode('utf-8')}")
    
    # Expected format check
    if len(token) == 37 and token.startswith('hf_'):
        print("✅ Token format looks correct")
    else:
        print("❌ Token format might be incorrect")
        print("Expected: 37 characters starting with 'hf_'")

if __name__ == "__main__":
    debug_token()
