import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const JobsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled.h1`
  text-align: center;
  color: white;
  margin-bottom: 2rem;
  font-size: 2.5rem;
  font-weight: 700;
`;

const JobsCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
`;

const JobsGrid = styled.div`
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
`;

const JobCard = styled(motion.div)`
  background: rgba(102, 126, 234, 0.05);
  border: 2px solid rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(102, 126, 234, 0.3);
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  &.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.15);
  }
`;

const JobHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const JobId = styled.span`
  font-family: monospace;
  font-size: 0.9rem;
  color: #666;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  background-color: ${props => {
    switch (props.status) {
      case 'queued': return '#ffc107';
      case 'generating_workflow': return '#17a2b8';
      case 'workflow_created': return '#17a2b8';
      case 'processing_3d': return '#17a2b8';
      case 'post_processing': return '#17a2b8';
      case 'completed': return '#28a745';
      case 'error': return '#dc3545';
      default: return '#6c757d';
    }
  }};
  color: ${props => props.status === 'queued' ? '#212529' : 'white'};
`;

const JobContent = styled.div`
  margin-bottom: 1rem;
`;

const JobPrompt = styled.p`
  margin: 0.5rem 0;
  color: #333;
  font-style: italic;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
`;

const JobMeta = styled.div`
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 1rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  border-radius: 4px;
  width: ${props => props.progress}%;
`;

const JobActions = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const ActionButton = styled.button`
  background: ${props => props.variant === 'primary' ? 'linear-gradient(45deg, #667eea, #764ba2)' : '#f8f9fa'};
  color: ${props => props.variant === 'primary' ? 'white' : '#333'};
  border: 2px solid ${props => props.variant === 'primary' ? 'transparent' : '#e0e0e0'};
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: #666;
`;

const RefreshButton = styled.button`
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
`;

const JobsList = ({ jobs, onJobSelected }) => {
  const [allJobs, setAllJobs] = useState(jobs || []);
  const [selectedJobId, setSelectedJobId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setAllJobs(jobs || []);
  }, [jobs]);

  const fetchJobs = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get('/api/jobs');
      setAllJobs(response.data.jobs || []);
    } catch (error) {
      console.error('Error fetching jobs:', error);
      toast.error('Failed to fetch jobs');
    } finally {
      setIsLoading(false);
    }
  };

  const handleJobClick = (job) => {
    setSelectedJobId(job.id || job.job_id);
    onJobSelected(job);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return 'Invalid date';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'queued': return 'Queued';
      case 'generating_workflow': return 'Creating Workflow';
      case 'workflow_created': return 'Workflow Ready';
      case 'processing_3d': return 'Generating 3D';
      case 'post_processing': return 'Finalizing';
      case 'completed': return 'Completed';
      case 'error': return 'Error';
      default: return status;
    }
  };

  return (
    <JobsContainer>
      <Title>Your Character Jobs</Title>
      
      <JobsCard
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
          <h3>All Jobs ({allJobs.length})</h3>
          <RefreshButton onClick={fetchJobs} disabled={isLoading}>
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </RefreshButton>
        </div>

        {allJobs.length === 0 ? (
          <EmptyState>
            <h4>No jobs found</h4>
            <p>You haven't generated any characters yet.</p>
            <Link to="/" className="btn">
              Generate Your First Character
            </Link>
          </EmptyState>
        ) : (
          <JobsGrid>
            {allJobs.map((job, index) => (
              <JobCard
                key={job.id || job.job_id || index}
                className={selectedJobId === (job.id || job.job_id) ? 'selected' : ''}
                onClick={() => handleJobClick(job)}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <JobHeader>
                  <JobId>{(job.id || job.job_id || 'unknown').slice(0, 8)}</JobId>
                  <StatusBadge status={job.status}>
                    {getStatusText(job.status)}
                  </StatusBadge>
                </JobHeader>

                <JobContent>
                  {job.text_prompt && (
                    <JobPrompt>"{job.text_prompt}"</JobPrompt>
                  )}
                  
                  <JobMeta>
                    <div>Created: {formatDate(job.created_at)}</div>
                    {job.completed_at && (
                      <div>Completed: {formatDate(job.completed_at)}</div>
                    )}
                    {job.image_path && (
                      <div>📷 Image uploaded</div>
                    )}
                  </JobMeta>

                  {job.status !== 'completed' && job.status !== 'error' && (
                    <div>
                      <ProgressBar>
                        <ProgressFill progress={job.progress || 0} />
                      </ProgressBar>
                      <div style={{ fontSize: '0.8rem', color: '#666' }}>
                        {job.progress || 0}% complete
                      </div>
                    </div>
                  )}

                  {job.error && (
                    <div style={{ color: '#dc3545', fontSize: '0.85rem', marginTop: '0.5rem' }}>
                      Error: {job.error}
                    </div>
                  )}
                </JobContent>

                <JobActions>
                  <ActionButton
                    variant="primary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleJobClick(job);
                    }}
                  >
                    Select
                  </ActionButton>
                  
                  {job.status === 'completed' && (
                    <ActionButton
                      as={Link}
                      to={`/viewer/${job.id || job.job_id}`}
                      onClick={(e) => e.stopPropagation()}
                    >
                      View 3D
                    </ActionButton>
                  )}
                  
                  {job.status === 'completed' && (
                    <ActionButton
                      onClick={async (e) => {
                        e.stopPropagation();
                        try {
                          const response = await axios.get(`/api/download/${job.id || job.job_id}/model`, {
                            responseType: 'blob'
                          });
                          
                          const url = window.URL.createObjectURL(new Blob([response.data]));
                          const link = document.createElement('a');
                          link.href = url;
                          link.setAttribute('download', `character_${(job.id || job.job_id).slice(0, 8)}.glb`);
                          document.body.appendChild(link);
                          link.click();
                          link.remove();
                          window.URL.revokeObjectURL(url);
                          
                          toast.success('Model downloaded!');
                        } catch (error) {
                          toast.error('Download failed');
                        }
                      }}
                    >
                      Download
                    </ActionButton>
                  )}
                </JobActions>
              </JobCard>
            ))}
          </JobsGrid>
        )}
      </JobsCard>
    </JobsContainer>
  );
};

export default JobsList;
