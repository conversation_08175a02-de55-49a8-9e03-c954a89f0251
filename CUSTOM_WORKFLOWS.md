# Custom Workflows and Direct Editing

This document describes how to create custom workflows and directly edit content in Blender and Unreal Engine.

## Creating Custom ComfyUI Workflows

You can create custom ComfyUI workflows programmatically using the `ComfyUIWorkflowGenerator` class:

```python
from src.workflow_generators.comfyui_workflow_generator import ComfyUIWorkflowGenerator

# Initialize the workflow generator
generator = ComfyUIWorkflowGenerator()

# Create an image-to-3D workflow
workflow = generator.create_image_to_3d_workflow(use_mickmumpitz=True)
generator.save_workflow(workflow, "my_custom_workflow.json")
```

Or use the command-line interface:

```bash
python src/integrated_workflow.py create-workflow --type image-to-3d --output my_workflow.json --use-mickmumpitz
```

Available workflow types:
- `image-to-3d`: Convert images to 3D models
- `text-to-3d`: Generate 3D models from text
- `character`: Generate character sheets

## Direct Editing in Blender

You can directly edit 3D models in Blender using the `BlenderDirectEditor` class:

```python
from src.integrations.blender.direct_editing import BlenderDirectEditor

# Initialize the Blender editor
editor = BlenderDirectEditor()

# Edit an existing model
editor.edit_existing_model("path/to/model.glb", edit_mode="sculpt")

# Create a new model
editor.create_new_model(output_path="output/my_new_model.glb")
```

Or use the command-line interface:

```bash
python src/integrated_workflow.py edit-in-blender --model-path path/to/model.glb --edit-mode sculpt --output-path output/edited_model.glb
```

Available edit modes:
- `sculpt`: Sculpt mode for organic modeling
- `edit`: Edit mode for precise vertex/edge/face editing
- `texture`: Texture paint mode for painting textures

## Direct Editing in Unreal Engine

You can directly edit and create content in Unreal Engine using the `UnrealDirectEditor` class:

```python
from src.integrations.unreal.direct_editing import UnrealDirectEditor

# Initialize the Unreal Engine editor
editor = UnrealDirectEditor()

# Open Unreal Engine
editor.open_unreal_editor()

# Import an asset
editor.import_asset("path/to/asset.fbx", destination_path="/Game/MyAssets")

# Create a new level
editor.create_level("MyNewLevel", template="Default")
```

Or use the command-line interface:

```bash
# Open Unreal Engine
python src/integrated_workflow.py open-unreal

# Import an asset
python src/integrated_workflow.py import-to-unreal --asset-path path/to/asset.fbx --destination-path /Game/MyAssets --asset-type StaticMesh

# Create a new level
python src/integrated_workflow.py create-unreal-level --level-name MyNewLevel --template Default
```

## Integrated Workflow

You can create a complete pipeline from input to output using the `IntegratedWorkflow` class:

```python
from src.integrated_workflow import IntegratedWorkflow

# Initialize the integrated workflow
workflow = IntegratedWorkflow()

# Create a complete pipeline
result = workflow.create_complete_pipeline(
    input_type="image",
    output_type="3d",
    input_path="input/image.png",
    output_path="output/model.glb"
)
```

Or use the command-line interface:

```bash
python src/integrated_workflow.py create-pipeline --input-type image --output-type 3d --input-path input/image.png --output-path output/model.glb
```

## Example Workflow

Here's an example of a complete workflow:

1. Create a character using mickmumpitz's workflow:
   ```bash
   python src/integrated_workflow.py create-workflow --type character --use-mickmumpitz
   ```

2. Generate the character in ComfyUI by loading the workflow file.

3. Edit the generated 3D model in Blender:
   ```bash
   python src/integrated_workflow.py edit-in-blender --model-path output/character.glb --edit-mode sculpt
   ```

4. Import the edited model into Unreal Engine:
   ```bash
   python src/integrated_workflow.py import-to-unreal --asset-path output/character.glb
   ```

5. Create a new level in Unreal Engine:
   ```bash
   python src/integrated_workflow.py create-unreal-level --level-name CharacterShowcase
   ```

6. Open Unreal Engine to work with the imported assets:
   ```bash
   python src/integrated_workflow.py open-unreal
   ```

## Requirements

- ComfyUI installed and configured
- Blender installed and accessible in the PATH or specified via environment variable
- Unreal Engine installed and accessible in the PATH or specified via environment variable
- An Unreal Engine project created and specified via environment variable

## Environment Variables

You can set the following environment variables to configure the tools:

- `COMFYUI_PATH`: Path to ComfyUI installation
- `BLENDER_PATH`: Path to Blender executable
- `UNREAL_PATH`: Path to Unreal Engine executable
- `UNREAL_PROJECT_PATH`: Path to Unreal Engine project
