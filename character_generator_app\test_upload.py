"""
Test script to verify image upload functionality
"""

import requests
from PIL import Image, ImageDraw
import io

def create_test_image():
    """Create a simple test image."""
    # Create a simple character image
    img = Image.new('RGB', (512, 512), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # Draw a simple character
    # Head
    draw.ellipse([200, 100, 312, 212], fill='peachpuff', outline='black', width=2)
    
    # Body
    draw.rectangle([225, 212, 287, 350], fill='blue', outline='black', width=2)
    
    # Arms
    draw.rectangle([175, 230, 225, 280], fill='blue', outline='black', width=2)
    draw.rectangle([287, 230, 337, 280], fill='blue', outline='black', width=2)
    
    # Legs
    draw.rectangle([235, 350, 260, 450], fill='darkblue', outline='black', width=2)
    draw.rectangle([252, 350, 277, 450], fill='darkblue', outline='black', width=2)
    
    # Eyes
    draw.ellipse([220, 130, 235, 145], fill='black')
    draw.ellipse([277, 130, 292, 145], fill='black')
    
    # Mouth
    draw.arc([235, 160, 277, 185], start=0, end=180, fill='black', width=2)
    
    return img

def test_upload():
    """Test the upload endpoint."""
    print("🧪 Testing image upload...")
    
    # Create test image
    img = create_test_image()
    
    # Convert to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    # Test upload
    try:
        files = {'file': ('test_character.png', img_bytes, 'image/png')}
        response = requests.post('http://localhost:5000/api/upload', files=files)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Upload successful!")
            print(f"Filename: {result['filename']}")
            print(f"File path: {result['file_path']}")
            print(f"Processed path: {result['processed_path']}")
            return result
        else:
            print("❌ Upload failed!")
            return None
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None

def test_generate_with_upload():
    """Test the complete flow: upload + generate."""
    print("\n🎮 Testing complete flow...")
    
    # First upload an image
    upload_result = test_upload()
    if not upload_result:
        print("❌ Cannot proceed without successful upload")
        return
    
    # Then generate a character
    print("\n🚀 Testing character generation with uploaded image...")
    data = {
        "text_prompt": "A friendly cartoon character with blue armor",
        "image_path": upload_result['processed_path'],
        "style_options": {
            "realistic": False,
            "cartoon": True,
            "anime": False,
            "lowPoly": False
        }
    }
    
    try:
        response = requests.post('http://localhost:5000/api/generate', json=data)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Generation started!")
            print(f"Job ID: {result['job_id']}")
            return result['job_id']
        else:
            print("❌ Generation failed!")
            return None
            
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return None

if __name__ == "__main__":
    test_generate_with_upload()
