using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class GameManager : MonoBehaviour
{
    // Singleton instance
    public static GameManager instance;
    
    // Game state
    public enum GameState { MainMenu, Playing, Paused, GameOver }
    public GameState currentState;
    
    // UI elements
    public GameObject mainMenuUI;
    public GameObject gameUI;
    public GameObject pauseMenuUI;
    public GameObject gameOverUI;
    
    // Score
    public int score = 0;
    public Text scoreText;
    
    // Player reference
    public GameObject player;
    
    // Awake is called when the script instance is being loaded
    void Awake()
    {
        // Singleton pattern
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    // Start is called before the first frame update
    void Start()
    {
        // Set initial game state
        SetGameState(GameState.MainMenu);
    }

    // Update is called once per frame
    void Update()
    {
        // Handle pause input
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            if (currentState == GameState.Playing)
            {
                SetGameState(GameState.Paused);
            }
            else if (currentState == GameState.Paused)
            {
                SetGameState(GameState.Playing);
            }
        }
        
        // Update UI
        if (scoreText != null)
        {
            scoreText.text = "Score: " + score.ToString();
        }
    }
    
    // Set the game state
    public void SetGameState(GameState newState)
    {
        currentState = newState;
        
        // Handle state-specific actions
        switch (newState)
        {
            case GameState.MainMenu:
                Time.timeScale = 0f;
                ShowUI(mainMenuUI);
                break;
                
            case GameState.Playing:
                Time.timeScale = 1f;
                ShowUI(gameUI);
                break;
                
            case GameState.Paused:
                Time.timeScale = 0f;
                ShowUI(pauseMenuUI);
                break;
                
            case GameState.GameOver:
                Time.timeScale = 0f;
                ShowUI(gameOverUI);
                break;
        }
    }
    
    // Show only the specified UI and hide others
    private void ShowUI(GameObject uiToShow)
    {
        if (mainMenuUI != null) mainMenuUI.SetActive(uiToShow == mainMenuUI);
        if (gameUI != null) gameUI.SetActive(uiToShow == gameUI);
        if (pauseMenuUI != null) pauseMenuUI.SetActive(uiToShow == pauseMenuUI);
        if (gameOverUI != null) gameOverUI.SetActive(uiToShow == gameOverUI);
    }
    
    // Start the game
    public void StartGame()
    {
        score = 0;
        SetGameState(GameState.Playing);
    }
    
    // Restart the game
    public void RestartGame()
    {
        SceneManager.LoadScene(SceneManager.GetActiveScene().buildIndex);
        StartGame();
    }
    
    // Quit the game
    public void QuitGame()
    {
        #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
        #else
            Application.Quit();
        #endif
    }
    
    // Add points to the score
    public void AddScore(int points)
    {
        score += points;
    }
    
    // Game over
    public void GameOver()
    {
        SetGameState(GameState.GameOver);
    }
}
