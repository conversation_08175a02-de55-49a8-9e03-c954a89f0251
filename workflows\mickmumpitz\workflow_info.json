{"workflows": [{"name": "3D Rendering Image", "file": "200524_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v3.json", "description": "Renders 3D scenes as images using SDXL", "source": "https://www.patreon.com/posts/free-workflows-104795004", "guide": "https://docs.google.com/document/d/1lemaWcaSAxn1YsRSkRcCXAT9Nho-tIObcoijPrlDfSs/edit?usp=sharing"}, {"name": "3D Rendering Video", "file": "200524_Mickmumpitz_3D-RENDERING_VIDEO_SD15LCM_SIMPLE_v3.json", "description": "Renders 3D animations as videos using SD 1.5 LCM", "source": "https://www.patreon.com/posts/free-workflows-104795004", "guide": "https://docs.google.com/document/d/1lemaWcaSAxn1YsRSkRcCXAT9Nho-tIObcoijPrlDfSs/edit?usp=sharing"}, {"name": "Character Sheet", "file": "241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json", "description": "Generates consistent character sheets using FLUX", "source": "https://www.patreon.com/posts/free-workflows-113743435", "guide": "https://docs.google.com/document/d/1PHYMpXqfNKj9dQIMVpXAg7R4FjNFIINok09L9heQTnM/edit?usp=sharing"}, {"name": "FLUX + LoRA", "file": "241007_MICKMUMPITZ_FLUX+LORA.json", "description": "Trains custom LoRAs for character consistency", "source": "https://www.patreon.com/posts/free-workflows-113743435", "guide": "https://docs.google.com/document/d/1PHYMpXqfNKj9dQIMVpXAg7R4FjNFIINok09L9heQTnM/edit?usp=sharing"}], "author": "mick<PERSON><PERSON><PERSON>", "author_url": "https://www.patreon.com/Mickmumpitz", "youtube": "https://www.youtube.com/@mickmumpitz"}