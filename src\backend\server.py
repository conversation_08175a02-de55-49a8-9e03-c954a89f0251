"""
Backend server module for handling API requests.
"""

import os
import json
from flask import request, jsonify

def create_api_routes(app, pipeline_manager):
    """
    Create API routes for the Flask application.
    
    Args:
        app: Flask application instance
        pipeline_manager: PipelineManager instance
    """
    
    @app.route('/api/generate', methods=['POST'])
    def generate_content():
        """
        Generate content based on input type and parameters.
        
        Expected JSON payload:
        {
            "input_type": "image"|"text"|"3d"|"video",
            "output_type": "image"|"3d"|"video"|"game",
            "input_data": <base64_encoded_data> or <text_prompt>,
            "parameters": {
                // Additional parameters specific to the generation task
            }
        }
        """
        try:
            data = request.json
            
            # Validate request
            required_fields = ['input_type', 'output_type', 'input_data']
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'Missing required field: {field}'}), 400
            
            # Process the generation request
            job_id = pipeline_manager.create_job(
                input_type=data['input_type'],
                output_type=data['output_type'],
                input_data=data['input_data'],
                parameters=data.get('parameters', {})
            )
            
            return jsonify({
                'status': 'success',
                'message': 'Generation job created',
                'job_id': job_id
            })
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/job/<job_id>', methods=['GET'])
    def get_job_status(job_id):
        """Get the status of a generation job."""
        try:
            status = pipeline_manager.get_job_status(job_id)
            return jsonify(status)
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/job/<job_id>/result', methods=['GET'])
    def get_job_result(job_id):
        """Get the result of a completed generation job."""
        try:
            result = pipeline_manager.get_job_result(job_id)
            return jsonify(result)
        except Exception as e:
            return jsonify({'error': str(e)}), 500
