"""
Script to install required ComfyUI custom nodes for mickmumpitz's workflows.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def install_custom_nodes(comfyui_path):
    """
    Install the required ComfyUI custom nodes for mickmumpitz's workflows.
    
    Args:
        comfyui_path: Path to the ComfyUI installation
    """
    # Create custom_nodes directory if it doesn't exist
    custom_nodes_dir = os.path.join(comfyui_path, 'custom_nodes')
    os.makedirs(custom_nodes_dir, exist_ok=True)
    
    # List of required custom nodes repositories
    required_nodes = [
        # For 3D rendering workflow
        {'repo': 'https://github.com/Fannovel16/comfyui_controlnet_aux.git', 'name': 'comfyui_controlnet_aux'},
        {'repo': 'https://github.com/Kosinkadink/ComfyUI-AnimateDiff-Evolved.git', 'name': 'ComfyUI-AnimateDiff-Evolved'},
        {'repo': 'https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git', 'name': 'ComfyUI-VideoHelperSuite'},
        {'repo': 'https://github.com/cubiq/ComfyUI_IPAdapter_plus.git', 'name': 'ComfyUI_IPAdapter_plus'},
        
        # For character generation workflow
        {'repo': 'https://github.com/ssitu/ComfyUI_UltimateSDUpscale.git', 'name': 'ComfyUI_UltimateSDUpscale'},
        {'repo': 'https://github.com/ltdrdata/ComfyUI-Manager.git', 'name': 'ComfyUI-Manager'},
        {'repo': 'https://github.com/rgthree/rgthree-comfy.git', 'name': 'rgthree-comfy'},
        {'repo': 'https://github.com/coreyryanhanson/ComfyQR.git', 'name': 'ComfyQR'},
        {'repo': 'https://github.com/kijai/ComfyUI-KJNodes.git', 'name': 'ComfyUI-KJNodes'},
        {'repo': 'https://github.com/WASasquatch/was-node-suite-comfyui.git', 'name': 'was-node-suite-comfyui'},
        {'repo': 'https://github.com/Gourieff/comfyui-reactor-node.git', 'name': 'comfyui-reactor-node'},
        {'repo': 'https://github.com/ZHO-ZHO-ZHO/ComfyUI-Flowty-LDSR.git', 'name': 'ComfyUI-Flowty-LDSR'},
        {'repo': 'https://github.com/Kosinkadink/ComfyUI-Advanced-ControlNet.git', 'name': 'ComfyUI-Advanced-ControlNet'},
        {'repo': 'https://github.com/Acly/comfyui-inpaint-nodes.git', 'name': 'comfyui-inpaint-nodes'},
        {'repo': 'https://github.com/Fannovel16/ComfyUI-Frame-Interpolation.git', 'name': 'ComfyUI-Frame-Interpolation'},
        {'repo': 'https://github.com/Kosinkadink/ComfyUI-FluxNodes.git', 'name': 'ComfyUI-FluxNodes'}
    ]
    
    # Clone or update each repository
    for node in required_nodes:
        node_path = os.path.join(custom_nodes_dir, node['name'])
        
        if os.path.exists(node_path):
            print(f"Updating {node['name']}...")
            try:
                subprocess.run(['git', 'pull'], cwd=node_path, check=True)
            except subprocess.CalledProcessError:
                print(f"Failed to update {node['name']}. Skipping.")
        else:
            print(f"Cloning {node['name']}...")
            try:
                subprocess.run(['git', 'clone', node['repo'], node_path], check=True)
            except subprocess.CalledProcessError:
                print(f"Failed to clone {node['name']}. Skipping.")
    
    print("\nCustom nodes installation complete!")
    print("Please restart ComfyUI to load the new nodes.")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Install required ComfyUI custom nodes for mickmumpitz\'s workflows')
    parser.add_argument('--comfyui-path', type=str, required=True,
                        help='Path to the ComfyUI installation')
    args = parser.parse_args()
    
    # Check if the ComfyUI path exists
    comfyui_path = args.comfyui_path
    if not os.path.exists(comfyui_path):
        print(f"Error: ComfyUI path not found: {comfyui_path}")
        sys.exit(1)
    
    # Check if it's a valid ComfyUI installation
    main_py = os.path.join(comfyui_path, 'main.py')
    if not os.path.exists(main_py):
        print(f"Error: Not a valid ComfyUI installation. main.py not found in {comfyui_path}")
        sys.exit(1)
    
    # Install custom nodes
    install_custom_nodes(comfyui_path)

if __name__ == '__main__':
    main()
