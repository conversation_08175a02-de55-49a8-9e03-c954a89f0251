<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Character Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }

        .dropzone {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(102, 126, 234, 0.05);
            margin-bottom: 1rem;
        }

        .dropzone:hover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.1);
        }

        .dropzone.dragover {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-full {
            width: 100%;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            border-radius: 10px;
            width: 0%;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-queued { background-color: #ffc107; color: #212529; }
        .status-processing { background-color: #17a2b8; color: white; }
        .status-completed { background-color: #28a745; color: white; }
        .status-error { background-color: #dc3545; color: white; }

        .preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .job-list {
            display: grid;
            gap: 1rem;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }

        .job-card {
            background: rgba(102, 126, 234, 0.05);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .job-card:hover {
            border-color: rgba(102, 126, 234, 0.3);
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .hidden {
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .download-section {
            background: rgba(40, 167, 69, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            border: 2px solid rgba(40, 167, 69, 0.2);
            margin-top: 1rem;
        }

        .download-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            margin: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 3D Character Generator</h1>
            <p>Create high-quality 3D characters for video games using AI</p>
        </div>

        <!-- Character Generation Form -->
        <div class="card">
            <h2>Generate Your Character</h2>
            <form id="characterForm">
                <div class="form-group">
                    <label class="form-label">Upload Reference Image (Optional)</label>
                    <div class="dropzone" id="dropzone">
                        <p>Drag & drop an image here, or click to select</p>
                        <p style="font-size: 0.9rem; color: #666;">Supports PNG, JPG, JPEG, GIF, BMP</p>
                        <input type="file" id="fileInput" accept="image/*" style="display: none;">
                        <img id="previewImage" class="preview-image hidden" alt="Preview">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Character Description</label>
                    <textarea
                        id="textPrompt"
                        class="form-control"
                        placeholder="Describe your character... (e.g., 'A brave knight with silver armor and a red cape')"
                        required
                    ></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">Style Options</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                        <label><input type="checkbox" id="realistic"> Realistic</label>
                        <label><input type="checkbox" id="cartoon" checked> Cartoon</label>
                        <label><input type="checkbox" id="anime"> Anime</label>
                        <label><input type="checkbox" id="lowPoly"> Low Poly</label>
                    </div>
                </div>

                <button type="submit" class="btn btn-full" id="generateBtn">
                    Generate 3D Character
                </button>
            </form>
        </div>

        <!-- Progress Section -->
        <div class="card hidden" id="progressSection">
            <h3>Generation Progress</h3>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <span id="jobId">Job ID: </span>
                <span class="status-badge" id="statusBadge">Queued</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="progressText">0% complete</p>
            <div id="completionMessage" class="hidden">
                <p style="color: #28a745; font-weight: 600;">✅ Character generation completed!</p>
                <div class="download-section">
                    <h4>Download Your Character</h4>
                    <p>Your 3D character is ready! Download the files below:</p>
                    <button class="btn download-btn" onclick="downloadFile('model')">Download 3D Model (.glb)</button>
                    <button class="btn download-btn" onclick="downloadFile('texture')">Download Texture (.png)</button>
                    <button class="btn download-btn" onclick="downloadFile('animation')">Download Animation (.fbx)</button>
                </div>
            </div>
        </div>

        <!-- Jobs List -->
        <div class="card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h3>Your Jobs</h3>
                <button class="btn" onclick="loadJobs()">Refresh</button>
            </div>
            <div id="jobsList" class="job-list">
                <p>No jobs found. Generate your first character!</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        let currentJob = null;
        let uploadedFilePath = null;

        // File upload handling
        const dropzone = document.getElementById('dropzone');
        const fileInput = document.getElementById('fileInput');
        const previewImage = document.getElementById('previewImage');

        dropzone.addEventListener('click', () => fileInput.click());
        dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropzone.classList.add('dragover');
        });
        dropzone.addEventListener('dragleave', () => {
            dropzone.classList.remove('dragover');
        });
        dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropzone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileUpload(e.target.files[0]);
            }
        });

        async function handleFileUpload(file) {
            console.log('Starting file upload:', file.name, file.size, file.type);

            // Validate file size (16MB limit)
            if (file.size > 16 * 1024 * 1024) {
                alert('File is too large. Maximum size is 16MB.');
                return;
            }

            // Validate file type
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp'];
            if (!allowedTypes.includes(file.type)) {
                alert('Invalid file type. Please upload PNG, JPG, JPEG, GIF, or BMP files.');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                console.log('Sending upload request to:', `${API_BASE}/api/upload`);

                const response = await fetch(`${API_BASE}/api/upload`, {
                    method: 'POST',
                    body: formData
                });

                console.log('Upload response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('Upload result:', result);

                    uploadedFilePath = result.processed_path;

                    // Show preview
                    previewImage.src = URL.createObjectURL(file);
                    previewImage.classList.remove('hidden');

                    alert('Image uploaded successfully!');
                } else {
                    const errorText = await response.text();
                    console.error('Upload failed:', response.status, errorText);
                    alert(`Failed to upload image: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                console.error('Upload error:', error);
                alert(`Error uploading image: ${error.message}`);
            }
        }

        // Form submission
        document.getElementById('characterForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const textPrompt = document.getElementById('textPrompt').value;
            const styleOptions = {
                realistic: document.getElementById('realistic').checked,
                cartoon: document.getElementById('cartoon').checked,
                anime: document.getElementById('anime').checked,
                lowPoly: document.getElementById('lowPoly').checked
            };

            if (!textPrompt && !uploadedFilePath) {
                alert('Please provide either a text prompt or upload an image');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text_prompt: textPrompt,
                        image_path: uploadedFilePath || '',
                        style_options: styleOptions
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    currentJob = result;

                    // Show progress section
                    document.getElementById('progressSection').classList.remove('hidden');
                    document.getElementById('jobId').textContent = `Job ID: ${result.job_id.slice(0, 8)}`;

                    // Start polling for updates
                    pollJobStatus(result.job_id);

                    alert('Character generation started!');
                } else {
                    alert('Failed to start character generation');
                }
            } catch (error) {
                console.error('Generation error:', error);
                alert('Error starting character generation');
            }
        });

        // Poll job status
        async function pollJobStatus(jobId) {
            try {
                const response = await fetch(`${API_BASE}/api/status/${jobId}`);
                if (response.ok) {
                    const job = await response.json();
                    updateProgress(job);

                    if (job.status === 'completed') {
                        currentJob = job;
                        document.getElementById('completionMessage').classList.remove('hidden');
                    } else if (job.status === 'error') {
                        alert(`Error: ${job.error}`);
                    } else {
                        // Continue polling
                        setTimeout(() => pollJobStatus(jobId), 2000);
                    }
                }
            } catch (error) {
                console.error('Status check error:', error);
            }
        }

        // Update progress display
        function updateProgress(job) {
            const statusBadge = document.getElementById('statusBadge');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            statusBadge.textContent = job.status;
            statusBadge.className = `status-badge status-${job.status}`;

            const progress = job.progress || 0;
            progressFill.style.width = `${progress}%`;
            progressText.textContent = `${progress}% complete`;
        }

        // Download files
        async function downloadFile(fileType) {
            if (!currentJob || currentJob.status !== 'completed') {
                alert('No completed character to download');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/download/${currentJob.job_id}/${fileType}`);
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;

                    let filename = `character_${currentJob.job_id.slice(0, 8)}`;
                    switch (fileType) {
                        case 'model': filename += '.glb'; break;
                        case 'texture': filename += '_texture.png'; break;
                        case 'animation': filename += '_idle.fbx'; break;
                    }

                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    alert(`${fileType} downloaded successfully!`);
                } else {
                    alert(`Failed to download ${fileType}`);
                }
            } catch (error) {
                console.error('Download error:', error);
                alert(`Error downloading ${fileType}`);
            }
        }

        // Load jobs list
        async function loadJobs() {
            try {
                const response = await fetch(`${API_BASE}/api/jobs`);
                if (response.ok) {
                    const data = await response.json();
                    const jobs = data.jobs || [];
                    displayJobs(jobs);
                }
            } catch (error) {
                console.error('Error loading jobs:', error);
            }
        }

        // Display jobs
        function displayJobs(jobs) {
            const jobsList = document.getElementById('jobsList');

            if (jobs.length === 0) {
                jobsList.innerHTML = '<p>No jobs found. Generate your first character!</p>';
                return;
            }

            jobsList.innerHTML = jobs.map(job => `
                <div class="job-card" onclick="selectJob('${job.id}')">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <span style="font-family: monospace; font-size: 0.9rem;">${job.id.slice(0, 8)}</span>
                        <span class="status-badge status-${job.status}">${job.status}</span>
                    </div>
                    ${job.text_prompt ? `<p style="font-style: italic; margin: 0.5rem 0;">"${job.text_prompt}"</p>` : ''}
                    <p style="font-size: 0.85rem; color: #666;">Created: ${new Date(job.created_at).toLocaleString()}</p>
                    ${job.status !== 'completed' && job.status !== 'error' ? `
                        <div class="progress-bar" style="height: 8px; margin: 0.5rem 0;">
                            <div class="progress-fill" style="width: ${job.progress || 0}%;"></div>
                        </div>
                        <p style="font-size: 0.8rem; color: #666;">${job.progress || 0}% complete</p>
                    ` : ''}
                </div>
            `).join('');
        }

        // Select job
        async function selectJob(jobId) {
            try {
                const response = await fetch(`${API_BASE}/api/status/${jobId}`);
                if (response.ok) {
                    const job = await response.json();
                    currentJob = job;

                    if (job.status === 'completed') {
                        document.getElementById('progressSection').classList.remove('hidden');
                        document.getElementById('jobId').textContent = `Job ID: ${job.id.slice(0, 8)}`;
                        updateProgress(job);
                        document.getElementById('completionMessage').classList.remove('hidden');
                    } else {
                        document.getElementById('progressSection').classList.remove('hidden');
                        document.getElementById('jobId').textContent = `Job ID: ${job.id.slice(0, 8)}`;
                        updateProgress(job);
                        document.getElementById('completionMessage').classList.add('hidden');

                        if (job.status !== 'error') {
                            pollJobStatus(job.id);
                        }
                    }
                }
            } catch (error) {
                console.error('Error selecting job:', error);
            }
        }

        // Load jobs on page load
        loadJobs();
    </script>
</body>
</html>
