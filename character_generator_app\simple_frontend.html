<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Character Generator</title>
    <!-- Three.js for 3D rendering -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/FBXLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/libs/fflate.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }

        .dropzone {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(102, 126, 234, 0.05);
            margin-bottom: 1rem;
        }

        .dropzone:hover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.1);
        }

        .dropzone.dragover {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-full {
            width: 100%;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            border-radius: 10px;
            width: 0%;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-queued { background-color: #ffc107; color: #212529; }
        .status-processing { background-color: #17a2b8; color: white; }
        .status-completed { background-color: #28a745; color: white; }
        .status-error { background-color: #dc3545; color: white; }

        .preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .job-list {
            display: grid;
            gap: 1rem;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }

        .job-card {
            background: rgba(102, 126, 234, 0.05);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .job-card:hover {
            border-color: rgba(102, 126, 234, 0.3);
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .hidden {
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .download-section {
            background: rgba(40, 167, 69, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            border: 2px solid rgba(40, 167, 69, 0.2);
            margin-top: 1rem;
        }

        .download-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            margin: 0.25rem;
        }

        /* 3D Viewer Styles */
        .viewer-container {
            width: 100%;
            height: 500px;
            border-radius: 10px;
            overflow: hidden;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin-bottom: 2rem;
            position: relative;
        }

        .viewer-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .control-panel {
            background: rgba(102, 126, 234, 0.1);
            padding: 1rem;
            border-radius: 8px;
        }

        .control-group {
            margin-bottom: 1rem;
        }

        .control-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .slider {
            width: 100%;
            margin: 0.5rem 0;
            -webkit-appearance: none;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }

        .slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }

        .animation-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .animation-btn {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e0e0e0;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .animation-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }

        .animation-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .viewer-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
            z-index: 100;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            z-index: 200;
        }

        .model-stats {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .model-stats h4 {
            color: white;
            margin-bottom: 0.5rem;
        }

        .model-stats p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0.25rem 0;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 3D Character Generator</h1>
            <p>Create high-quality 3D characters for video games using AI</p>
        </div>

        <!-- Character Generation Form -->
        <div class="card">
            <h2>Generate Your Character</h2>
            <form id="characterForm">
                <div class="form-group">
                    <label class="form-label">Upload Reference Image (Optional)</label>
                    <div class="dropzone" id="dropzone">
                        <p>Drag & drop an image here, or click to select</p>
                        <p style="font-size: 0.9rem; color: #666;">Supports PNG, JPG, JPEG, GIF, BMP</p>
                        <input type="file" id="fileInput" accept="image/*" style="display: none;">
                        <img id="previewImage" class="preview-image hidden" alt="Preview">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Character Description</label>
                    <textarea
                        id="textPrompt"
                        class="form-control"
                        placeholder="Describe your character... (e.g., 'A brave knight with silver armor and a red cape')"
                        required
                    ></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">Style Options</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                        <label><input type="checkbox" id="realistic"> Realistic</label>
                        <label><input type="checkbox" id="cartoon" checked> Cartoon</label>
                        <label><input type="checkbox" id="anime"> Anime</label>
                        <label><input type="checkbox" id="lowPoly"> Low Poly</label>
                    </div>
                </div>

                <button type="submit" class="btn btn-full" id="generateBtn">
                    Generate 3D Character
                </button>
            </form>
        </div>

        <!-- Progress Section -->
        <div class="card hidden" id="progressSection">
            <h3>Generation Progress</h3>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <span id="jobId">Job ID: </span>
                <span class="status-badge" id="statusBadge">Queued</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="progressText">0% complete</p>
            <div id="completionMessage" class="hidden">
                <p style="color: #28a745; font-weight: 600;">✅ Character generation completed!</p>

                <!-- 3D Viewer Section -->
                <div class="card" style="background: rgba(255, 255, 255, 0.1); margin: 1rem 0;">
                    <h4 style="color: white; margin-bottom: 1rem;">🎮 3D Character Viewer</h4>

                    <div class="viewer-container" id="viewerContainer">
                        <div class="loading-overlay" id="loadingOverlay">
                            <div>
                                <div class="loading"></div>
                                <p>Loading 3D model...</p>
                            </div>
                        </div>
                        <div class="viewer-info" id="viewerInfo">
                            Use mouse to rotate, zoom, and pan
                        </div>
                    </div>

                    <div class="viewer-controls">
                        <div class="control-panel">
                            <h5 style="margin-bottom: 1rem; color: #333;">Transform Controls</h5>

                            <div class="control-group">
                                <label class="control-label">Position X</label>
                                <input type="range" class="slider" id="positionX" min="-5" max="5" step="0.1" value="0">
                                <span id="positionXValue">0</span>
                            </div>

                            <div class="control-group">
                                <label class="control-label">Position Y</label>
                                <input type="range" class="slider" id="positionY" min="-5" max="5" step="0.1" value="0">
                                <span id="positionYValue">0</span>
                            </div>

                            <div class="control-group">
                                <label class="control-label">Position Z</label>
                                <input type="range" class="slider" id="positionZ" min="-5" max="5" step="0.1" value="0">
                                <span id="positionZValue">0</span>
                            </div>

                            <div class="control-group">
                                <label class="control-label">Scale</label>
                                <input type="range" class="slider" id="scale" min="0.1" max="3" step="0.1" value="1">
                                <span id="scaleValue">1</span>
                            </div>
                        </div>

                        <div class="control-panel">
                            <h5 style="margin-bottom: 1rem; color: #333;">Animation Controls</h5>

                            <div class="control-group">
                                <label class="control-label">Animations</label>
                                <div class="animation-controls">
                                    <button class="animation-btn active" onclick="setAnimation('idle')">Idle</button>
                                    <button class="animation-btn" onclick="setAnimation('rotate')">Rotate</button>
                                    <button class="animation-btn" onclick="setAnimation('bounce')">Bounce</button>
                                    <button class="animation-btn" onclick="setAnimation('none')">Static</button>
                                </div>
                            </div>

                            <div class="control-group">
                                <label class="control-label">Animation Speed</label>
                                <input type="range" class="slider" id="animationSpeed" min="0.1" max="3" step="0.1" value="1">
                                <span id="animationSpeedValue">1</span>
                            </div>
                        </div>

                        <div class="control-panel">
                            <h5 style="margin-bottom: 1rem; color: #333;">Lighting</h5>

                            <div class="control-group">
                                <label class="control-label">Ambient Light</label>
                                <input type="range" class="slider" id="ambientLight" min="0" max="2" step="0.1" value="0.4">
                                <span id="ambientLightValue">0.4</span>
                            </div>

                            <div class="control-group">
                                <label class="control-label">Directional Light</label>
                                <input type="range" class="slider" id="directionalLight" min="0" max="3" step="0.1" value="1">
                                <span id="directionalLightValue">1</span>
                            </div>

                            <div class="control-group">
                                <button class="btn" onclick="resetView()" style="width: 100%; margin-top: 0.5rem;">Reset View</button>
                            </div>
                        </div>
                    </div>

                    <div class="model-stats" id="modelStats" style="display: none;">
                        <h4>Model Information</h4>
                        <p id="modelVertices">Vertices: -</p>
                        <p id="modelFaces">Faces: -</p>
                        <p id="modelMaterials">Materials: -</p>
                        <p id="modelAnimations">Animations: -</p>
                    </div>
                </div>

                <div class="download-section">
                    <h4>Download Your Character</h4>
                    <p>Your 3D character is ready! Download the files below:</p>
                    <button class="btn download-btn" onclick="downloadFile('model')">Download 3D Model (.glb)</button>
                    <button class="btn download-btn" onclick="downloadFile('texture')">Download Texture (.png)</button>
                    <button class="btn download-btn" onclick="downloadFile('animation')">Download Animation (.fbx)</button>
                    <button class="btn download-btn" onclick="load3DModel()">🎮 Load in 3D Viewer</button>
                </div>
            </div>
        </div>

        <!-- Jobs List -->
        <div class="card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h3>Your Jobs</h3>
                <button class="btn" onclick="loadJobs()">Refresh</button>
            </div>
            <div id="jobsList" class="job-list">
                <p>No jobs found. Generate your first character!</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        let currentJob = null;
        let uploadedFilePath = null;

        // File upload handling
        const dropzone = document.getElementById('dropzone');
        const fileInput = document.getElementById('fileInput');
        const previewImage = document.getElementById('previewImage');

        dropzone.addEventListener('click', () => fileInput.click());
        dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropzone.classList.add('dragover');
        });
        dropzone.addEventListener('dragleave', () => {
            dropzone.classList.remove('dragover');
        });
        dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropzone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileUpload(e.target.files[0]);
            }
        });

        async function handleFileUpload(file) {
            console.log('Starting file upload:', file.name, file.size, file.type);

            // Validate file size (16MB limit)
            if (file.size > 16 * 1024 * 1024) {
                alert('File is too large. Maximum size is 16MB.');
                return;
            }

            // Validate file type
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp'];
            if (!allowedTypes.includes(file.type)) {
                alert('Invalid file type. Please upload PNG, JPG, JPEG, GIF, or BMP files.');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                console.log('Sending upload request to:', `${API_BASE}/api/upload`);

                const response = await fetch(`${API_BASE}/api/upload`, {
                    method: 'POST',
                    body: formData
                });

                console.log('Upload response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('Upload result:', result);

                    uploadedFilePath = result.processed_path;

                    // Show preview
                    previewImage.src = URL.createObjectURL(file);
                    previewImage.classList.remove('hidden');

                    alert('Image uploaded successfully!');
                } else {
                    const errorText = await response.text();
                    console.error('Upload failed:', response.status, errorText);
                    alert(`Failed to upload image: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                console.error('Upload error:', error);
                alert(`Error uploading image: ${error.message}`);
            }
        }

        // Form submission
        document.getElementById('characterForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const textPrompt = document.getElementById('textPrompt').value;
            const styleOptions = {
                realistic: document.getElementById('realistic').checked,
                cartoon: document.getElementById('cartoon').checked,
                anime: document.getElementById('anime').checked,
                lowPoly: document.getElementById('lowPoly').checked
            };

            if (!textPrompt && !uploadedFilePath) {
                alert('Please provide either a text prompt or upload an image');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text_prompt: textPrompt,
                        image_path: uploadedFilePath || '',
                        style_options: styleOptions
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    currentJob = result;

                    // Show progress section
                    document.getElementById('progressSection').classList.remove('hidden');
                    document.getElementById('jobId').textContent = `Job ID: ${result.job_id.slice(0, 8)}`;

                    // Start polling for updates
                    pollJobStatus(result.job_id);

                    alert('Character generation started!');
                } else {
                    alert('Failed to start character generation');
                }
            } catch (error) {
                console.error('Generation error:', error);
                alert('Error starting character generation');
            }
        });

        // Poll job status
        async function pollJobStatus(jobId) {
            try {
                const response = await fetch(`${API_BASE}/api/status/${jobId}`);
                if (response.ok) {
                    const job = await response.json();
                    updateProgress(job);

                    if (job.status === 'completed') {
                        currentJob = job;
                        document.getElementById('completionMessage').classList.remove('hidden');
                    } else if (job.status === 'error') {
                        alert(`Error: ${job.error}`);
                    } else {
                        // Continue polling
                        setTimeout(() => pollJobStatus(jobId), 2000);
                    }
                }
            } catch (error) {
                console.error('Status check error:', error);
            }
        }

        // Update progress display
        function updateProgress(job) {
            const statusBadge = document.getElementById('statusBadge');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            statusBadge.textContent = job.status;
            statusBadge.className = `status-badge status-${job.status}`;

            const progress = job.progress || 0;
            progressFill.style.width = `${progress}%`;
            progressText.textContent = `${progress}% complete`;
        }

        // Download files
        async function downloadFile(fileType) {
            if (!currentJob || currentJob.status !== 'completed') {
                alert('No completed character to download');
                return;
            }

            try {
                // Use the correct job ID field
                const jobId = currentJob.id || currentJob.job_id;
                if (!jobId) {
                    alert('Invalid job ID');
                    return;
                }

                console.log('Downloading file:', fileType, 'for job:', jobId);
                const response = await fetch(`${API_BASE}/api/download/${jobId}/${fileType}`);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;

                    let filename = `character_${jobId.slice(0, 8)}`;
                    switch (fileType) {
                        case 'model': filename += '.glb'; break;
                        case 'texture': filename += '_texture.png'; break;
                        case 'animation': filename += '_idle.fbx'; break;
                    }

                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    alert(`${fileType} downloaded successfully!`);
                } else {
                    const errorText = await response.text();
                    console.error('Download failed:', response.status, errorText);
                    alert(`Failed to download ${fileType}: ${response.status}`);
                }
            } catch (error) {
                console.error('Download error:', error);
                alert(`Error downloading ${fileType}: ${error.message}`);
            }
        }

        // Load jobs list
        async function loadJobs() {
            try {
                const response = await fetch(`${API_BASE}/api/jobs`);
                if (response.ok) {
                    const data = await response.json();
                    const jobs = data.jobs || [];
                    displayJobs(jobs);
                }
            } catch (error) {
                console.error('Error loading jobs:', error);
            }
        }

        // Display jobs
        function displayJobs(jobs) {
            const jobsList = document.getElementById('jobsList');

            // Ensure jobs is an array
            if (!Array.isArray(jobs)) {
                console.error('Jobs is not an array:', jobs);
                jobsList.innerHTML = '<p>Error loading jobs. Please refresh.</p>';
                return;
            }

            if (jobs.length === 0) {
                jobsList.innerHTML = '<p>No jobs found. Generate your first character!</p>';
                return;
            }

            jobsList.innerHTML = jobs.map(job => {
                // Safely access job properties
                const jobId = job.id || job.job_id || 'unknown';
                const status = job.status || 'unknown';
                const textPrompt = job.text_prompt || '';
                const createdAt = job.created_at || '';
                const progress = job.progress || 0;

                return `
                    <div class="job-card" onclick="selectJob('${jobId}')">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <span style="font-family: monospace; font-size: 0.9rem;">${jobId.slice(0, 8)}</span>
                            <span class="status-badge status-${status}">${status}</span>
                        </div>
                        ${textPrompt ? `<p style="font-style: italic; margin: 0.5rem 0;">"${textPrompt}"</p>` : ''}
                        <p style="font-size: 0.85rem; color: #666;">Created: ${createdAt ? new Date(createdAt).toLocaleString() : 'Unknown'}</p>
                        ${status !== 'completed' && status !== 'error' ? `
                            <div class="progress-bar" style="height: 8px; margin: 0.5rem 0;">
                                <div class="progress-fill" style="width: ${progress}%;"></div>
                            </div>
                            <p style="font-size: 0.8rem; color: #666;">${progress}% complete</p>
                        ` : ''}
                    </div>
                `;
            }).join('');
        }

        // Select job
        async function selectJob(jobId) {
            try {
                console.log('Selecting job:', jobId);
                const response = await fetch(`${API_BASE}/api/status/${jobId}`);
                if (response.ok) {
                    const job = await response.json();
                    console.log('Job data:', job);
                    currentJob = job;

                    // Use the correct job ID field
                    const displayJobId = job.id || job.job_id || jobId;

                    if (job.status === 'completed') {
                        document.getElementById('progressSection').classList.remove('hidden');
                        document.getElementById('jobId').textContent = `Job ID: ${displayJobId.slice(0, 8)}`;
                        updateProgress(job);
                        document.getElementById('completionMessage').classList.remove('hidden');
                    } else {
                        document.getElementById('progressSection').classList.remove('hidden');
                        document.getElementById('jobId').textContent = `Job ID: ${displayJobId.slice(0, 8)}`;
                        updateProgress(job);
                        document.getElementById('completionMessage').classList.add('hidden');

                        if (job.status !== 'error') {
                            pollJobStatus(displayJobId);
                        }
                    }
                } else {
                    console.error('Failed to get job status:', response.status);
                    alert('Failed to load job details');
                }
            } catch (error) {
                console.error('Error selecting job:', error);
                alert('Error loading job details');
            }
        }

        // Load jobs on page load
        loadJobs();

        // 3D Viewer Variables
        let scene, camera, renderer, controls;
        let currentModel = null;
        let mixer = null;
        let clock = new THREE.Clock();
        let currentAnimation = 'idle';
        let animationSpeed = 1;
        let ambientLight, directionalLight;

        // Initialize 3D Viewer
        function init3DViewer() {
            const container = document.getElementById('viewerContainer');

            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x1e3c72);

            // Camera
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.set(3, 2, 3);

            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.outputEncoding = THREE.sRGBEncoding;
            renderer.toneMapping = THREE.ACESFilmicToneMapping;
            renderer.toneMappingExposure = 1;

            // Clear any existing canvas
            const existingCanvas = container.querySelector('canvas');
            if (existingCanvas) {
                container.removeChild(existingCanvas);
            }

            container.appendChild(renderer.domElement);

            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.screenSpacePanning = false;
            controls.minDistance = 1;
            controls.maxDistance = 20;
            controls.maxPolarAngle = Math.PI;

            // Lights
            ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);

            directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // Ground plane
            const groundGeometry = new THREE.PlaneGeometry(20, 20);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x999999 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -2;
            ground.receiveShadow = true;
            scene.add(ground);

            // Setup control listeners
            setupControlListeners();

            // Start animation loop
            animate();

            console.log('3D Viewer initialized');
        }

        // Setup control event listeners
        function setupControlListeners() {
            // Position controls
            document.getElementById('positionX').addEventListener('input', updateModelTransform);
            document.getElementById('positionY').addEventListener('input', updateModelTransform);
            document.getElementById('positionZ').addEventListener('input', updateModelTransform);
            document.getElementById('scale').addEventListener('input', updateModelTransform);

            // Animation speed
            document.getElementById('animationSpeed').addEventListener('input', (e) => {
                animationSpeed = parseFloat(e.target.value);
                document.getElementById('animationSpeedValue').textContent = animationSpeed;
            });

            // Lighting controls
            document.getElementById('ambientLight').addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                ambientLight.intensity = value;
                document.getElementById('ambientLightValue').textContent = value;
            });

            document.getElementById('directionalLight').addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                directionalLight.intensity = value;
                document.getElementById('directionalLightValue').textContent = value;
            });
        }

        // Update model transform based on controls
        function updateModelTransform() {
            if (!currentModel) return;

            const posX = parseFloat(document.getElementById('positionX').value);
            const posY = parseFloat(document.getElementById('positionY').value);
            const posZ = parseFloat(document.getElementById('positionZ').value);
            const scale = parseFloat(document.getElementById('scale').value);

            currentModel.position.set(posX, posY, posZ);
            currentModel.scale.set(scale, scale, scale);

            // Update display values
            document.getElementById('positionXValue').textContent = posX;
            document.getElementById('positionYValue').textContent = posY;
            document.getElementById('positionZValue').textContent = posZ;
            document.getElementById('scaleValue').textContent = scale;
        }

        // Set animation type
        function setAnimation(type) {
            currentAnimation = type;

            // Update button states
            document.querySelectorAll('.animation-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            console.log('Animation set to:', type);
        }

        // Reset view to default
        function resetView() {
            if (camera && controls) {
                camera.position.set(3, 2, 3);
                controls.reset();
            }

            // Reset controls
            document.getElementById('positionX').value = 0;
            document.getElementById('positionY').value = 0;
            document.getElementById('positionZ').value = 0;
            document.getElementById('scale').value = 1;
            document.getElementById('animationSpeed').value = 1;
            document.getElementById('ambientLight').value = 0.4;
            document.getElementById('directionalLight').value = 1;

            updateModelTransform();

            // Reset lighting
            if (ambientLight) ambientLight.intensity = 0.4;
            if (directionalLight) directionalLight.intensity = 1;

            // Update display values
            document.getElementById('positionXValue').textContent = '0';
            document.getElementById('positionYValue').textContent = '0';
            document.getElementById('positionZValue').textContent = '0';
            document.getElementById('scaleValue').textContent = '1';
            document.getElementById('animationSpeedValue').textContent = '1';
            document.getElementById('ambientLightValue').textContent = '0.4';
            document.getElementById('directionalLightValue').textContent = '1';
        }

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);

            const delta = clock.getDelta();

            // Update mixer for GLB animations
            if (mixer) {
                mixer.update(delta * animationSpeed);
            }

            // Custom animations
            if (currentModel) {
                switch (currentAnimation) {
                    case 'rotate':
                        currentModel.rotation.y += 0.01 * animationSpeed;
                        break;
                    case 'bounce':
                        const originalY = parseFloat(document.getElementById('positionY').value);
                        currentModel.position.y = originalY + Math.sin(Date.now() * 0.002 * animationSpeed) * 0.3;
                        break;
                    case 'idle':
                        currentModel.rotation.y = Math.sin(Date.now() * 0.001 * animationSpeed) * 0.1;
                        break;
                }
            }

            if (controls) controls.update();
            if (renderer && scene && camera) renderer.render(scene, camera);
        }

        // Load 3D model
        async function load3DModel() {
            if (!currentJob || currentJob.status !== 'completed') {
                alert('No completed character to load');
                return;
            }

            const jobId = currentJob.id || currentJob.job_id;
            if (!jobId) {
                alert('Invalid job ID');
                return;
            }

            // Initialize viewer if not already done
            if (!renderer) {
                init3DViewer();
            }

            // Show loading overlay
            document.getElementById('loadingOverlay').style.display = 'flex';

            try {
                console.log('Loading 3D model for job:', jobId);

                // Download the GLB file
                const response = await fetch(`${API_BASE}/api/download/${jobId}/model`);

                if (!response.ok) {
                    throw new Error(`Failed to download model: ${response.status}`);
                }

                const blob = await response.blob();
                const url = URL.createObjectURL(blob);

                // Load with GLTFLoader
                const loader = new THREE.GLTFLoader();

                loader.load(url, (gltf) => {
                    console.log('Model loaded successfully:', gltf);

                    // Remove existing model
                    if (currentModel) {
                        scene.remove(currentModel);
                    }

                    currentModel = gltf.scene;

                    // Setup model
                    currentModel.traverse((child) => {
                        if (child.isMesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                        }
                    });

                    // Center and scale model
                    const box = new THREE.Box3().setFromObject(currentModel);
                    const center = box.getCenter(new THREE.Vector3());
                    const size = box.getSize(new THREE.Vector3());

                    // Center the model
                    currentModel.position.sub(center);

                    // Scale to reasonable size
                    const maxDim = Math.max(size.x, size.y, size.z);
                    if (maxDim > 0) {
                        const scale = 2 / maxDim;
                        currentModel.scale.setScalar(scale);
                    }

                    scene.add(currentModel);

                    // Setup animations if available
                    if (gltf.animations && gltf.animations.length > 0) {
                        mixer = new THREE.AnimationMixer(currentModel);
                        gltf.animations.forEach((clip) => {
                            const action = mixer.clipAction(clip);
                            action.play();
                        });
                        console.log('Animations loaded:', gltf.animations.length);
                    }

                    // Update model stats
                    updateModelStats(gltf);

                    // Hide loading overlay
                    document.getElementById('loadingOverlay').style.display = 'none';

                    // Clean up blob URL
                    URL.revokeObjectURL(url);

                    alert('3D model loaded successfully!');

                }, (progress) => {
                    console.log('Loading progress:', progress);
                }, (error) => {
                    console.error('Error loading model:', error);
                    document.getElementById('loadingOverlay').style.display = 'none';

                    // Load fallback model
                    loadFallbackModel();
                });

            } catch (error) {
                console.error('Error loading 3D model:', error);
                document.getElementById('loadingOverlay').style.display = 'none';

                // Load fallback model
                loadFallbackModel();
            }
        }

        // Load a fallback 3D model (simple geometry)
        function loadFallbackModel() {
            console.log('Loading fallback model...');

            // Initialize viewer if not already done
            if (!renderer) {
                init3DViewer();
            }

            // Remove existing model
            if (currentModel) {
                scene.remove(currentModel);
            }

            // Create a simple character-like model
            const group = new THREE.Group();

            // Body
            const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.2, 8);
            const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.y = 0;
            body.castShadow = true;
            group.add(body);

            // Head
            const headGeometry = new THREE.SphereGeometry(0.25, 16, 16);
            const headMaterial = new THREE.MeshLambertMaterial({ color: 0xf4c2a1 });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.y = 0.8;
            head.castShadow = true;
            group.add(head);

            // Arms
            const armGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.8, 8);
            const armMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 });

            const leftArm = new THREE.Mesh(armGeometry, armMaterial);
            leftArm.position.set(-0.5, 0.2, 0);
            leftArm.castShadow = true;
            group.add(leftArm);

            const rightArm = new THREE.Mesh(armGeometry, armMaterial);
            rightArm.position.set(0.5, 0.2, 0);
            rightArm.castShadow = true;
            group.add(rightArm);

            // Legs
            const legGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.8, 8);
            const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2c3e50 });

            const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
            leftLeg.position.set(-0.15, -0.8, 0);
            leftLeg.castShadow = true;
            group.add(leftLeg);

            const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
            rightLeg.position.set(0.15, -0.8, 0);
            rightLeg.castShadow = true;
            group.add(rightLeg);

            currentModel = group;
            scene.add(currentModel);

            // Update model stats for fallback
            document.getElementById('modelStats').style.display = 'block';
            document.getElementById('modelVertices').textContent = 'Vertices: ~200 (Fallback Model)';
            document.getElementById('modelFaces').textContent = 'Faces: ~150 (Fallback Model)';
            document.getElementById('modelMaterials').textContent = 'Materials: 3 (Fallback Model)';
            document.getElementById('modelAnimations').textContent = 'Animations: Custom (Fallback Model)';

            // Hide loading overlay
            document.getElementById('loadingOverlay').style.display = 'none';

            console.log('Fallback model loaded');
        }

        // Update model statistics
        function updateModelStats(gltf) {
            let vertices = 0;
            let faces = 0;
            let materials = 0;

            gltf.scene.traverse((child) => {
                if (child.isMesh) {
                    if (child.geometry) {
                        if (child.geometry.attributes.position) {
                            vertices += child.geometry.attributes.position.count;
                        }
                        if (child.geometry.index) {
                            faces += child.geometry.index.count / 3;
                        }
                    }
                    if (child.material) {
                        materials++;
                    }
                }
            });

            document.getElementById('modelStats').style.display = 'block';
            document.getElementById('modelVertices').textContent = `Vertices: ${vertices.toLocaleString()}`;
            document.getElementById('modelFaces').textContent = `Faces: ${Math.floor(faces).toLocaleString()}`;
            document.getElementById('modelMaterials').textContent = `Materials: ${materials}`;
            document.getElementById('modelAnimations').textContent = `Animations: ${gltf.animations ? gltf.animations.length : 0}`;
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                const container = document.getElementById('viewerContainer');
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        });
    </script>
</body>
</html>
