#!/usr/bin/env python3
"""
Install ComfyUI 3D Pack for enhanced 3D generation
"""

import os
import subprocess
import sys

def install_comfyui_3d_pack():
    """Install ComfyUI 3D Pack and required nodes."""

    comfyui_path = r"C:\Users\<USER>\OneDrive\Desktop\ComfyUI_windows_portable_nvidia (1)\ComfyUI_windows_portable\ComfyUI"
    custom_nodes_path = os.path.join(comfyui_path, "custom_nodes")

    print("🚀 Installing ComfyUI 3D Pack")
    print("=" * 40)

    # Check if ComfyUI directory exists
    if not os.path.exists(comfyui_path):
        print(f"❌ ComfyUI not found at: {comfyui_path}")
        print("Please verify your ComfyUI installation path")
        return False

    if not os.path.exists(custom_nodes_path):
        print(f"❌ Custom nodes directory not found: {custom_nodes_path}")
        return False

    print(f"✅ ComfyUI found at: {comfyui_path}")
    print(f"✅ Custom nodes directory: {custom_nodes_path}")

    # List of nodes to install
    nodes_to_install = [
        {
            "name": "ComfyUI-Manager",
            "url": "https://github.com/ltdrdata/ComfyUI-Manager.git",
            "description": "Node manager for easy installation"
        },
        {
            "name": "ComfyUI-3D-Pack",
            "url": "https://github.com/MrForExample/ComfyUI-3D-Pack.git",
            "description": "3D generation nodes (Stable Fast 3D, InstantMesh, etc.)"
        }
    ]

    installed_count = 0

    for node in nodes_to_install:
        node_path = os.path.join(custom_nodes_path, node["name"])

        print(f"\n📦 Installing {node['name']}...")
        print(f"   Description: {node['description']}")

        if os.path.exists(node_path):
            print(f"✅ {node['name']} already installed at {node_path}")
            installed_count += 1
        else:
            try:
                print(f"📥 Cloning from {node['url']}...")
                result = subprocess.run([
                    "git", "clone", node["url"], node_path
                ], capture_output=True, text=True, timeout=120)

                if result.returncode == 0:
                    print(f"✅ {node['name']} installed successfully")
                    installed_count += 1
                else:
                    print(f"❌ Failed to install {node['name']}")
                    print(f"Error: {result.stderr}")

            except subprocess.TimeoutExpired:
                print(f"❌ Timeout installing {node['name']}")
            except FileNotFoundError:
                print("❌ Git not found. Please install Git first.")
                print("Download from: https://git-scm.com/download/win")
                return False
            except Exception as e:
                print(f"❌ Error installing {node['name']}: {e}")

    # Summary
    print(f"\n📊 Installation Summary")
    print("=" * 30)
    print(f"✅ {installed_count}/{len(nodes_to_install)} nodes installed")

    if installed_count == len(nodes_to_install):
        print("\n🎉 All nodes installed successfully!")
        print("\n📋 Next Steps:")
        print("1. Restart ComfyUI completely")
        print("2. Wait for it to load (may take longer first time)")
        print("3. Check for new 3D generation nodes")
        print("4. Test enhanced 3D character generation")

        return True
    else:
        print("\n⚠️ Some nodes failed to install")
        print("You can install them manually or try again")
        return False

def check_git_installation():
    """Check if Git is installed."""
    try:
        result = subprocess.run(["git", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Git found: {result.stdout.strip()}")
            return True
        else:
            print("❌ Git not working properly")
            return False
    except FileNotFoundError:
        print("❌ Git not found")
        print("Please install Git from: https://git-scm.com/download/win")
        return False

def main():
    """Main installation function."""
    print("🔧 ComfyUI 3D Pack Installer")
    print("=" * 30)

    # Check prerequisites
    if not check_git_installation():
        print("\n💡 Install Git first, then run this script again")
        input("Press Enter to exit...")
        return

    # Install nodes
    success = install_comfyui_3d_pack()

    if success:
        print("\n🚀 Installation complete!")
        print("Your 3D character generator will now have enhanced capabilities!")
    else:
        print("\n⚠️ Installation had some issues")
        print("Check the errors above and try manual installation if needed")

    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
