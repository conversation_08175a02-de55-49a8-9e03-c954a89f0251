<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 3D Character Generator - Connected Interface</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-full {
            width: 100%;
        }

        .status-section {
            background: rgba(102, 126, 234, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .viewer-container {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 1rem 0;
            position: relative;
        }

        .character-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .character-card {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }

        .character-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .hidden {
            display: none;
        }

        .success {
            color: #28a745;
            font-weight: 600;
        }

        .error {
            color: #dc3545;
            font-weight: 600;
        }

        .info {
            color: #17a2b8;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 3D Character Generator</h1>
            <p>Connected Interface - Real Character Generation</p>
        </div>

        <!-- API Connection Status -->
        <div class="card">
            <h3>🔗 Backend Connection</h3>
            <div class="status-section">
                <p id="connectionStatus" class="info">Testing connection...</p>
                <button onclick="testConnection()" class="btn">Test Connection</button>
            </div>
        </div>

        <!-- Character Generation -->
        <div class="card">
            <h3>🎯 Generate Character</h3>
            
            <div class="form-group">
                <label class="form-label">Character Description</label>
                <textarea
                    id="characterPrompt"
                    class="form-control"
                    placeholder="Describe your character... (e.g., 'A blue robot warrior with silver armor')"
                >A blue robot warrior with silver armor</textarea>
            </div>

            <button onclick="generateCharacter()" class="btn btn-full" id="generateBtn">
                Generate 3D Character
            </button>

            <div id="generationStatus" class="status-section hidden">
                <h4>Generation Status</h4>
                <p id="statusText">Preparing...</p>
                <div id="generationResult"></div>
            </div>
        </div>

        <!-- Generated Characters -->
        <div class="card">
            <h3>📁 Your Generated Characters</h3>
            <div class="character-list" id="characterList">
                <div class="character-card" onclick="loadCharacter('test_character_1_robot.glb')">
                    <h4>🤖 Robot Character</h4>
                    <p>Blue warrior with silver armor</p>
                    <small>14,364 bytes</small>
                </div>
                <div class="character-card" onclick="loadCharacter('test_character_2_alien.glb')">
                    <h4>👽 Alien Character</h4>
                    <p>Mystical purple being</p>
                    <small>14,376 bytes</small>
                </div>
                <div class="character-card" onclick="loadCharacter('test_character_3_warrior.glb')">
                    <h4>⚔️ Warrior Character</h4>
                    <p>Golden armored fighter</p>
                    <small>14,368 bytes</small>
                </div>
                <div class="character-card" onclick="loadCharacter('test_procedural_character.glb')">
                    <h4>🎮 Procedural Character</h4>
                    <p>Latest generated character</p>
                    <small>14,364 bytes</small>
                </div>
            </div>
        </div>

        <!-- 3D Viewer -->
        <div class="card hidden" id="viewerSection">
            <h3>🎮 3D Character Viewer</h3>
            <div class="viewer-container" id="viewerContainer">
                <div id="viewerInfo" style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 0.5rem; border-radius: 5px; font-size: 0.8rem;">
                    Use mouse to rotate, zoom, and pan
                </div>
            </div>
            <div class="status-section">
                <h4>Model Information</h4>
                <div id="modelInfo">
                    <p>Load a character to see details</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let scene, camera, renderer, controls;
        let currentModel = null;

        // Test API connection
        async function testConnection() {
            const statusEl = document.getElementById('connectionStatus');
            statusEl.textContent = 'Testing connection...';
            statusEl.className = 'info';

            try {
                // Try multiple endpoints
                const endpoints = [
                    'http://localhost:5000/api/test',
                    'http://localhost:5000/',
                    'http://127.0.0.1:5000/api/test'
                ];

                let connected = false;
                let lastError = '';

                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(endpoint, {
                            method: 'GET',
                            mode: 'cors'
                        });

                        if (response.ok) {
                            const data = await response.json();
                            statusEl.textContent = `✅ Connected to backend! (${endpoint})`;
                            statusEl.className = 'success';
                            connected = true;
                            break;
                        }
                    } catch (e) {
                        lastError = e.message;
                        continue;
                    }
                }

                if (!connected) {
                    statusEl.textContent = `❌ Backend not available. Error: ${lastError}`;
                    statusEl.className = 'error';
                    
                    // Show fallback options
                    statusEl.innerHTML += '<br><br>💡 <strong>Options:</strong><br>' +
                        '1. Start the backend server manually<br>' +
                        '2. Use the existing generated characters<br>' +
                        '3. Generate characters via Python script';
                }

            } catch (error) {
                statusEl.textContent = `❌ Connection failed: ${error.message}`;
                statusEl.className = 'error';
            }
        }

        // Generate character
        async function generateCharacter() {
            const prompt = document.getElementById('characterPrompt').value.trim();
            if (!prompt) {
                alert('Please enter a character description.');
                return;
            }

            const generateBtn = document.getElementById('generateBtn');
            const statusSection = document.getElementById('generationStatus');
            const statusText = document.getElementById('statusText');
            const resultDiv = document.getElementById('generationResult');

            generateBtn.disabled = true;
            generateBtn.textContent = 'Generating...';
            statusSection.classList.remove('hidden');
            statusText.textContent = 'Connecting to backend...';

            try {
                // Try to connect to backend
                const response = await fetch('http://localhost:5000/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ prompt: prompt })
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success) {
                        statusText.textContent = '✅ Character generated successfully!';
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h4>Generation Complete</h4>
                                <p><strong>Job ID:</strong> ${result.job_id}</p>
                                <p><strong>Character Type:</strong> ${result.character_type}</p>
                                <p><strong>File Size:</strong> ${result.file_size} bytes</p>
                                <p><strong>Method:</strong> ${result.method || 'Real generation'}</p>
                                ${result.filename ? `<p><strong>File:</strong> ${result.filename}</p>` : ''}
                                ${result.note ? `<p><em>${result.note}</em></p>` : ''}
                            </div>
                        `;

                        // Add to character list
                        addToCharacterList(result);
                    } else {
                        throw new Error(result.error || 'Generation failed');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                console.error('Generation error:', error);
                statusText.textContent = '❌ Backend not available - Using local generation';
                
                // Fallback to local generation simulation
                const jobId = Date.now().toString(36);
                const characterType = analyzeCharacterPrompt(prompt);
                
                resultDiv.innerHTML = `
                    <div class="info">
                        <h4>Local Generation (Simulation)</h4>
                        <p><strong>Job ID:</strong> ${jobId}</p>
                        <p><strong>Character Type:</strong> ${characterType.name}</p>
                        <p><strong>Description:</strong> ${characterType.description}</p>
                        <p><em>To use real generation, start the backend server</em></p>
                        <br>
                        <button class="btn" onclick="runPythonGeneration('${prompt}')">
                            🐍 Generate with Python Script
                        </button>
                    </div>
                `;
            }

            generateBtn.disabled = false;
            generateBtn.textContent = 'Generate 3D Character';
        }

        // Analyze character prompt (client-side)
        function analyzeCharacterPrompt(prompt) {
            const lowerPrompt = prompt.toLowerCase();
            
            if (lowerPrompt.includes('robot') || lowerPrompt.includes('android')) {
                return { name: 'robot', description: 'Mechanical warrior with advanced armor' };
            } else if (lowerPrompt.includes('alien') || lowerPrompt.includes('extraterrestrial')) {
                return { name: 'alien', description: 'Mysterious being from another world' };
            } else if (lowerPrompt.includes('warrior') || lowerPrompt.includes('knight')) {
                return { name: 'warrior', description: 'Battle-hardened fighter' };
            } else if (lowerPrompt.includes('wizard') || lowerPrompt.includes('mage')) {
                return { name: 'wizard', description: 'Mystical spellcaster' };
            } else {
                return { name: 'humanoid', description: 'Versatile character' };
            }
        }

        // Add character to list
        function addToCharacterList(result) {
            const characterList = document.getElementById('characterList');
            
            const characterCard = document.createElement('div');
            characterCard.className = 'character-card';
            characterCard.onclick = () => loadCharacter(result.filename);
            
            characterCard.innerHTML = `
                <h4>🆕 ${result.character_type} Character</h4>
                <p>Recently generated</p>
                <small>${result.file_size} bytes</small>
            `;
            
            characterList.insertBefore(characterCard, characterList.firstChild);
        }

        // Run Python generation
        function runPythonGeneration(prompt) {
            alert(`To generate with Python:\n\n1. Open terminal in the project directory\n2. Run: python test_multiple_characters.py\n3. Or run: python -c "import sys; sys.path.append('backend'); from app import create_character_glb; glb = create_character_glb('${prompt}'); open('new_character.glb', 'wb').write(glb); print('Character saved as new_character.glb')"\n\nThis will create a real GLB file you can load here.`);
        }

        // Load character
        function loadCharacter(filename) {
            const viewerSection = document.getElementById('viewerSection');
            viewerSection.classList.remove('hidden');
            
            if (!renderer) {
                init3DViewer();
            }
            
            // Try to load the actual file
            const loader = new THREE.GLTFLoader();
            
            loader.load(filename, (gltf) => {
                // Remove existing model
                if (currentModel) {
                    scene.remove(currentModel);
                }
                
                currentModel = gltf.scene;
                
                // Center and scale model
                const box = new THREE.Box3().setFromObject(currentModel);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                
                currentModel.position.sub(center);
                
                const maxDim = Math.max(size.x, size.y, size.z);
                if (maxDim > 0) {
                    const scale = 2 / maxDim;
                    currentModel.scale.setScalar(scale);
                }
                
                scene.add(currentModel);
                
                // Update info
                updateModelInfo(gltf, filename);
                
                console.log('Character loaded:', filename);
                
            }, undefined, (error) => {
                console.error('Error loading character:', error);
                
                // Load fallback model
                loadFallbackModel(filename);
            });
        }

        // Initialize 3D viewer
        function init3DViewer() {
            const container = document.getElementById('viewerContainer');
            
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x2a5298);
            
            // Camera
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.set(3, 2, 3);
            
            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.shadowMap.enabled = true;
            container.appendChild(renderer.domElement);
            
            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            
            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(5, 5, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            // Grid
            const gridHelper = new THREE.GridHelper(10, 10);
            scene.add(gridHelper);
            
            animate();
        }

        // Load fallback model
        function loadFallbackModel(filename) {
            // Create a simple character model
            const group = new THREE.Group();
            
            // Body
            const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.2, 8);
            const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.y = 0;
            body.castShadow = true;
            group.add(body);
            
            // Head
            const headGeometry = new THREE.SphereGeometry(0.25, 16, 16);
            const headMaterial = new THREE.MeshLambertMaterial({ color: 0xf4c2a1 });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.y = 0.8;
            head.castShadow = true;
            group.add(head);
            
            currentModel = group;
            scene.add(currentModel);
            
            // Update info
            document.getElementById('modelInfo').innerHTML = `
                <p><strong>File:</strong> ${filename} (Fallback Model)</p>
                <p><strong>Vertices:</strong> ~200</p>
                <p><strong>Faces:</strong> ~150</p>
                <p><strong>Note:</strong> Fallback model - original file not found</p>
            `;
        }

        // Update model info
        function updateModelInfo(gltf, filename) {
            let vertices = 0;
            let faces = 0;
            
            gltf.scene.traverse((child) => {
                if (child.isMesh && child.geometry) {
                    if (child.geometry.attributes.position) {
                        vertices += child.geometry.attributes.position.count;
                    }
                    if (child.geometry.index) {
                        faces += child.geometry.index.count / 3;
                    }
                }
            });
            
            document.getElementById('modelInfo').innerHTML = `
                <p><strong>File:</strong> ${filename}</p>
                <p><strong>Vertices:</strong> ${vertices.toLocaleString()}</p>
                <p><strong>Faces:</strong> ${Math.floor(faces).toLocaleString()}</p>
                <p><strong>Animations:</strong> ${gltf.animations ? gltf.animations.length : 0}</p>
                <p><strong>Status:</strong> Loaded successfully</p>
            `;
        }

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            if (currentModel) {
                currentModel.rotation.y += 0.005;
            }
            
            if (controls) controls.update();
            if (renderer && scene && camera) renderer.render(scene, camera);
        }

        // Initialize on load
        window.addEventListener('load', () => {
            testConnection();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                const container = document.getElementById('viewerContainer');
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        });
    </script>
</body>
</html>
