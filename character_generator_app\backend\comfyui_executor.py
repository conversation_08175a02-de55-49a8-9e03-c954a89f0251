#!/usr/bin/env python3
"""
ComfyUI workflow executor for real-time AI processing
"""

import json
import requests
import websocket
import uuid
import time
import os
import threading
from urllib.parse import urljoin

class ComfyUIExecutor:
    """Execute ComfyUI workflows with real-time monitoring"""
    
    def __init__(self, server_url="http://127.0.0.1:8188", client_id=None):
        self.server_url = server_url
        self.client_id = client_id or str(uuid.uuid4())
        self.ws_url = server_url.replace('http', 'ws') + f"/ws?clientId={self.client_id}"
        
        self.ws = None
        self.execution_results = {}
        self.execution_status = {}
        
        # Test connection
        self.available = self.test_connection()
        
        if self.available:
            print(f"✅ ComfyUI Executor connected: {server_url}")
        else:
            print(f"⚠️ ComfyUI Executor not available: {server_url}")
    
    def test_connection(self):
        """Test connection to ComfyUI server"""
        try:
            response = requests.get(f"{self.server_url}/system_stats", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def connect_websocket(self):
        """Connect to ComfyUI WebSocket for real-time updates"""
        try:
            if self.ws:
                return True
            
            self.ws = websocket.WebSocketApp(
                self.ws_url,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            
            # Start WebSocket in a separate thread
            ws_thread = threading.Thread(target=self.ws.run_forever)
            ws_thread.daemon = True
            ws_thread.start()
            
            # Wait a moment for connection
            time.sleep(1)
            return True
            
        except Exception as e:
            print(f"WebSocket connection error: {e}")
            return False
    
    def on_message(self, ws, message):
        """Handle WebSocket messages from ComfyUI"""
        try:
            data = json.loads(message)
            msg_type = data.get('type')
            
            if msg_type == 'status':
                # Update execution status
                status_data = data.get('data', {})
                exec_info = status_data.get('status', {})
                self.execution_status.update(exec_info)
                
            elif msg_type == 'progress':
                # Update progress information
                progress_data = data.get('data', {})
                print(f"⏳ Progress: {progress_data.get('value', 0)}/{progress_data.get('max', 100)}")
                
            elif msg_type == 'executed':
                # Node execution completed
                node_data = data.get('data', {})
                node_id = node_data.get('node')
                output = node_data.get('output', {})
                
                if node_id:
                    self.execution_results[node_id] = output
                    
        except Exception as e:
            print(f"WebSocket message error: {e}")
    
    def on_error(self, ws, error):
        """Handle WebSocket errors"""
        print(f"WebSocket error: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close"""
        print("WebSocket connection closed")
        self.ws = None
    
    def load_workflow(self, workflow_path):
        """Load workflow from JSON file"""
        try:
            with open(workflow_path, 'r') as f:
                workflow = json.load(f)
            return workflow
        except Exception as e:
            print(f"Error loading workflow {workflow_path}: {e}")
            return None
    
    def update_workflow_parameters(self, workflow, parameters):
        """Update workflow with custom parameters"""
        try:
            updated_workflow = workflow.copy()
            
            # Update text prompts
            if 'positive_prompt' in parameters:
                for node_id, node in updated_workflow.get('nodes', {}).items():
                    if node.get('class_type') == 'CLIPTextEncode':
                        if 'positive' in node.get('_meta', {}).get('title', '').lower():
                            node['inputs']['text'] = parameters['positive_prompt']
            
            if 'negative_prompt' in parameters:
                for node_id, node in updated_workflow.get('nodes', {}).items():
                    if node.get('class_type') == 'CLIPTextEncode':
                        if 'negative' in node.get('_meta', {}).get('title', '').lower():
                            node['inputs']['text'] = parameters['negative_prompt']
            
            # Update image inputs
            if 'input_image' in parameters:
                for node_id, node in updated_workflow.get('nodes', {}).items():
                    if node.get('class_type') == 'LoadImage':
                        node['inputs']['image'] = parameters['input_image']
            
            # Update generation parameters
            for node_id, node in updated_workflow.get('nodes', {}).items():
                if node.get('class_type') == 'KSampler':
                    if 'seed' in parameters:
                        node['inputs']['seed'] = parameters['seed']
                    if 'steps' in parameters:
                        node['inputs']['steps'] = parameters['steps']
                    if 'cfg' in parameters:
                        node['inputs']['cfg'] = parameters['cfg']
                    if 'denoise' in parameters:
                        node['inputs']['denoise'] = parameters['denoise']
            
            return updated_workflow
            
        except Exception as e:
            print(f"Error updating workflow parameters: {e}")
            return workflow
    
    def queue_workflow(self, workflow):
        """Queue workflow for execution"""
        try:
            prompt_id = str(uuid.uuid4())
            
            queue_data = {
                "prompt": workflow.get('nodes', {}),
                "client_id": self.client_id,
                "extra_data": {
                    "extra_pnginfo": {
                        "workflow": workflow
                    }
                }
            }
            
            response = requests.post(
                f"{self.server_url}/prompt",
                json=queue_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                prompt_id = result.get('prompt_id')
                print(f"✅ Workflow queued: {prompt_id}")
                return prompt_id
            else:
                print(f"❌ Failed to queue workflow: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Error queuing workflow: {e}")
            return None
    
    def wait_for_completion(self, prompt_id, timeout=300):
        """Wait for workflow completion"""
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                # Check if execution is complete
                try:
                    response = requests.get(f"{self.server_url}/history/{prompt_id}")
                    if response.status_code == 200:
                        history = response.json()
                        if prompt_id in history:
                            print(f"✅ Workflow completed: {prompt_id}")
                            return history[prompt_id]
                except:
                    pass
                
                # Check queue status
                try:
                    response = requests.get(f"{self.server_url}/queue")
                    if response.status_code == 200:
                        queue_data = response.json()
                        
                        # Check if still in queue
                        in_queue = any(
                            item[1].get('prompt_id') == prompt_id 
                            for item in queue_data.get('queue_running', []) + queue_data.get('queue_pending', [])
                        )
                        
                        if not in_queue:
                            # Not in queue, check history again
                            continue
                except:
                    pass
                
                time.sleep(2)
            
            print(f"⏰ Workflow timeout: {prompt_id}")
            return None
            
        except Exception as e:
            print(f"Error waiting for completion: {e}")
            return None
    
    def get_output_images(self, history_data):
        """Extract output image paths from history data"""
        try:
            output_images = []
            
            if not history_data:
                return output_images
            
            outputs = history_data.get('outputs', {})
            
            for node_id, node_output in outputs.items():
                if 'images' in node_output:
                    for image_info in node_output['images']:
                        filename = image_info.get('filename')
                        subfolder = image_info.get('subfolder', '')
                        
                        if filename:
                            # Construct full path
                            if subfolder:
                                image_path = os.path.join('output', subfolder, filename)
                            else:
                                image_path = os.path.join('output', filename)
                            
                            output_images.append({
                                'filename': filename,
                                'path': image_path,
                                'node_id': node_id,
                                'type': image_info.get('type', 'output')
                            })
            
            return output_images
            
        except Exception as e:
            print(f"Error extracting output images: {e}")
            return []
    
    def execute_workflow(self, workflow_path, parameters=None, timeout=300):
        """Execute a complete workflow with parameters"""
        try:
            if not self.available:
                print("❌ ComfyUI not available")
                return None
            
            print(f"🚀 Executing workflow: {os.path.basename(workflow_path)}")
            
            # Load workflow
            workflow = self.load_workflow(workflow_path)
            if not workflow:
                return None
            
            # Update parameters
            if parameters:
                workflow = self.update_workflow_parameters(workflow, parameters)
            
            # Connect WebSocket for monitoring
            self.connect_websocket()
            
            # Queue workflow
            prompt_id = self.queue_workflow(workflow)
            if not prompt_id:
                return None
            
            # Wait for completion
            history_data = self.wait_for_completion(prompt_id, timeout)
            if not history_data:
                return None
            
            # Extract output images
            output_images = self.get_output_images(history_data)
            
            result = {
                'success': True,
                'prompt_id': prompt_id,
                'output_images': output_images,
                'history': history_data,
                'workflow_file': os.path.basename(workflow_path)
            }
            
            print(f"✅ Workflow execution complete: {len(output_images)} images generated")
            return result
            
        except Exception as e:
            print(f"❌ Workflow execution failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'workflow_file': os.path.basename(workflow_path) if workflow_path else 'unknown'
            }
    
    def execute_character_sheet_workflow(self, input_image_path, character_description):
        """Execute character sheet generation workflow"""
        parameters = {
            'input_image': os.path.basename(input_image_path),
            'positive_prompt': f"character sheet, multiple views, {character_description}, reference sheet, detailed anatomy, professional quality",
            'negative_prompt': "blurry, low quality, distorted, nsfw, nude, bad anatomy",
            'seed': int(time.time()) % 1000000,
            'steps': 30,
            'cfg': 8.0
        }
        
        workflow_path = "workflows/mickmumpitz_character_sheet.json"
        return self.execute_workflow(workflow_path, parameters)
    
    def execute_face_enhancement_workflow(self, input_image_path):
        """Execute face enhancement workflow"""
        parameters = {
            'input_image': os.path.basename(input_image_path),
            'positive_prompt': "high quality face, detailed features, clear skin, professional photography, good lighting",
            'negative_prompt': "blurry, low quality, distorted, bad lighting, noise, artifacts",
            'seed': int(time.time()) % 1000000,
            'steps': 20,
            'cfg': 6.0,
            'denoise': 0.3
        }
        
        workflow_path = "workflows/mickmumpitz_face_enhancement.json"
        return self.execute_workflow(workflow_path, parameters)
    
    def execute_3d_rendering_workflow(self, character_description, style="realistic"):
        """Execute 3D rendering workflow"""
        parameters = {
            'positive_prompt': f"3D rendered character, {character_description}, {style} style, high quality, detailed modeling, professional lighting",
            'negative_prompt': "2D, flat, cartoon, low quality, blurry, distorted, amateur, bad lighting",
            'seed': int(time.time()) % 1000000,
            'steps': 25,
            'cfg': 7.5
        }
        
        workflow_path = "workflows/mickmumpitz_3d_rendering.json"
        return self.execute_workflow(workflow_path, parameters)
    
    def get_server_status(self):
        """Get ComfyUI server status"""
        try:
            if not self.available:
                return {'status': 'unavailable'}
            
            # Get system stats
            response = requests.get(f"{self.server_url}/system_stats", timeout=5)
            if response.status_code == 200:
                stats = response.json()
            else:
                stats = {}
            
            # Get queue info
            response = requests.get(f"{self.server_url}/queue", timeout=5)
            if response.status_code == 200:
                queue_info = response.json()
            else:
                queue_info = {}
            
            return {
                'status': 'available',
                'server_url': self.server_url,
                'client_id': self.client_id,
                'system_stats': stats,
                'queue_info': queue_info,
                'websocket_connected': self.ws is not None
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
