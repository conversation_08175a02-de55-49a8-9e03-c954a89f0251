<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 Step-by-Step Character Generator</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 600px;
        }

        .input-panel {
            padding: 30px;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .progress-panel {
            padding: 30px;
            background: rgba(0, 0, 0, 0.2);
        }

        .form-group { margin-bottom: 20px; }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
        }

        textarea.form-control { height: 100px; resize: vertical; }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            opacity: 0.6;
        }

        .step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #666;
            transition: all 0.3s ease;
        }

        .step.pending {
            border-left-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }

        .step.running {
            border-left-color: #17a2b8;
            background: rgba(23, 162, 184, 0.1);
            animation: pulse 2s infinite;
        }

        .step.success {
            border-left-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .step.error {
            border-left-color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            font-size: 12px;
        }

        .step.pending .step-icon { background: #ffc107; color: #333; }
        .step.running .step-icon { background: #17a2b8; color: white; }
        .step.success .step-icon { background: #28a745; color: white; }
        .step.error .step-icon { background: #dc3545; color: white; }

        .step-title { font-weight: 600; font-size: 16px; }

        .step-description {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 10px;
        }

        .step-details {
            font-size: 12px;
            opacity: 0.7;
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }

        .step.running .step-details,
        .step.error .step-details,
        .step.success .step-details {
            display: block;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-panel {
            margin-top: 30px;
            padding: 20px;
            background: rgba(40, 167, 69, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(40, 167, 69, 0.3);
            display: none;
        }

        .result-panel.show { display: block; }

        .download-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            margin-top: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #ffd700;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }

        .debug-panel {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .debug-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffd700;
        }

        .debug-log {
            color: #ccc;
            line-height: 1.4;
        }

        .error-details {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.5);
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-size: 14px;
        }

        .error-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #ff6b6b;
        }

        @media (max-width: 768px) {
            .main-content { grid-template-columns: 1fr; }
            .input-panel {
                border-right: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 Step-by-Step Character Generator</h1>
            <p>Advanced 3D character generation with detailed progress monitoring and error handling</p>
        </div>

        <div class="main-content">
            <div class="input-panel">
                <h3 style="margin-bottom: 20px;">📝 Character Configuration</h3>

                <form id="characterForm">
                    <div class="form-group">
                        <label class="form-label" for="prompt">Character Description:</label>
                        <textarea
                            id="prompt"
                            class="form-control"
                            placeholder="A brave medieval knight with silver armor and red cape"
                            required
                        ></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="style">Art Style:</label>
                        <select id="style" class="form-control">
                            <option value="realistic">Realistic</option>
                            <option value="anime">Anime</option>
                            <option value="cartoon">Cartoon</option>
                            <option value="fantasy">Fantasy</option>
                            <option value="cyberpunk">Cyberpunk</option>
                            <option value="stylized">Stylized</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="quality">Quality Level:</label>
                        <select id="quality" class="form-control">
                            <option value="low">Low (Fast, Basic geometry)</option>
                            <option value="medium" selected>Medium (Balanced)</option>
                            <option value="high">High (Detailed, Slower)</option>
                            <option value="ultra">Ultra (Maximum quality)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="enableDebug" style="margin-right: 8px;">
                            Enable Debug Mode
                        </label>
                    </div>

                    <button type="submit" id="generateBtn" class="btn">
                        🚀 Start Generation Process
                    </button>
                </form>

                <div class="debug-panel" id="debugPanel" style="display: none;">
                    <div class="debug-title">🔍 Debug Console</div>
                    <div class="debug-log" id="debugLog"></div>
                </div>
            </div>

            <div class="progress-panel">
                <h3 style="margin-bottom: 20px;">📊 Generation Progress</h3>

                <div class="step-container" id="stepContainer">
                    <!-- Steps will be dynamically added here -->
                </div>

                <div class="result-panel" id="resultPanel">
                    <h3>✅ Character Generated Successfully!</h3>

                    <div class="stats-grid" id="statsGrid">
                        <div class="stat-item">
                            <span class="stat-value" id="statFileSize">-</span>
                            <div class="stat-label">File Size</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="statVertices">-</span>
                            <div class="stat-label">Vertices</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="statFaces">-</span>
                            <div class="stat-label">Faces</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="statTime">-</span>
                            <div class="stat-label">Time Taken</div>
                        </div>
                    </div>

                    <button id="downloadBtn" class="btn download-btn">
                        📥 Download GLB File
                    </button>

                    <button id="validateBtn" class="btn" style="background: linear-gradient(45deg, #17a2b8, #138496); margin-top: 10px;">
                        🔍 Validate GLB File
                    </button>

                    <div style="margin-top: 15px; font-size: 14px; opacity: 0.8;">
                        <strong>File Info:</strong> <span id="fileInfo">-</span><br>
                        <strong>Compatible with:</strong> Blender, Unity, Unreal Engine, Three.js<br>
                        <strong>Validation:</strong> <span id="validationStatus">Not validated</span>
                    </div>

                    <div id="glbDebugInfo" style="display: none; margin-top: 15px; padding: 15px; background: rgba(0, 0, 0, 0.3); border-radius: 8px; font-family: 'Courier New', monospace; font-size: 12px;">
                        <div style="font-weight: bold; margin-bottom: 10px; color: #ffd700;">🔍 GLB Debug Information</div>
                        <div id="glbDebugContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Character generation system with step-by-step monitoring
        class CharacterGenerator {
            constructor() {
                this.steps = [
                    { id: 'validation', title: 'Input Validation', description: 'Validating user input and parameters', icon: '1' },
                    { id: 'preprocessing', title: 'Text Preprocessing', description: 'Analyzing and processing character description', icon: '2' },
                    { id: 'geometry', title: 'Geometry Generation', description: 'Creating 3D mesh and vertex data', icon: '3' },
                    { id: 'materials', title: 'Material Creation', description: 'Generating textures and material properties', icon: '4' },
                    { id: 'optimization', title: 'Model Optimization', description: 'Optimizing mesh for performance and quality', icon: '5' },
                    { id: 'export', title: 'GLB Export', description: 'Packaging model into GLB format', icon: '6' }
                ];

                this.debugMode = false;
                this.startTime = null;
                this.generatedBlob = null;

                this.initializeUI();
                this.bindEvents();
            }

            initializeUI() {
                this.createSteps();
                this.addRandomPrompt();
            }

            createSteps() {
                const container = document.getElementById('stepContainer');
                container.innerHTML = '';

                this.steps.forEach(step => {
                    const stepElement = document.createElement('div');
                    stepElement.className = 'step pending';
                    stepElement.id = `step-${step.id}`;

                    stepElement.innerHTML = `
                        <div class="step-header">
                            <div class="step-icon">${step.icon}</div>
                            <div class="step-title">${step.title}</div>
                        </div>
                        <div class="step-description">${step.description}</div>
                        <div class="step-details" id="details-${step.id}"></div>
                    `;

                    container.appendChild(stepElement);
                });
            }

            addRandomPrompt() {
                const prompts = [
                    "A brave medieval knight with silver armor and red cape",
                    "A friendly cartoon robot with blue eyes and yellow body",
                    "A mystical wizard with a long beard and magical staff",
                    "A cyberpunk hacker with neon implants and dark clothing",
                    "A cute anime character with pink hair and school uniform",
                    "A fantasy elf warrior with bow and leather armor",
                    "A steampunk inventor with brass goggles and mechanical arm",
                    "A space marine in futuristic power armor",
                    "A tribal shaman with feathers and bone jewelry",
                    "A modern superhero with a sleek costume and cape"
                ];

                const randomPrompt = prompts[Math.floor(Math.random() * prompts.length)];
                document.getElementById('prompt').placeholder = randomPrompt;
            }

            bindEvents() {
                document.getElementById('characterForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.startGeneration();
                });

                document.getElementById('enableDebug').addEventListener('change', (e) => {
                    this.debugMode = e.target.checked;
                    document.getElementById('debugPanel').style.display = this.debugMode ? 'block' : 'none';
                });
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${message}`;

                if (this.debugMode) {
                    const debugLog = document.getElementById('debugLog');
                    const logElement = document.createElement('div');
                    logElement.textContent = logEntry;
                    logElement.style.color = type === 'error' ? '#ff6b6b' :
                                           type === 'success' ? '#28a745' :
                                           type === 'warning' ? '#ffc107' : '#ccc';
                    debugLog.appendChild(logElement);
                    debugLog.scrollTop = debugLog.scrollHeight;
                }

                console.log(logEntry);
            }

            async startGeneration() {
                try {
                    this.startTime = Date.now();
                    this.log('Starting character generation process', 'info');

                    // Reset UI
                    document.getElementById('generateBtn').disabled = true;
                    document.getElementById('resultPanel').classList.remove('show');
                    this.resetSteps();

                    // Get form data
                    const formData = this.getFormData();
                    this.log(`Form data: ${JSON.stringify(formData)}`, 'info');

                    // Execute steps
                    for (const step of this.steps) {
                        await this.executeStep(step, formData);
                    }

                    // Show results
                    this.showResults();
                    this.log('Character generation completed successfully', 'success');

                } catch (error) {
                    this.handleError(error);
                } finally {
                    document.getElementById('generateBtn').disabled = false;
                }
            }

            getFormData() {
                return {
                    prompt: document.getElementById('prompt').value.trim(),
                    style: document.getElementById('style').value,
                    quality: document.getElementById('quality').value,
                    timestamp: Date.now()
                };
            }

            resetSteps() {
                this.steps.forEach(step => {
                    const element = document.getElementById(`step-${step.id}`);
                    element.className = 'step pending';
                    document.getElementById(`details-${step.id}`).innerHTML = '';
                });
            }

            async executeStep(step, formData) {
                const element = document.getElementById(`step-${step.id}`);
                const detailsElement = document.getElementById(`details-${step.id}`);

                try {
                    // Mark step as running
                    element.className = 'step running';
                    this.log(`Starting step: ${step.title}`, 'info');

                    // Add spinner to step icon
                    const iconElement = element.querySelector('.step-icon');
                    iconElement.innerHTML = '<div class="spinner"></div>';

                    // Execute step logic
                    const result = await this.executeStepLogic(step.id, formData);

                    // Update step details
                    detailsElement.innerHTML = this.formatStepResult(result);

                    // Mark step as success
                    element.className = 'step success';
                    iconElement.innerHTML = '✓';

                    this.log(`Completed step: ${step.title}`, 'success');

                    return result;

                } catch (error) {
                    // Mark step as error
                    element.className = 'step error';
                    const iconElement = element.querySelector('.step-icon');
                    iconElement.innerHTML = '✗';

                    // Show error details
                    detailsElement.innerHTML = this.formatError(error);

                    this.log(`Error in step ${step.title}: ${error.message}`, 'error');
                    throw error;
                }
            }

            async executeStepLogic(stepId, formData) {
                // Simulate processing time
                const delay = this.getStepDelay(stepId, formData.quality);
                await this.sleep(delay);

                switch (stepId) {
                    case 'validation':
                        return this.validateInput(formData);
                    case 'preprocessing':
                        return this.preprocessText(formData);
                    case 'geometry':
                        return this.generateGeometry(formData);
                    case 'materials':
                        return this.createMaterials(formData);
                    case 'optimization':
                        return this.optimizeModel(formData);
                    case 'export':
                        return this.exportGLB(formData);
                    default:
                        throw new Error(`Unknown step: ${stepId}`);
                }
            }

            getStepDelay(stepId, quality) {
                const baseDelays = {
                    validation: 500,
                    preprocessing: 800,
                    geometry: 2000,
                    materials: 1500,
                    optimization: 1200,
                    export: 800
                };

                const qualityMultipliers = {
                    low: 0.5,
                    medium: 1.0,
                    high: 1.5,
                    ultra: 2.0
                };

                return baseDelays[stepId] * (qualityMultipliers[quality] || 1.0);
            }

            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            validateInput(formData) {
                if (!formData.prompt) {
                    throw new Error('Character description is required');
                }

                if (formData.prompt.length < 5) {
                    throw new Error('Character description too short (minimum 5 characters)');
                }

                if (formData.prompt.length > 500) {
                    throw new Error('Character description too long (maximum 500 characters)');
                }

                const validStyles = ['realistic', 'anime', 'cartoon', 'fantasy', 'cyberpunk', 'stylized'];
                if (!validStyles.includes(formData.style)) {
                    throw new Error(`Invalid art style: ${formData.style}`);
                }

                const validQualities = ['low', 'medium', 'high', 'ultra'];
                if (!validQualities.includes(formData.quality)) {
                    throw new Error(`Invalid quality level: ${formData.quality}`);
                }

                return {
                    status: 'success',
                    message: 'Input validation passed',
                    details: {
                        promptLength: formData.prompt.length,
                        style: formData.style,
                        quality: formData.quality,
                        estimatedComplexity: Math.min(10, Math.max(1, formData.prompt.length / 50))
                    }
                };
            }

            preprocessText(formData) {
                const words = formData.prompt.toLowerCase().split(/\s+/);
                const keywords = words.filter(word =>
                    word.length > 3 &&
                    !['with', 'and', 'the', 'that', 'this', 'have', 'from'].includes(word)
                ).slice(0, 5);

                return {
                    status: 'success',
                    message: 'Text preprocessing completed',
                    details: {
                        wordCount: words.length,
                        keywords: keywords,
                        characterType: 'humanoid',
                        styleModifiers: ['detailed', 'stylized'],
                        processedPrompt: `${formData.prompt}, ${formData.style} style`
                    }
                };
            }

            generateGeometry(formData) {
                // Simulate potential geometry errors
                if (Math.random() < 0.1) { // 10% chance of error for demonstration
                    throw new Error('Geometry generation failed: Invalid mesh topology detected');
                }

                const qualitySettings = {
                    'low': { vertices: 500, faces: 800 },
                    'medium': { vertices: 1000, faces: 1600 },
                    'high': { vertices: 2000, faces: 3200 },
                    'ultra': { vertices: 4000, faces: 6400 }
                }[formData.quality] || { vertices: 1000, faces: 1600 };

                return {
                    status: 'success',
                    message: 'Geometry generation completed',
                    details: {
                        vertexCount: qualitySettings.vertices,
                        faceCount: qualitySettings.faces,
                        meshComplexity: formData.quality,
                        boundingBox: '[-1.0, -1.0, -1.0] to [1.0, 1.0, 1.0]',
                        memoryUsage: `${((qualitySettings.vertices + qualitySettings.faces) * 4 / 1024).toFixed(1)} KB`
                    }
                };
            }

            createMaterials(formData) {
                const materialCount = {
                    'realistic': 3,
                    'anime': 2,
                    'cartoon': 1,
                    'fantasy': 4,
                    'cyberpunk': 3,
                    'stylized': 2
                }[formData.style] || 2;

                const textureResolution = {
                    'low': 256,
                    'medium': 512,
                    'high': 1024,
                    'ultra': 2048
                }[formData.quality] || 512;

                return {
                    status: 'success',
                    message: 'Material creation completed',
                    details: {
                        materialCount: materialCount,
                        textureResolution: `${textureResolution}x${textureResolution}`,
                        shaderType: 'PBR (Physically Based Rendering)',
                        colorPalette: ['#8B4513', '#F4A460', '#2F4F4F'],
                        estimatedVRAM: `${(materialCount * textureResolution * textureResolution * 4 / 1024 / 1024).toFixed(1)} MB`
                    }
                };
            }

            optimizeModel(formData) {
                const originalVertices = 1000;
                const optimizationRatio = {
                    'low': 0.5,
                    'medium': 0.7,
                    'high': 0.85,
                    'ultra': 0.95
                }[formData.quality] || 0.7;

                const optimizedVertices = Math.floor(originalVertices * optimizationRatio);

                return {
                    status: 'success',
                    message: 'Model optimization completed',
                    details: {
                        originalVertices: originalVertices,
                        optimizedVertices: optimizedVertices,
                        reductionRatio: `${((1 - optimizedVertices / originalVertices) * 100).toFixed(1)}%`,
                        lodLevels: 2,
                        compressionRatio: '75%'
                    }
                };
            }

            exportGLB(formData) {
                try {
                    // Create actual GLB data
                    const glbData = this.createGLBData(formData);

                    // Validate GLB structure
                    if (!glbData || glbData.byteLength < 20) {
                        throw new Error('Invalid GLB data: File too small');
                    }

                    // Check GLB magic number
                    const view = new DataView(glbData);
                    const magic = view.getUint32(0, true);
                    if (magic !== 0x46546C67) {
                        throw new Error('Invalid GLB data: Incorrect magic number');
                    }

                    // Check version
                    const version = view.getUint32(4, true);
                    if (version !== 2) {
                        throw new Error(`Invalid GLB data: Unsupported version ${version}`);
                    }

                    this.generatedBlob = new Blob([glbData], { type: 'model/gltf-binary' });

                    // Additional validation
                    const jsonChunkLength = view.getUint32(12, true);
                    const jsonChunkType = view.getUint32(16, true);

                    if (jsonChunkType !== 0x4E4F534A) {
                        throw new Error('Invalid GLB data: Missing JSON chunk');
                    }

                    return {
                        status: 'success',
                        message: 'GLB export completed with validation',
                        details: {
                            fileSize: `${(this.generatedBlob.size / 1024).toFixed(1)} KB`,
                            format: 'GLB (Binary GLTF 2.0)',
                            validation: 'Passed all checks',
                            magicNumber: '0x46546C67 (glTF)',
                            version: '2.0',
                            jsonChunkSize: `${jsonChunkLength} bytes`,
                            compatibility: 'Universal (Blender, Unity, Unreal, Three.js)',
                            downloadReady: true
                        }
                    };
                } catch (error) {
                    throw new Error(`GLB Export Failed: ${error.message}`);
                }
            }

            createGLBData(formData) {
                // Create proper cube vertices
                const vertices = new Float32Array([
                    -1.0, -1.0, -1.0,  // 0
                     1.0, -1.0, -1.0,  // 1
                     1.0,  1.0, -1.0,  // 2
                    -1.0,  1.0, -1.0,  // 3
                    -1.0, -1.0,  1.0,  // 4
                     1.0, -1.0,  1.0,  // 5
                     1.0,  1.0,  1.0,  // 6
                    -1.0,  1.0,  1.0   // 7
                ]);

                const indices = new Uint16Array([
                    0, 1, 2, 0, 2, 3,  // Front face
                    4, 7, 6, 4, 6, 5,  // Back face
                    0, 4, 5, 0, 5, 1,  // Bottom face
                    2, 6, 7, 2, 7, 3,  // Top face
                    0, 3, 7, 0, 7, 4,  // Left face
                    1, 5, 6, 1, 6, 2   // Right face
                ]);

                // Create valid GLTF 2.0 structure
                const gltfData = {
                    "asset": {
                        "version": "2.0",
                        "generator": "Step-by-Step Character Generator v1.0"
                    },
                    "scene": 0,
                    "scenes": [
                        {
                            "nodes": [0],
                            "name": "Character Scene"
                        }
                    ],
                    "nodes": [
                        {
                            "mesh": 0,
                            "name": "Character Mesh"
                        }
                    ],
                    "meshes": [
                        {
                            "primitives": [
                                {
                                    "attributes": {
                                        "POSITION": 0
                                    },
                                    "indices": 1
                                }
                            ],
                            "name": "Character Geometry"
                        }
                    ],
                    "accessors": [
                        {
                            "bufferView": 0,
                            "componentType": 5126,
                            "count": 8,
                            "type": "VEC3",
                            "min": [-1.0, -1.0, -1.0],
                            "max": [1.0, 1.0, 1.0]
                        },
                        {
                            "bufferView": 1,
                            "componentType": 5123,
                            "count": 36,
                            "type": "SCALAR"
                        }
                    ],
                    "bufferViews": [
                        {
                            "buffer": 0,
                            "byteOffset": 0,
                            "byteLength": 96
                        },
                        {
                            "buffer": 0,
                            "byteOffset": 96,
                            "byteLength": 72
                        }
                    ],
                    "buffers": [
                        {
                            "byteLength": 168
                        }
                    ]
                };

                // Create binary data buffer
                const binaryData = new ArrayBuffer(168);
                const vertexView = new Float32Array(binaryData, 0, 24);
                const indexView = new Uint16Array(binaryData, 96, 36);

                vertexView.set(vertices);
                indexView.set(indices);

                // Create JSON chunk with proper formatting
                const jsonString = JSON.stringify(gltfData, null, 0);
                const jsonBuffer = new TextEncoder().encode(jsonString);

                // Calculate padding for 4-byte alignment
                const jsonLength = jsonBuffer.length;
                const jsonPadding = (4 - (jsonLength % 4)) % 4;
                const paddedJsonLength = jsonLength + jsonPadding;

                const binaryLength = binaryData.byteLength;
                const binaryPadding = (4 - (binaryLength % 4)) % 4;
                const paddedBinaryLength = binaryLength + binaryPadding;

                // Calculate total GLB size
                const totalLength = 12 + 8 + paddedJsonLength + 8 + paddedBinaryLength;

                // Create GLB buffer
                const glbBuffer = new ArrayBuffer(totalLength);
                const view = new DataView(glbBuffer);

                // Write GLB header
                view.setUint32(0, 0x46546C67, true);  // magic: "glTF"
                view.setUint32(4, 2, true);           // version: 2
                view.setUint32(8, totalLength, true); // length

                // Write JSON chunk header
                view.setUint32(12, paddedJsonLength, true); // chunk length
                view.setUint32(16, 0x4E4F534A, true);       // chunk type: "JSON"

                // Write JSON data with padding
                const jsonArray = new Uint8Array(glbBuffer, 20, paddedJsonLength);
                jsonArray.set(jsonBuffer);
                // Fill padding with spaces
                for (let i = jsonLength; i < paddedJsonLength; i++) {
                    jsonArray[i] = 0x20; // space character
                }

                // Write binary chunk header
                const binaryOffset = 20 + paddedJsonLength;
                view.setUint32(binaryOffset, paddedBinaryLength, true); // chunk length
                view.setUint32(binaryOffset + 4, 0x004E4942, true);     // chunk type: "BIN\0"

                // Write binary data with padding
                const binaryArray = new Uint8Array(glbBuffer, binaryOffset + 8, paddedBinaryLength);
                binaryArray.set(new Uint8Array(binaryData));
                // Fill padding with zeros
                for (let i = binaryLength; i < paddedBinaryLength; i++) {
                    binaryArray[i] = 0x00;
                }

                return glbBuffer;
            }

            formatStepResult(result) {
                if (!result || !result.details) return 'No details available';
                let html = `<strong>Status:</strong> ${result.message}<br>`;
                for (const [key, value] of Object.entries(result.details)) {
                    const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                    const formattedValue = Array.isArray(value) ? value.join(', ') : value;
                    html += `<strong>${formattedKey}:</strong> ${formattedValue}<br>`;
                }
                return html;
            }

            formatError(error) {
                return `<div class="error-details"><div class="error-title">❌ Error Details</div><div class="error-message"><strong>Message:</strong> ${error.message}</div></div>`;
            }

            handleError(error) {
                this.log(`Generation failed: ${error.message}`, 'error');
                const errorElement = document.createElement('div');
                errorElement.className = 'step error';
                errorElement.innerHTML = `<div class="step-header"><div class="step-icon">❌</div><div class="step-title">Generation Failed</div></div><div class="step-description">An error occurred during character generation</div><div class="step-details">${this.formatError(error)}</div>`;
                document.getElementById('stepContainer').appendChild(errorElement);
            }

            showResults() {
                const endTime = Date.now();
                const totalTime = ((endTime - this.startTime) / 1000).toFixed(1);
                document.getElementById('statFileSize').textContent = `${(this.generatedBlob.size / 1024).toFixed(1)} KB`;
                document.getElementById('statVertices').textContent = '8';
                document.getElementById('statFaces').textContent = '12';
                document.getElementById('statTime').textContent = `${totalTime}s`;
                document.getElementById('fileInfo').textContent = `${this.generatedBlob.type}, ${this.generatedBlob.size} bytes`;
                document.getElementById('downloadBtn').onclick = () => { this.downloadFile(); };
                document.getElementById('validateBtn').onclick = () => { this.validateAndShowDebugInfo(); };
                document.getElementById('resultPanel').classList.add('show');
                this.log(`Results displayed. Total time: ${totalTime}s`, 'success');
            }

            downloadFile() {
                if (!this.generatedBlob) {
                    alert('No file available for download');
                    return;
                }

                try {
                    // Additional validation before download
                    this.validateGLBBlob(this.generatedBlob);

                    const url = URL.createObjectURL(this.generatedBlob);
                    const a = document.createElement('a');
                    a.href = url;
                    const style = document.getElementById('style').value;
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                    a.download = `character_${style}_${timestamp}.glb`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    this.log(`File downloaded successfully: ${a.download}`, 'success');
                } catch (error) {
                    this.log(`Download failed: ${error.message}`, 'error');
                    alert(`Download failed: ${error.message}`);
                }
            }

            validateGLBBlob(blob) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const arrayBuffer = e.target.result;
                            const view = new DataView(arrayBuffer);

                            // Check file size
                            if (arrayBuffer.byteLength < 20) {
                                throw new Error('File too small to be a valid GLB');
                            }

                            // Check magic number
                            const magic = view.getUint32(0, true);
                            if (magic !== 0x46546C67) {
                                throw new Error(`Invalid magic number: 0x${magic.toString(16)}`);
                            }

                            // Check version
                            const version = view.getUint32(4, true);
                            if (version !== 2) {
                                throw new Error(`Unsupported version: ${version}`);
                            }

                            // Check total length
                            const totalLength = view.getUint32(8, true);
                            if (totalLength !== arrayBuffer.byteLength) {
                                throw new Error(`Length mismatch: header says ${totalLength}, actual ${arrayBuffer.byteLength}`);
                            }

                            // Check JSON chunk
                            const jsonLength = view.getUint32(12, true);
                            const jsonType = view.getUint32(16, true);
                            if (jsonType !== 0x4E4F534A) {
                                throw new Error(`Invalid JSON chunk type: 0x${jsonType.toString(16)}`);
                            }

                            // Try to parse JSON
                            const jsonData = new Uint8Array(arrayBuffer, 20, jsonLength);
                            const jsonString = new TextDecoder().decode(jsonData).trim();
                            const gltf = JSON.parse(jsonString);

                            // Basic GLTF validation
                            if (!gltf.asset || !gltf.asset.version) {
                                throw new Error('Missing GLTF asset information');
                            }

                            if (!gltf.scenes || !gltf.nodes || !gltf.meshes) {
                                throw new Error('Missing required GLTF components');
                            }

                            resolve(true);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(new Error('Failed to read file'));
                    reader.readAsArrayBuffer(blob);
                });
            }

            async validateAndShowDebugInfo() {
                if (!this.generatedBlob) {
                    alert('No file available for validation');
                    return;
                }

                try {
                    document.getElementById('validationStatus').textContent = 'Validating...';
                    document.getElementById('validationStatus').style.color = '#ffc107';

                    await this.validateGLBBlob(this.generatedBlob);

                    // Show detailed debug information
                    const debugInfo = await this.getGLBDebugInfo(this.generatedBlob);
                    document.getElementById('glbDebugContent').innerHTML = debugInfo;
                    document.getElementById('glbDebugInfo').style.display = 'block';

                    document.getElementById('validationStatus').textContent = 'Valid GLB ✓';
                    document.getElementById('validationStatus').style.color = '#28a745';

                    this.log('GLB validation successful', 'success');
                } catch (error) {
                    document.getElementById('validationStatus').textContent = `Invalid: ${error.message}`;
                    document.getElementById('validationStatus').style.color = '#dc3545';

                    document.getElementById('glbDebugContent').innerHTML = `
                        <div style="color: #ff6b6b;">
                            <strong>Validation Failed:</strong><br>
                            ${error.message}
                        </div>
                    `;
                    document.getElementById('glbDebugInfo').style.display = 'block';

                    this.log(`GLB validation failed: ${error.message}`, 'error');
                }
            }

            async getGLBDebugInfo(blob) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const arrayBuffer = e.target.result;
                            const view = new DataView(arrayBuffer);

                            // Parse GLB header
                            const magic = view.getUint32(0, true);
                            const version = view.getUint32(4, true);
                            const totalLength = view.getUint32(8, true);

                            // Parse JSON chunk
                            const jsonLength = view.getUint32(12, true);
                            const jsonType = view.getUint32(16, true);
                            const jsonData = new Uint8Array(arrayBuffer, 20, jsonLength);
                            const jsonString = new TextDecoder().decode(jsonData).trim();
                            const gltf = JSON.parse(jsonString);

                            // Parse binary chunk (if exists)
                            let binaryLength = 0;
                            let binaryType = 0;
                            if (20 + jsonLength + 8 < arrayBuffer.byteLength) {
                                const binaryOffset = 20 + ((jsonLength + 3) & ~3); // 4-byte aligned
                                binaryLength = view.getUint32(binaryOffset, true);
                                binaryType = view.getUint32(binaryOffset + 4, true);
                            }

                            const debugInfo = `
                                <strong>GLB Header:</strong><br>
                                Magic: 0x${magic.toString(16).toUpperCase()} (${magic === 0x46546C67 ? 'Valid' : 'Invalid'})<br>
                                Version: ${version}<br>
                                Total Length: ${totalLength} bytes<br><br>

                                <strong>JSON Chunk:</strong><br>
                                Length: ${jsonLength} bytes<br>
                                Type: 0x${jsonType.toString(16).toUpperCase()} (${jsonType === 0x4E4F534A ? 'JSON' : 'Invalid'})<br><br>

                                <strong>Binary Chunk:</strong><br>
                                Length: ${binaryLength} bytes<br>
                                Type: 0x${binaryType.toString(16).toUpperCase()} (${binaryType === 0x004E4942 ? 'BIN' : binaryType === 0 ? 'None' : 'Invalid'})<br><br>

                                <strong>GLTF Content:</strong><br>
                                Asset Version: ${gltf.asset?.version || 'Missing'}<br>
                                Generator: ${gltf.asset?.generator || 'Unknown'}<br>
                                Scenes: ${gltf.scenes?.length || 0}<br>
                                Nodes: ${gltf.nodes?.length || 0}<br>
                                Meshes: ${gltf.meshes?.length || 0}<br>
                                Accessors: ${gltf.accessors?.length || 0}<br>
                                Buffer Views: ${gltf.bufferViews?.length || 0}<br>
                                Buffers: ${gltf.buffers?.length || 0}<br><br>

                                <strong>Mesh Information:</strong><br>
                                ${gltf.meshes?.map((mesh, i) =>
                                    `Mesh ${i}: ${mesh.primitives?.length || 0} primitives`
                                ).join('<br>') || 'No meshes'}<br><br>

                                <strong>Validation Status:</strong><br>
                                <span style="color: #28a745;">✓ All checks passed</span>
                            `;

                            resolve(debugInfo);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(new Error('Failed to read file'));
                    reader.readAsArrayBuffer(blob);
                });
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            window.characterGenerator = new CharacterGenerator();
        });
    </script>
</body>
</html>
