#!/usr/bin/env python3
"""
Advanced face processing for exact face matching in 3D character generation
"""

import cv2
import numpy as np
import os
import json
from PIL import Image
import base64

class FaceProcessor:
    """Advanced face processing for character generation"""
    
    def __init__(self):
        self.face_cascade = None
        self.eye_cascade = None
        self.smile_cascade = None
        self.init_cascades()
    
    def init_cascades(self):
        """Initialize OpenCV face detection cascades"""
        try:
            # Try to load OpenCV cascades
            cascade_path = cv2.data.haarcascades
            
            self.face_cascade = cv2.CascadeClassifier(
                os.path.join(cascade_path, 'haarcascade_frontalface_default.xml')
            )
            self.eye_cascade = cv2.CascadeClassifier(
                os.path.join(cascade_path, 'haarcascade_eye.xml')
            )
            self.smile_cascade = cv2.CascadeClassifier(
                os.path.join(cascade_path, 'haarcascade_smile.xml')
            )
            
            print("✅ Face detection cascades loaded successfully")
            
        except Exception as e:
            print(f"⚠️ Could not load face cascades: {e}")
            print("Face detection will use fallback methods")
    
    def extract_face_features(self, image_path):
        """Extract detailed face features from an image"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Could not load image")
            
            # Convert to RGB for processing
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Detect faces
            faces = self.detect_faces(gray)
            
            if len(faces) == 0:
                print("⚠️ No faces detected, using full image")
                return self.analyze_full_image(image_rgb)
            
            # Use the largest face
            face = max(faces, key=lambda f: f[2] * f[3])
            x, y, w, h = face
            
            # Extract face region
            face_region = image_rgb[y:y+h, x:x+w]
            
            # Analyze face features
            features = self.analyze_face_region(face_region, gray[y:y+h, x:x+w])
            
            # Add face position info
            features['face_bounds'] = {'x': x, 'y': y, 'width': w, 'height': h}
            features['image_size'] = {'width': image.shape[1], 'height': image.shape[0]}
            
            return features
            
        except Exception as e:
            print(f"Error extracting face features: {e}")
            # Return basic fallback features
            return self.get_default_features()
    
    def detect_faces(self, gray_image):
        """Detect faces in grayscale image"""
        if self.face_cascade is None:
            return []
        
        try:
            faces = self.face_cascade.detectMultiScale(
                gray_image,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30)
            )
            return faces
        except Exception as e:
            print(f"Face detection error: {e}")
            return []
    
    def analyze_face_region(self, face_rgb, face_gray):
        """Analyze detailed features of a face region"""
        features = {
            'face_shape': self.determine_face_shape(face_rgb),
            'skin_tone': self.extract_skin_tone(face_rgb),
            'eye_features': self.analyze_eyes(face_gray),
            'facial_structure': self.analyze_facial_structure(face_rgb),
            'color_palette': self.extract_color_palette(face_rgb),
            'age_estimate': self.estimate_age_group(face_rgb),
            'gender_estimate': self.estimate_gender(face_rgb)
        }
        
        return features
    
    def determine_face_shape(self, face_image):
        """Determine face shape characteristics"""
        h, w = face_image.shape[:2]
        
        # Calculate face proportions
        aspect_ratio = w / h
        
        if aspect_ratio > 0.8:
            shape = "round"
        elif aspect_ratio < 0.7:
            shape = "oval"
        else:
            shape = "square"
        
        return {
            'shape': shape,
            'aspect_ratio': aspect_ratio,
            'width': w,
            'height': h
        }
    
    def extract_skin_tone(self, face_image):
        """Extract dominant skin tone from face"""
        try:
            # Convert to HSV for better skin detection
            hsv = cv2.cvtColor(face_image, cv2.COLOR_RGB2HSV)
            
            # Define skin color range in HSV
            lower_skin = np.array([0, 20, 70], dtype=np.uint8)
            upper_skin = np.array([20, 255, 255], dtype=np.uint8)
            
            # Create mask for skin pixels
            skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            # Get skin pixels
            skin_pixels = face_image[skin_mask > 0]
            
            if len(skin_pixels) > 0:
                # Calculate average skin color
                avg_color = np.mean(skin_pixels, axis=0)
                return {
                    'rgb': avg_color.tolist(),
                    'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
                    'tone_category': self.categorize_skin_tone(avg_color)
                }
            else:
                # Fallback to center region average
                center_region = face_image[face_image.shape[0]//3:2*face_image.shape[0]//3, 
                                         face_image.shape[1]//3:2*face_image.shape[1]//3]
                avg_color = np.mean(center_region.reshape(-1, 3), axis=0)
                return {
                    'rgb': avg_color.tolist(),
                    'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
                    'tone_category': self.categorize_skin_tone(avg_color)
                }
                
        except Exception as e:
            print(f"Skin tone extraction error: {e}")
            return {
                'rgb': [200, 180, 160],
                'hex': '#c8b4a0',
                'tone_category': 'medium'
            }
    
    def categorize_skin_tone(self, rgb_color):
        """Categorize skin tone into general categories"""
        # Simple categorization based on brightness
        brightness = np.mean(rgb_color)
        
        if brightness < 120:
            return 'dark'
        elif brightness < 180:
            return 'medium'
        else:
            return 'light'
    
    def analyze_eyes(self, face_gray):
        """Analyze eye features"""
        try:
            if self.eye_cascade is None:
                return {'detected': False, 'count': 0}
            
            eyes = self.eye_cascade.detectMultiScale(face_gray, 1.1, 5)
            
            return {
                'detected': len(eyes) > 0,
                'count': len(eyes),
                'positions': eyes.tolist() if len(eyes) > 0 else [],
                'eye_distance': self.calculate_eye_distance(eyes) if len(eyes) >= 2 else None
            }
            
        except Exception as e:
            print(f"Eye analysis error: {e}")
            return {'detected': False, 'count': 0}
    
    def calculate_eye_distance(self, eyes):
        """Calculate distance between eyes"""
        if len(eyes) < 2:
            return None
        
        # Get centers of first two eyes
        eye1_center = (eyes[0][0] + eyes[0][2]//2, eyes[0][1] + eyes[0][3]//2)
        eye2_center = (eyes[1][0] + eyes[1][2]//2, eyes[1][1] + eyes[1][3]//2)
        
        # Calculate distance
        distance = np.sqrt((eye1_center[0] - eye2_center[0])**2 + 
                          (eye1_center[1] - eye2_center[1])**2)
        
        return float(distance)
    
    def analyze_facial_structure(self, face_image):
        """Analyze overall facial structure"""
        h, w = face_image.shape[:2]
        
        # Divide face into regions for analysis
        upper_face = face_image[:h//3, :]  # Forehead area
        middle_face = face_image[h//3:2*h//3, :]  # Eyes/nose area
        lower_face = face_image[2*h//3:, :]  # Mouth/chin area
        
        return {
            'proportions': {
                'upper_third': h//3,
                'middle_third': h//3,
                'lower_third': h - 2*(h//3)
            },
            'symmetry_score': self.calculate_symmetry(face_image),
            'jawline_strength': self.analyze_jawline(lower_face)
        }
    
    def calculate_symmetry(self, face_image):
        """Calculate facial symmetry score"""
        try:
            h, w = face_image.shape[:2]
            
            # Split face in half
            left_half = face_image[:, :w//2]
            right_half = face_image[:, w//2:]
            
            # Flip right half
            right_half_flipped = cv2.flip(right_half, 1)
            
            # Resize to match if needed
            if left_half.shape != right_half_flipped.shape:
                min_w = min(left_half.shape[1], right_half_flipped.shape[1])
                left_half = left_half[:, :min_w]
                right_half_flipped = right_half_flipped[:, :min_w]
            
            # Calculate difference
            diff = cv2.absdiff(left_half, right_half_flipped)
            symmetry_score = 1.0 - (np.mean(diff) / 255.0)
            
            return float(max(0, min(1, symmetry_score)))
            
        except Exception as e:
            print(f"Symmetry calculation error: {e}")
            return 0.8  # Default reasonable symmetry
    
    def analyze_jawline(self, lower_face):
        """Analyze jawline characteristics"""
        try:
            # Convert to grayscale for edge detection
            gray = cv2.cvtColor(lower_face, cv2.COLOR_RGB2GRAY)
            
            # Apply edge detection
            edges = cv2.Canny(gray, 50, 150)
            
            # Count edge pixels in lower region (jawline area)
            lower_region = edges[edges.shape[0]//2:, :]
            edge_density = np.sum(lower_region > 0) / lower_region.size
            
            # Categorize jawline strength
            if edge_density > 0.1:
                strength = 'strong'
            elif edge_density > 0.05:
                strength = 'moderate'
            else:
                strength = 'soft'
            
            return {
                'strength': strength,
                'edge_density': float(edge_density)
            }
            
        except Exception as e:
            print(f"Jawline analysis error: {e}")
            return {'strength': 'moderate', 'edge_density': 0.07}
    
    def extract_color_palette(self, face_image):
        """Extract dominant colors from face"""
        try:
            # Reshape image to list of pixels
            pixels = face_image.reshape(-1, 3)
            
            # Use k-means clustering to find dominant colors
            from sklearn.cluster import KMeans
            
            # Cluster into 5 dominant colors
            kmeans = KMeans(n_clusters=5, random_state=42, n_init=10)
            kmeans.fit(pixels)
            
            colors = kmeans.cluster_centers_.astype(int)
            
            return {
                'dominant_colors': colors.tolist(),
                'hex_colors': ['#{:02x}{:02x}{:02x}'.format(c[0], c[1], c[2]) for c in colors]
            }
            
        except ImportError:
            # Fallback without sklearn
            return self.extract_color_palette_simple(face_image)
        except Exception as e:
            print(f"Color palette extraction error: {e}")
            return self.extract_color_palette_simple(face_image)
    
    def extract_color_palette_simple(self, face_image):
        """Simple color palette extraction without sklearn"""
        # Sample colors from different regions
        h, w = face_image.shape[:2]
        
        regions = [
            face_image[h//4:h//2, w//4:3*w//4],  # Upper face
            face_image[h//2:3*h//4, w//4:3*w//4],  # Middle face
            face_image[3*h//4:, w//4:3*w//4],  # Lower face
        ]
        
        colors = []
        for region in regions:
            if region.size > 0:
                avg_color = np.mean(region.reshape(-1, 3), axis=0).astype(int)
                colors.append(avg_color.tolist())
        
        return {
            'dominant_colors': colors,
            'hex_colors': ['#{:02x}{:02x}{:02x}'.format(c[0], c[1], c[2]) for c in colors]
        }
    
    def estimate_age_group(self, face_image):
        """Estimate age group from facial features"""
        # Simple heuristic based on skin texture and facial proportions
        h, w = face_image.shape[:2]
        
        # Analyze skin texture (simplified)
        gray = cv2.cvtColor(face_image, cv2.COLOR_RGB2GRAY)
        texture_variance = np.var(gray)
        
        # Categorize based on texture variance (smoother = younger)
        if texture_variance < 200:
            return 'young'  # 18-30
        elif texture_variance < 400:
            return 'adult'  # 30-50
        else:
            return 'mature'  # 50+
    
    def estimate_gender(self, face_image):
        """Estimate gender from facial features"""
        # Simple heuristic based on facial structure
        h, w = face_image.shape[:2]
        
        # Analyze jawline area
        lower_face = face_image[2*h//3:, :]
        
        # Convert to grayscale for edge analysis
        gray_lower = cv2.cvtColor(lower_face, cv2.COLOR_RGB2GRAY)
        edges = cv2.Canny(gray_lower, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        
        # Simple classification (this is very basic)
        if edge_density > 0.08:
            return 'masculine'  # Stronger jawline typically
        else:
            return 'feminine'  # Softer features typically
    
    def analyze_full_image(self, image_rgb):
        """Analyze full image when no face is detected"""
        return {
            'face_shape': {'shape': 'unknown', 'aspect_ratio': 1.0},
            'skin_tone': self.extract_average_color(image_rgb),
            'eye_features': {'detected': False, 'count': 0},
            'facial_structure': {'symmetry_score': 0.8},
            'color_palette': self.extract_color_palette_simple(image_rgb),
            'age_estimate': 'adult',
            'gender_estimate': 'neutral',
            'face_bounds': None,
            'full_image_analysis': True
        }
    
    def extract_average_color(self, image):
        """Extract average color from image"""
        avg_color = np.mean(image.reshape(-1, 3), axis=0)
        return {
            'rgb': avg_color.tolist(),
            'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
            'tone_category': self.categorize_skin_tone(avg_color)
        }
    
    def get_default_features(self):
        """Return default features when analysis fails"""
        return {
            'face_shape': {'shape': 'oval', 'aspect_ratio': 0.75},
            'skin_tone': {'rgb': [200, 180, 160], 'hex': '#c8b4a0', 'tone_category': 'medium'},
            'eye_features': {'detected': False, 'count': 0},
            'facial_structure': {'symmetry_score': 0.8},
            'color_palette': {'dominant_colors': [[200, 180, 160]], 'hex_colors': ['#c8b4a0']},
            'age_estimate': 'adult',
            'gender_estimate': 'neutral',
            'analysis_failed': True
        }
    
    def save_face_analysis(self, features, output_path):
        """Save face analysis results to JSON file"""
        try:
            with open(output_path, 'w') as f:
                json.dump(features, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving face analysis: {e}")
            return False
