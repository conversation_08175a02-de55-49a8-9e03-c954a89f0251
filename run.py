"""
Run script for the AI Content Generation Pipeline.
"""

import os
import sys
import subprocess
import argparse
from dotenv import load_dotenv

def start_comfyui():
    """Start ComfyUI in a separate process."""
    comfyui_path = os.path.join('external', 'ComfyUI')
    
    if not os.path.exists(comfyui_path):
        print("ComfyUI not found. Please run setup.py first.")
        return None
    
    print("Starting ComfyUI...")
    process = subprocess.Popen(
        [sys.executable, 'main.py'],
        cwd=comfyui_path,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    return process

def start_flask_app():
    """Start the Flask application."""
    print("Starting Flask application...")
    process = subprocess.Popen(
        [sys.executable, os.path.join('src', 'app.py')],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    return process

def main():
    """Main function to run the application."""
    parser = argparse.ArgumentParser(description='Run the AI Content Generation Pipeline')
    parser.add_argument('--no-comfyui', action='store_true', help='Do not start ComfyUI')
    parser.add_argument('--port', type=int, default=5000, help='Port for the Flask application')
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    
    # Set port
    os.environ['PORT'] = str(args.port)
    
    # Start ComfyUI if needed
    comfyui_process = None
    if not args.no_comfyui:
        comfyui_process = start_comfyui()
    
    # Start Flask application
    flask_process = start_flask_app()
    
    try:
        # Wait for processes to complete
        flask_process.wait()
    except KeyboardInterrupt:
        print("\nShutting down...")
    finally:
        # Clean up processes
        if flask_process:
            flask_process.terminate()
        
        if comfyui_process:
            comfyui_process.terminate()
    
    print("Application stopped.")

if __name__ == '__main__':
    main()
