<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Load Real Characters</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .viewer {
            width: 100%;
            height: 500px;
            background: #1a1a1a;
            border-radius: 10px;
            margin: 20px 0;
        }
        .controls {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .file-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .upload-area {
            border: 2px dashed rgba(255,255,255,0.5);
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: white;
            background: rgba(255,255,255,0.1);
        }
        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Real Character Viewer</h1>
        <p>Load and view your generated GLB character files</p>

        <!-- File Upload -->
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <h3>📁 Upload GLB File</h3>
            <p>Click here or drag & drop a GLB file to view it in 3D</p>
            <input type="file" id="fileInput" accept=".glb,.gltf" onchange="loadFile(event)">
        </div>

        <!-- Generated Files -->
        <div class="controls">
            <h3>🎯 Your Generated Characters</h3>
            <div class="file-list" id="fileList">
                <button class="file-btn" onclick="loadCharacter('test_character_1_robot.glb')">
                    🤖 Robot Character
                </button>
                <button class="file-btn" onclick="loadCharacter('test_character_2_alien.glb')">
                    👽 Alien Character
                </button>
                <button class="file-btn" onclick="loadCharacter('test_character_3_warrior.glb')">
                    ⚔️ Warrior Character
                </button>
                <button class="file-btn" onclick="loadCharacter('test_procedural_character.glb')">
                    🎮 Procedural Character
                </button>
            </div>
        </div>

        <!-- 3D Viewer -->
        <div id="viewer" class="viewer"></div>

        <!-- Info -->
        <div class="controls">
            <h3>📊 Model Info</h3>
            <div id="modelInfo">
                <p>Load a character to see details</p>
            </div>
        </div>
    </div>

    <script>
        let scene, camera, renderer, controls;
        let currentModel = null;

        // Initialize 3D viewer
        function init3DViewer() {
            const container = document.getElementById('viewer');
            
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x1a1a1a);
            
            // Camera
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.set(3, 2, 3);
            
            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.shadowMap.enabled = true;
            container.appendChild(renderer.domElement);
            
            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            
            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(5, 5, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            // Grid
            const gridHelper = new THREE.GridHelper(10, 10);
            scene.add(gridHelper);
            
            animate();
        }

        // Load character file
        function loadCharacter(filename) {
            const loader = new THREE.GLTFLoader();
            
            loader.load(filename, (gltf) => {
                // Remove existing model
                if (currentModel) {
                    scene.remove(currentModel);
                }
                
                currentModel = gltf.scene;
                
                // Center and scale model
                const box = new THREE.Box3().setFromObject(currentModel);
                const center = box.getCenter(new THREE.Vector3());
                const size = box.getSize(new THREE.Vector3());
                
                currentModel.position.sub(center);
                
                const maxDim = Math.max(size.x, size.y, size.z);
                if (maxDim > 0) {
                    const scale = 2 / maxDim;
                    currentModel.scale.setScalar(scale);
                }
                
                scene.add(currentModel);
                
                // Update info
                updateModelInfo(gltf, filename);
                
                console.log('Character loaded:', filename);
                
            }, undefined, (error) => {
                console.error('Error loading character:', error);
                alert(`Could not load ${filename}. Make sure the file exists in the same directory.`);
            });
        }

        // Load file from input
        function loadFile(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const arrayBuffer = e.target.result;
                const loader = new THREE.GLTFLoader();
                
                loader.parse(arrayBuffer, '', (gltf) => {
                    // Remove existing model
                    if (currentModel) {
                        scene.remove(currentModel);
                    }
                    
                    currentModel = gltf.scene;
                    
                    // Center and scale model
                    const box = new THREE.Box3().setFromObject(currentModel);
                    const center = box.getCenter(new THREE.Vector3());
                    const size = box.getSize(new THREE.Vector3());
                    
                    currentModel.position.sub(center);
                    
                    const maxDim = Math.max(size.x, size.y, size.z);
                    if (maxDim > 0) {
                        const scale = 2 / maxDim;
                        currentModel.scale.setScalar(scale);
                    }
                    
                    scene.add(currentModel);
                    
                    // Update info
                    updateModelInfo(gltf, file.name);
                    
                    console.log('File loaded:', file.name);
                    
                }, (error) => {
                    console.error('Error parsing file:', error);
                    alert('Error loading file. Make sure it\'s a valid GLB/GLTF file.');
                });
            };
            
            reader.readAsArrayBuffer(file);
        }

        // Update model info
        function updateModelInfo(gltf, filename) {
            let vertices = 0;
            let faces = 0;
            let materials = 0;
            
            gltf.scene.traverse((child) => {
                if (child.isMesh) {
                    if (child.geometry) {
                        if (child.geometry.attributes.position) {
                            vertices += child.geometry.attributes.position.count;
                        }
                        if (child.geometry.index) {
                            faces += child.geometry.index.count / 3;
                        }
                    }
                    if (child.material) {
                        materials++;
                    }
                }
            });
            
            const info = document.getElementById('modelInfo');
            info.innerHTML = `
                <p><strong>File:</strong> ${filename}</p>
                <p><strong>Vertices:</strong> ${vertices.toLocaleString()}</p>
                <p><strong>Faces:</strong> ${Math.floor(faces).toLocaleString()}</p>
                <p><strong>Materials:</strong> ${materials}</p>
                <p><strong>Animations:</strong> ${gltf.animations ? gltf.animations.length : 0}</p>
                <p><strong>File Size:</strong> Ready for games and 3D software</p>
            `;
        }

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            if (currentModel) {
                currentModel.rotation.y += 0.005;
            }
            
            if (controls) controls.update();
            if (renderer && scene && camera) renderer.render(scene, camera);
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                const container = document.getElementById('viewer');
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        });

        // Initialize on load
        window.addEventListener('load', () => {
            init3DViewer();
            console.log('Real Character Viewer initialized');
        });
    </script>
</body>
</html>
