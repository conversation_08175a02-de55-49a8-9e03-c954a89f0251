{"last_node_id": 353, "last_link_id": 700, "nodes": [{"id": 141, "type": "PreviewImage", "pos": {"0": 4238.80078125, "1": -488}, "size": {"0": 466.0791320800781, "1": 510.5869445800781}, "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 287}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 134, "type": "ImageCrop", "pos": {"0": 3224, "1": 78}, "size": {"0": 315, "1": 130}, "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 280}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [283, 289], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [800, 1000, 57, 0]}, {"id": 149, "type": "Reroute", "pos": {"0": 4072, "1": 262}, "size": [75, 26], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 292}], "outputs": [{"name": "", "type": "IMAGE", "links": [293, 294], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 83, "type": "UpscaleModelLoader", "pos": {"0": 1830, "1": 100}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [118], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UpscaleModelLoader"}, "widgets_values": ["4x-ClearRealityV1.pth"]}, {"id": 84, "type": "PreviewImage", "pos": {"0": 1815, "1": -488}, "size": {"0": 486.7819519042969, "1": 529.9662475585938}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 386}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 97, "type": "FromBasicPipe_v2", "pos": {"0": 1519, "1": 1268}, "size": {"0": 267, "1": 126}, "flags": {"collapsed": true}, "order": 28, "mode": 0, "inputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "link": 149}], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "links": [], "slot_index": 0, "shape": 3}, {"name": "model", "type": "MODEL", "links": [150], "slot_index": 1, "shape": 3}, {"name": "clip", "type": "CLIP", "links": null, "slot_index": 2, "shape": 3}, {"name": "vae", "type": "VAE", "links": [153], "slot_index": 3, "shape": 3}, {"name": "positive", "type": "CONDITIONING", "links": [], "slot_index": 4, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [], "slot_index": 5, "shape": 3}], "properties": {"Node name for S&R": "FromBasicPipe_v2"}}, {"id": 160, "type": "DualCLIPLoader", "pos": {"0": -125.42578125, "1": -296.70538330078125}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [313, 329, 343, 362], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp8_e4m3fn.safetensors", "clip_l.safetensors", "flux"], "color": "#323", "bgcolor": "#535"}, {"id": 129, "type": "ImageScale", "pos": {"0": 3224, "1": 262}, "size": {"0": 315, "1": 130}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 388}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [280, 284, 292], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 2048, 2048, "disabled"]}, {"id": 95, "type": "ToBasicPipe", "pos": {"0": 685, "1": 1267}, "size": {"0": 241.79998779296875, "1": 106}, "flags": {"collapsed": true}, "order": 24, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 342}, {"name": "clip", "type": "CLIP", "link": 343}, {"name": "vae", "type": "VAE", "link": 344}, {"name": "positive", "type": "CONDITIONING", "link": 340}, {"name": "negative", "type": "CONDITIONING", "link": 341}], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "links": [149], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ToBasicPipe"}}, {"id": 170, "type": "PrimitiveNode", "pos": {"0": -125.42578125, "1": -36.70539474487305}, "size": {"0": 210, "1": 82}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [319, 322], "slot_index": 0, "widget": {"name": "width"}}], "title": "width", "properties": {"Run widget replace on values": false}, "widgets_values": [1280, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 171, "type": "PrimitiveNode", "pos": {"0": -125.42578125, "1": 93.29461669921875}, "size": {"0": 210, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [320, 323], "slot_index": 0, "widget": {"name": "height"}}], "title": "height", "properties": {"Run widget replace on values": false}, "widgets_values": [1280, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 180, "type": "LoadImage", "pos": {"0": 685, "1": -196.70538330078125}, "size": {"0": 315, "1": 314}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [338], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["Pose_sheet_v02.png", "image"], "color": "#223", "bgcolor": "#335"}, {"id": 159, "type": "VAELoader", "pos": {"0": -125.42578125, "1": -146.70538330078125}, "size": {"0": 311.81634521484375, "1": 60.429901123046875}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [327, 331, 344, 359], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"], "color": "#323", "bgcolor": "#535"}, {"id": 167, "type": "EmptySD3LatentImage", "pos": {"0": 234.57423400878906, "1": -136.70538330078125}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 319, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 320, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [404], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1280, 1280, 1]}, {"id": 178, "type": "SamplerCustomAdvanced", "pos": {"0": 1035, "1": 143}, "size": {"0": 270, "1": 330}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 332, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 333, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 334, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 335, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 404, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": [330], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "color": "#323", "bgcolor": "#535"}, {"id": 138, "type": "PreviewImage", "pos": {"0": 3224, "1": -488}, "size": {"0": 466.0791320800781, "1": 510.5869445800781}, "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 283}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 157, "type": "CLIPTextEncode", "pos": {"0": 235, "1": -417}, "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 313}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [324], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a character sheet, white background, multiple views, from multiple angles, visible face, portrait, the american woman is wearing a coat, dressed in autumn fashion, neutral expression, it is a masterpiece, photography, american woman, blonde", true], "color": "#232", "bgcolor": "#353"}, {"id": 163, "type": "BasicScheduler", "pos": {"0": 235, "1": 253}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 315, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [335], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", 25, 1]}, {"id": 165, "type": "RandomNoise", "pos": {"0": 234.57423400878906, "1": 3.2946057319641113}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [332], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [671983948663894, "fixed"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 162, "type": "KSamplerSelect", "pos": {"0": 234.57423400878906, "1": 143.29461669921875}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [334], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["deis"]}, {"id": 258, "type": "PreviewImage", "pos": {"0": 5110, "1": -488}, "size": {"0": 439.9931945800781, "1": 493.07720947265625}, "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 496}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 142, "type": "ImageCrop", "pos": {"0": 4239, "1": 78}, "size": {"0": 315, "1": 130}, "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 293}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [287, 291], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [1000, 1000, 1184, 1074]}, {"id": 139, "type": "ImageCrop", "pos": {"0": 3711, "1": 78}, "size": {"0": 315, "1": 130}, "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 284}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [285, 290], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [1000, 1000, 1158, 42]}, {"id": 150, "type": "Reroute", "pos": {"0": 4557, "1": 265}, "size": [75, 26], "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 294}], "outputs": [{"name": "", "type": "IMAGE", "links": [498, 587], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 140, "type": "PreviewImage", "pos": {"0": 3711, "1": -488}, "size": {"0": 466.0791320800781, "1": 510.5869445800781}, "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 285}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 299, "type": "PreviewImage", "pos": {"0": 4751, "1": -488}, "size": {"0": 300.5074157714844, "1": 502.5292053222656}, "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 588}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 18, "type": "UltralyticsDetectorProvider", "pos": {"0": 2460, "1": 90}, "size": {"0": 315, "1": 78}, "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [369], "slot_index": 0, "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 298, "type": "ImageCrop", "pos": {"0": 4737, "1": 78}, "size": {"0": 315, "1": 130}, "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 587}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [588, 589], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [500, 1000, 785, 42]}, {"id": 166, "type": "FluxGuidance", "pos": {"0": 685, "1": -307}, "size": {"0": 317.4000244140625, "1": 58}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 318}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [317, 381, 383], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [4.2], "color": "#233", "bgcolor": "#355"}, {"id": 161, "type": "UNETLoader", "pos": {"0": -125.42578125, "1": -426.70538330078125}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [321], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev-fp8.safetensors", "fp8_e4m3fn"], "color": "#323", "bgcolor": "#535"}, {"id": 185, "type": "ToDetailerPipe", "pos": {"0": 2540, "1": 1370}, "size": {"0": 400, "1": 289}, "flags": {"collapsed": true}, "order": 27, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 358}, {"name": "clip", "type": "CLIP", "link": 362}, {"name": "vae", "type": "VAE", "link": 359}, {"name": "positive", "type": "CONDITIONING", "link": 383}, {"name": "negative", "type": "CONDITIONING", "link": 380}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 369}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": 374}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [357], "shape": 3}], "properties": {"Node name for S&R": "ToDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text", true]}, {"id": 259, "type": "ImageCrop", "pos": {"0": 5230, "1": 78}, "size": {"0": 315, "1": 130}, "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 498}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [496, 590, 620, 621, 622, 623], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 1369, 1031]}, {"id": 303, "type": "Simple String", "pos": {"0": -720, "1": -550}, "size": {"0": 479.66900634765625, "1": 58}, "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [612], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Simple String"}, "widgets_values": ["character_sheet_flux"], "color": "#432", "bgcolor": "#653"}, {"id": 177, "type": "VAEDecode", "pos": {"0": 1356, "1": 191}, "size": {"0": 210, "1": 46}, "flags": {"collapsed": true}, "order": 32, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 330}, {"name": "vae", "type": "VAE", "link": 331}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [339, 393], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 164, "type": "BasicGuider", "pos": {"0": 685, "1": -407}, "size": {"0": 222.3482666015625, "1": 46}, "flags": {"collapsed": true}, "order": 26, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 316, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 317, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [333], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}}, {"id": 196, "type": "SaveImage", "pos": {"0": 1108, "1": -488}, "size": {"0": 497.5215148925781, "1": 523.5535278320312}, "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 393}, {"name": "filename_prefix", "type": "STRING", "link": 629, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 314, "type": "Reroute", "pos": {"0": -184, "1": 1446}, "size": [75, 26], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 612, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [628], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 324, "type": "Reroute", "pos": {"0": 868, "1": 1446}, "size": [75, 26], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 628, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [629, 631], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 145, "type": "SaveImage", "pos": {"0": 3224, "1": 478}, "size": {"0": 315, "1": 270}, "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 289}, {"name": "filename_prefix", "type": "STRING", "link": 634, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 146, "type": "SaveImage", "pos": {"0": 3711, "1": 478}, "size": {"0": 315, "1": 270}, "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 290}, {"name": "filename_prefix", "type": "STRING", "link": 636, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 147, "type": "SaveImage", "pos": {"0": 4239, "1": 471}, "size": {"0": 315, "1": 270}, "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 291}, {"name": "filename_prefix", "type": "STRING", "link": 638, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 300, "type": "SaveImage", "pos": {"0": 4721, "1": 471}, "size": {"0": 315, "1": 270}, "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 589}, {"name": "filename_prefix", "type": "STRING", "link": 640, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 325, "type": "Reroute", "pos": {"0": 2221, "1": 1446}, "size": [75, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 631, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [632, 633], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 327, "type": "Reroute", "pos": {"0": 3522, "1": 1446}, "size": [75, 26], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 635, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [636, 637], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 328, "type": "Reroute", "pos": {"0": 4121, "1": 1446}, "size": [75, 26], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 637, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [638, 639], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 329, "type": "Reroute", "pos": {"0": 4579, "1": 1446}, "size": [75, 26], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 639, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [640, 641], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 330, "type": "Reroute", "pos": {"0": 5088, "1": 1446}, "size": [75, 26], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 641, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [642, 643], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 82, "type": "UltimateSDUpscale", "pos": {"0": 1830, "1": 211}, "size": {"0": 315, "1": 826}, "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 339}, {"name": "model", "type": "MODEL", "link": 150}, {"name": "positive", "type": "CONDITIONING", "link": 381}, {"name": "negative", "type": "CONDITIONING", "link": 382}, {"name": "vae", "type": "VAE", "link": 153}, {"name": "upscale_model", "type": "UPSCALE_MODEL", "link": 118}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [365, 386], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UltimateSDUpscale"}, "widgets_values": [2, 384340151733828, "fixed", 22, 1, "deis", "beta", 0.2, "Linear", 1024, 1024, 8, 32, "None", 1, 64, 8, 16, true, false]}, {"id": 183, "type": "FaceDetailerPipe", "pos": {"0": 2460, "1": 210}, "size": {"0": 346, "1": 994}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 365}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 357}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "link": null}], "outputs": [{"name": "image", "type": "IMAGE", "links": [364, 388], "slot_index": 0, "shape": 3}, {"name": "cropped_refined", "type": "IMAGE", "links": [695], "slot_index": 1, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": null, "shape": 6}, {"name": "mask", "type": "MASK", "links": null, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": null, "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "links": null, "shape": 6}], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [512, true, 1024, 12346, "fixed", 20, 1, "deis", "beta", 0.22, 5, true, true, 0.5, 20, 3, "center-1", 0, 0.93, 0, 0.7, "False", 10, 0.2, 1, false, 20]}, {"id": 326, "type": "Reroute", "pos": {"0": 3046, "1": 1446}, "size": [75, 26], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 633, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [634, 635, 652], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 168, "type": "Note", "pos": {"0": -586, "1": -190}, "size": {"0": 336, "1": 288}, "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["If you get an error in any of the nodes above make sure the files are in the correct directories.\n\nSee the top of the examples page for the links : https://comfyanonymous.github.io/ComfyUI_examples/flux/\n\nflux1-dev.safetensors goes in: ComfyUI/models/unet/\n\nt5xxl_fp16.safetensors and clip_l.safetensors go in: ComfyUI/models/clip/\n\nae.safetensors goes in: ComfyUI/models/vae/\n\n\nTip: You can set the weight_dtype above to one of the fp8 types if you have memory issues."], "color": "#432", "bgcolor": "#653"}, {"id": 173, "type": "ControlNetApplySD3", "pos": {"0": 690, "1": 160}, "size": {"0": 315, "1": 186}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 324}, {"name": "negative", "type": "CONDITIONING", "link": 325}, {"name": "control_net", "type": "CONTROL_NET", "link": 698}, {"name": "vae", "type": "VAE", "link": 327}, {"name": "image", "type": "IMAGE", "link": 338}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [318, 340], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [341, 380, 382], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "widgets_values": [0.66, 0, 0.4], "color": "#223", "bgcolor": "#335"}, {"id": 175, "type": "CLIPTextEncode", "pos": {"0": 235, "1": -207}, "size": {"0": 285.6000061035156, "1": 81}, "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 329}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [325], "slot_index": 0}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["", true], "color": "#322", "bgcolor": "#533"}, {"id": 301, "type": "SaveImage", "pos": {"0": 5203, "1": 469}, "size": {"0": 315, "1": 270}, "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 590}, {"name": "filename_prefix", "type": "STRING", "link": 642, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 316, "type": "ExpressionEditor", "pos": {"0": 5730, "1": 100}, "size": {"0": 260.81048583984375, "1": 690}, "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "src_image", "type": "IMAGE", "link": 620}, {"name": "motion_link", "type": "EDITOR_LINK", "link": null}, {"name": "sample_image", "type": "IMAGE", "link": null}, {"name": "add_exp", "type": "EXP_DATA", "link": null}], "outputs": [{"name": "image", "type": "IMAGE", "links": [675], "slot_index": 0, "shape": 3}, {"name": "motion_link", "type": "EDITOR_LINK", "links": null, "shape": 3}, {"name": "save_exp", "type": "EXP_DATA", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ExpressionEditor"}, "widgets_values": [0, 0, 0, 0, 0, 23.5, 0, 0, 0, 0, 0, 0, 1, 1, "OnlyExpression", 1.7000000000000002]}, {"id": 337, "type": "SaveImage", "pos": {"0": 5714, "1": -490}, "size": {"0": 399.6399841308594, "1": 436.32391357421875}, "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 675}, {"name": "filename_prefix", "type": "STRING", "link": 687, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 341, "type": "SaveImage", "pos": {"0": 6188, "1": -490}, "size": {"0": 400, "1": 440}, "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 684}, {"name": "filename_prefix", "type": "STRING", "link": 689, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 342, "type": "SaveImage", "pos": {"0": 6734, "1": -490}, "size": {"0": 400, "1": 440}, "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 685}, {"name": "filename_prefix", "type": "STRING", "link": 691, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 343, "type": "SaveImage", "pos": {"0": 7314, "1": -490}, "size": {"0": 400, "1": 440}, "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 686}, {"name": "filename_prefix", "type": "STRING", "link": 694, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 317, "type": "ExpressionEditor", "pos": {"0": 6190, "1": 100}, "size": {"0": 260.81048583984375, "1": 690}, "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "src_image", "type": "IMAGE", "link": 621}, {"name": "motion_link", "type": "EDITOR_LINK", "link": null}, {"name": "sample_image", "type": "IMAGE", "link": null}, {"name": "add_exp", "type": "EXP_DATA", "link": null}], "outputs": [{"name": "image", "type": "IMAGE", "links": [684], "slot_index": 0, "shape": 3}, {"name": "motion_link", "type": "EDITOR_LINK", "links": null, "shape": 3}, {"name": "save_exp", "type": "EXP_DATA", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ExpressionEditor"}, "widgets_values": [-8, -8, 4, 0, 0, 0, 0, 0, 0, 8.1, 0, 1, 1, 1, "OnlyExpression", 1.7000000000000002]}, {"id": 318, "type": "ExpressionEditor", "pos": {"0": 6730, "1": 100}, "size": {"0": 260.81048583984375, "1": 690}, "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "src_image", "type": "IMAGE", "link": 622}, {"name": "motion_link", "type": "EDITOR_LINK", "link": null}, {"name": "sample_image", "type": "IMAGE", "link": null}, {"name": "add_exp", "type": "EXP_DATA", "link": null}], "outputs": [{"name": "image", "type": "IMAGE", "links": [685], "slot_index": 0, "shape": 3}, {"name": "motion_link", "type": "EDITOR_LINK", "links": null, "shape": 3}, {"name": "save_exp", "type": "EXP_DATA", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ExpressionEditor"}, "widgets_values": [14.600000000000001, 0, 0, 5, 15, 0, 0, 0, 0, 0, 0, 0, 1, 1, "OnlyExpression", 1.7000000000000002]}, {"id": 319, "type": "ExpressionEditor", "pos": {"0": 7310, "1": 100}, "size": {"0": 260.81048583984375, "1": 690}, "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "src_image", "type": "IMAGE", "link": 623}, {"name": "motion_link", "type": "EDITOR_LINK", "link": null}, {"name": "sample_image", "type": "IMAGE", "link": null}, {"name": "add_exp", "type": "EXP_DATA", "link": null}], "outputs": [{"name": "image", "type": "IMAGE", "links": [686], "slot_index": 0, "shape": 3}, {"name": "motion_link", "type": "EDITOR_LINK", "links": null, "slot_index": 1, "shape": 3}, {"name": "save_exp", "type": "EXP_DATA", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ExpressionEditor"}, "widgets_values": [0, -20, 0, 5, 15, 0, 0, 0, 120, 0, 15, 1.3, 1, 1, "OnlyExpression", 1.7000000000000002]}, {"id": 313, "type": "Reroute", "pos": {"0": 5545, "1": 1446}, "size": [75, 26], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 643, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [687, 688], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 344, "type": "Reroute", "pos": {"0": 6018, "1": 1446}, "size": [75, 26], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 688, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [689, 692], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 346, "type": "Reroute", "pos": {"0": 6498, "1": 1445}, "size": [75, 26], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 692, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [691, 693], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 347, "type": "Reroute", "pos": {"0": 7113, "1": 1446}, "size": [75, 26], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 693, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [694], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 87, "type": "SaveImage", "pos": {"0": 3224, "1": 876}, "size": {"0": 315, "1": 270}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 695}, {"name": "filename_prefix", "type": "STRING", "link": 652, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["faceRefine"]}, {"id": 179, "type": "SaveImage", "pos": {"0": 2440, "1": -490}, "size": {"0": 497.5215148925781, "1": 523.5535278320312}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 364}, {"name": "filename_prefix", "type": "STRING", "link": 632, "widget": {"name": "filename_prefix"}}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 187, "type": "CoreMLDetailerHookProvider", "pos": {"0": 2540, "1": 1330}, "size": {"0": 327.5999755859375, "1": 58}, "flags": {"collapsed": true}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "DETAILER_HOOK", "type": "DETAILER_HOOK", "links": [374], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CoreMLDetailerHookProvider"}, "widgets_values": ["512x768"]}, {"id": 169, "type": "ModelSamplingFlux", "pos": {"0": 235, "1": 393}, "size": {"0": 315, "1": 130}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 321, "slot_index": 0}, {"name": "width", "type": "INT", "link": 322, "slot_index": 1, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 323, "slot_index": 2, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [315, 316, 342, 358], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, 1280, 1280]}, {"id": 302, "type": "Fast Groups Muter (rgthree)", "pos": {"0": -720, "1": -440}, "size": {"0": 486.3753967285156, "1": 178}, "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "OPT_CONNECTION", "type": "*", "links": null}], "properties": {"matchColors": "", "matchTitle": "", "showNav": true, "sort": "alphanumeric", "customSortAlphabet": "", "toggleRestriction": "default"}, "color": "#432", "bgcolor": "#653"}, {"id": 348, "type": "ControlNetLoader", "pos": {"0": 688, "1": 395}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [698], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["UnionFlux.safetensors"], "color": "#223", "bgcolor": "#335"}], "links": [[118, 83, 0, 82, 5, "UPSCALE_MODEL"], [149, 95, 0, 97, 0, "BASIC_PIPE"], [150, 97, 1, 82, 1, "MODEL"], [153, 97, 3, 82, 4, "VAE"], [280, 129, 0, 134, 0, "IMAGE"], [283, 134, 0, 138, 0, "IMAGE"], [284, 129, 0, 139, 0, "IMAGE"], [285, 139, 0, 140, 0, "IMAGE"], [287, 142, 0, 141, 0, "IMAGE"], [289, 134, 0, 145, 0, "IMAGE"], [290, 139, 0, 146, 0, "IMAGE"], [291, 142, 0, 147, 0, "IMAGE"], [292, 129, 0, 149, 0, "*"], [293, 149, 0, 142, 0, "IMAGE"], [294, 149, 0, 150, 0, "*"], [313, 160, 0, 157, 0, "CLIP"], [315, 169, 0, 163, 0, "MODEL"], [316, 169, 0, 164, 0, "MODEL"], [317, 166, 0, 164, 1, "CONDITIONING"], [318, 173, 0, 166, 0, "CONDITIONING"], [319, 170, 0, 167, 0, "INT"], [320, 171, 0, 167, 1, "INT"], [321, 161, 0, 169, 0, "MODEL"], [322, 170, 0, 169, 1, "INT"], [323, 171, 0, 169, 2, "INT"], [324, 157, 0, 173, 0, "CONDITIONING"], [325, 175, 0, 173, 1, "CONDITIONING"], [327, 159, 0, 173, 3, "VAE"], [329, 160, 0, 175, 0, "CLIP"], [330, 178, 1, 177, 0, "LATENT"], [331, 159, 0, 177, 1, "VAE"], [332, 165, 0, 178, 0, "NOISE"], [333, 164, 0, 178, 1, "GUIDER"], [334, 162, 0, 178, 2, "SAMPLER"], [335, 163, 0, 178, 3, "SIGMAS"], [338, 180, 0, 173, 4, "IMAGE"], [339, 177, 0, 82, 0, "IMAGE"], [340, 173, 0, 95, 3, "CONDITIONING"], [341, 173, 1, 95, 4, "CONDITIONING"], [342, 169, 0, 95, 0, "MODEL"], [343, 160, 0, 95, 1, "CLIP"], [344, 159, 0, 95, 2, "VAE"], [357, 185, 0, 183, 1, "DETAILER_PIPE"], [358, 169, 0, 185, 0, "MODEL"], [359, 159, 0, 185, 2, "VAE"], [362, 160, 0, 185, 1, "CLIP"], [364, 183, 0, 179, 0, "IMAGE"], [365, 82, 0, 183, 0, "IMAGE"], [369, 18, 0, 185, 5, "BBOX_DETECTOR"], [374, 187, 0, 185, 8, "DETAILER_HOOK"], [380, 173, 1, 185, 4, "CONDITIONING"], [381, 166, 0, 82, 2, "CONDITIONING"], [382, 173, 1, 82, 3, "CONDITIONING"], [383, 166, 0, 185, 3, "CONDITIONING"], [386, 82, 0, 84, 0, "IMAGE"], [388, 183, 0, 129, 0, "IMAGE"], [393, 177, 0, 196, 0, "IMAGE"], [404, 167, 0, 178, 4, "LATENT"], [496, 259, 0, 258, 0, "IMAGE"], [498, 150, 0, 259, 0, "IMAGE"], [587, 150, 0, 298, 0, "IMAGE"], [588, 298, 0, 299, 0, "IMAGE"], [589, 298, 0, 300, 0, "IMAGE"], [590, 259, 0, 301, 0, "IMAGE"], [612, 303, 0, 314, 0, "*"], [620, 259, 0, 316, 0, "IMAGE"], [621, 259, 0, 317, 0, "IMAGE"], [622, 259, 0, 318, 0, "IMAGE"], [623, 259, 0, 319, 0, "IMAGE"], [628, 314, 0, 324, 0, "*"], [629, 324, 0, 196, 1, "STRING"], [631, 324, 0, 325, 0, "*"], [632, 325, 0, 179, 1, "STRING"], [633, 325, 0, 326, 0, "*"], [634, 326, 0, 145, 1, "STRING"], [635, 326, 0, 327, 0, "*"], [636, 327, 0, 146, 1, "STRING"], [637, 327, 0, 328, 0, "*"], [638, 328, 0, 147, 1, "STRING"], [639, 328, 0, 329, 0, "*"], [640, 329, 0, 300, 1, "STRING"], [641, 329, 0, 330, 0, "*"], [642, 330, 0, 301, 1, "STRING"], [643, 330, 0, 313, 0, "*"], [652, 326, 0, 87, 1, "STRING"], [675, 316, 0, 337, 0, "IMAGE"], [684, 317, 0, 341, 0, "IMAGE"], [685, 318, 0, 342, 0, "IMAGE"], [686, 319, 0, 343, 0, "IMAGE"], [687, 313, 0, 337, 1, "STRING"], [688, 313, 0, 344, 0, "*"], [689, 344, 0, 341, 1, "STRING"], [691, 346, 0, 342, 1, "STRING"], [692, 344, 0, 346, 0, "*"], [693, 346, 0, 347, 0, "*"], [694, 347, 0, 343, 1, "STRING"], [695, 183, 1, 87, 0, "IMAGE"], [698, 348, 0, 173, 2, "CONTROL_NET"]], "groups": [{"title": "1 CHARACTER GENERATION", "bounding": [-192, -597, 1894, 1241], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "2 UPSCALE + FACE FIX", "bounding": [1782, -602, 1323, 1832], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "3 SAVE POSES", "bounding": [3152, -603, 2443, 1388], "color": "#88A", "font_size": 24, "flags": {}}, {"title": "4 EMOTIONS", "bounding": [5691, -601, 2077, 1514], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8140274938686372, "offset": [562.9468676417416, 573.770660331743]}}, "version": 0.4}