# 🎮 AI-Powered 3D Character Generator

A comprehensive standalone application that integrates ComfyUI, Hugging Face, and AgenticSeek to automatically generate 3D models, characters, and assets from text prompts, images, or 3D files. Perfect for game development, 3D modeling, and creative projects.

## 🌟 Features

### 🎯 Core Capabilities
- **Text-to-3D Generation**: Create 3D characters from text descriptions
- **Image-to-3D Conversion**: Transform 2D images into 3D models
- **Face Matching**: Generate characters with exact facial features from uploaded photos
- **Real-time AI Processing**: Live workflow execution with progress monitoring
- **Multi-format Export**: GLB, FBX, OBJ formats for Unity, Unreal, Blender

### 🤖 AI Integrations
- **ComfyUI**: Advanced AI image generation and processing workflows
- **Hugging Face**: State-of-the-art AI models and transformers
- **AgenticSeek**: Intelligent AI assistance and optimization
- **Stable Diffusion**: High-quality image generation
- **MediaPipe**: Advanced face detection and analysis

### 🎨 Professional Features
- **Real-time 3D Viewer**: Interactive model preview with controls
- **Material System**: Automatic texture and material generation
- **Animation Support**: Basic rigging and animation capabilities
- **Game Engine Ready**: Direct export to Unity and Unreal Engine
- **Batch Processing**: Generate multiple characters simultaneously

## 🚀 Quick Start

### Prerequisites
- Windows 10/11 (64-bit)
- NVIDIA GPU with 6GB+ VRAM (recommended)
- Python 3.11 or 3.12
- 20GB+ free disk space

### One-Click Installation
```bash
# Clone the repository
git clone https://github.com/yourusername/ai-3d-character-generator.git
cd ai-3d-character-generator

# Run the automated installer
./install.bat

# Start the application
./start.bat
```

### Manual Installation
```bash
# Install Python dependencies
pip install -r requirements.txt

# Download AI models
python download_models.py

# Configure services
python setup_config.py

# Start the application
python comprehensive_backend.py
```

## 📖 Usage

### Web Interface
1. Open your browser to `http://localhost:8080`
2. Choose generation method:
   - **Text Prompt**: Describe your character
   - **Image Upload**: Upload a reference image
   - **Face Matching**: Upload a photo for exact face replication
3. Configure settings (style, quality, format)
4. Click "Generate Character"
5. Monitor real-time progress
6. Download your 3D model

### API Usage
```python
import requests

# Generate character from text
response = requests.post('http://localhost:8080/generate/character', json={
    'prompt': 'a mystical wizard with glowing staff',
    'style': 'fantasy',
    'quality': 'high',
    'format': 'glb'
})

# Get generation status
status = requests.get(f'http://localhost:8080/status/{response.json()["task_id"]}')
```

## 🛠️ Configuration

### Hugging Face Setup
1. Create account at [huggingface.co](https://huggingface.co)
2. Generate API token
3. Add to `config.json`:
```json
{
  "huggingface": {
    "api_token": "your_token_here"
  }
}
```

### ComfyUI Configuration
ComfyUI is included and pre-configured. Custom workflows can be added to `/workflows/` directory.

### GPU Optimization
For NVIDIA GPUs, CUDA acceleration is automatically enabled. For optimal performance:
- Use RTX 3060 or better
- Ensure 8GB+ VRAM for high-quality generation
- Update to latest NVIDIA drivers

## 📁 Project Structure

```
ai-3d-character-generator/
├── backend/                 # Core application logic
│   ├── comfyui_integration/ # ComfyUI workflow management
│   ├── huggingface_client/  # HF API integration
│   └── mesh_generator/      # 3D model generation
├── comfyui/                 # Embedded ComfyUI installation
├── models/                  # Pre-downloaded AI models
├── workflows/               # ComfyUI workflow templates
├── frontend/                # Web interface
├── outputs/                 # Generated 3D models
└── docs/                    # Documentation
```

## 🎯 Supported Formats

### Input
- **Text**: Natural language descriptions
- **Images**: JPG, PNG, WebP (up to 10MB)
- **3D Models**: OBJ, FBX, GLB for enhancement

### Output
- **GLB**: Optimized for web and mobile
- **FBX**: Industry standard for game engines
- **OBJ**: Universal 3D format
- **Unity Package**: Ready-to-import assets
- **Unreal Assets**: UE5-compatible files

## 🔧 Advanced Features

### Custom Workflows
Create custom ComfyUI workflows in `/workflows/custom/`:
```json
{
  "name": "My Custom Workflow",
  "description": "Custom character generation",
  "nodes": { ... }
}
```

### Batch Generation
```python
# Generate multiple characters
batch_request = {
    "prompts": [
        "warrior with sword",
        "mage with staff",
        "archer with bow"
    ],
    "batch_size": 3
}
```

### Face Matching Pipeline
1. Upload reference photo
2. AI extracts facial features
3. Generates 3D character with matching face
4. Applies style and enhancements
5. Exports game-ready model

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### Development Setup
```bash
# Clone for development
git clone https://github.com/yourusername/ai-3d-character-generator.git
cd ai-3d-character-generator

# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
python -m pytest tests/

# Start development server
python comprehensive_backend.py --dev
```

## 📄 License

This project is licensed under the MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **ComfyUI Team**: For the amazing workflow system
- **Hugging Face**: For democratizing AI
- **Stability AI**: For Stable Diffusion
- **Three.js**: For 3D web rendering
- **Open Source Community**: For countless contributions

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/yourusername/ai-3d-character-generator/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/ai-3d-character-generator/discussions)
- **Discord**: [Join our community](https://discord.gg/your-invite)

## 🔄 Updates

- **v1.0.0**: Initial release with ComfyUI integration
- **v1.1.0**: Added face matching capabilities
- **v1.2.0**: Enhanced game engine export
- **v1.3.0**: Batch processing and API improvements

---

**Made with ❤️ for the 3D and AI community**
