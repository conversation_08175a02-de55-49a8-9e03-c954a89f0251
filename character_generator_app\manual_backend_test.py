#!/usr/bin/env python3
"""
Manual backend test - run this to test character generation
"""

import sys
import os
import json
import uuid
from datetime import datetime

# Add backend to path
sys.path.append('backend')

def test_character_generation():
    """Test character generation manually"""
    print("🧪 Manual Character Generation Test")
    print("=" * 35)
    
    try:
        # Import character generation functions
        from app import create_character_glb, analyze_character_prompt
        print("✅ Character generation functions imported")
        
        # Test prompts
        test_prompts = [
            "A blue robot warrior with silver armor",
            "A mystical alien wizard with purple robes",
            "A fierce dragon knight with golden scales"
        ]
        
        results = []
        
        for i, prompt in enumerate(test_prompts):
            print(f"\n🎯 Test {i+1}: {prompt}")
            
            try:
                # Analyze character
                character_type = analyze_character_prompt(prompt)
                print(f"   Character type: {character_type['name']}")
                
                # Generate GLB
                glb_content = create_character_glb(prompt)
                
                # Save file
                job_id = str(uuid.uuid4())[:8]
                filename = f"manual_test_{job_id}_{character_type['name']}.glb"
                
                # Ensure directory exists
                os.makedirs('backend/outputs/models', exist_ok=True)
                filepath = os.path.join('backend', 'outputs', 'models', filename)
                
                with open(filepath, 'wb') as f:
                    f.write(glb_content)
                
                print(f"   ✅ Generated: {filename} ({len(glb_content)} bytes)")
                
                results.append({
                    'prompt': prompt,
                    'character_type': character_type['name'],
                    'filename': filename,
                    'file_size': len(glb_content),
                    'job_id': job_id
                })
                
            except Exception as e:
                print(f"   ❌ Failed: {e}")
        
        # Create API response file
        api_response = {
            'test_results': results,
            'timestamp': datetime.now().isoformat(),
            'total_generated': len(results),
            'status': 'success'
        }
        
        with open('manual_test_results.json', 'w') as f:
            json.dump(api_response, f, indent=2)
        
        print(f"\n📊 Test Summary:")
        print(f"   Characters generated: {len(results)}")
        print(f"   Results saved to: manual_test_results.json")
        print(f"   Files saved in: backend/outputs/models/")
        
        # Show how to use with web interface
        print(f"\n🌐 To use with web interface:")
        print(f"   1. Open connected_interface.html")
        print(f"   2. Click on the generated character files")
        print(f"   3. View them in the 3D viewer")
        
        return True
        
    except Exception as e:
        print(f"❌ Character generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_api_response():
    """Create a simple API response for testing"""
    print("\n📡 Creating API Response Simulation")
    print("=" * 35)
    
    # Simulate API responses
    api_responses = {
        'status': {
            'server': 'simulated',
            'character_generation': 'available',
            'timestamp': datetime.now().isoformat()
        },
        'test': {
            'test': 'success',
            'backend': 'working (simulated)',
            'timestamp': datetime.now().isoformat()
        }
    }
    
    # Save API responses
    os.makedirs('api_responses', exist_ok=True)
    
    for endpoint, response in api_responses.items():
        filename = f"api_responses/{endpoint}.json"
        with open(filename, 'w') as f:
            json.dump(response, f, indent=2)
        print(f"✅ Created: {filename}")
    
    print("📡 API response files created for testing")

def main():
    """Main test function"""
    print("🔧 Manual Backend Test & Setup")
    print("=" * 30)
    
    # Test character generation
    gen_success = test_character_generation()
    
    # Create API responses
    create_simple_api_response()
    
    # Summary
    print(f"\n📋 Manual Test Complete")
    print("=" * 25)
    print(f"Character generation: {'✅' if gen_success else '❌'}")
    print(f"API simulation: ✅")
    
    if gen_success:
        print(f"\n🎉 Success! Your character generation system is working!")
        print(f"📁 Check the backend/outputs/models/ directory for GLB files")
        print(f"🌐 Use connected_interface.html to view them")
    else:
        print(f"\n⚠️ Some issues detected. Check the error messages above.")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
