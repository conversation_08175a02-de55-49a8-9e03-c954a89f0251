#!/usr/bin/env python3
"""Test Hugging Face account and token creation"""

import requests

def test_without_token():
    """Test if we can access public endpoints without a token"""
    print("🧪 Testing public Hugging Face API access...")
    
    try:
        # Test public model info
        url = "https://huggingface.co/api/models/gpt2"
        response = requests.get(url, timeout=10)
        
        print(f"Public API Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Public API is accessible")
            return True
        else:
            print("❌ Public API not accessible")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing public API: {e}")
        return False

def test_token_format(token):
    """Test token format without making API calls"""
    print(f"\n🔍 Analyzing token format...")
    print(f"Token: {token}")
    print(f"Length: {len(token)}")
    print(f"Starts with 'hf_': {token.startswith('hf_')}")
    
    # Check for common issues
    issues = []
    
    if not token.startswith('hf_'):
        issues.append("Token should start with 'hf_'")
    
    if len(token) != 37:
        issues.append(f"Token should be 37 characters, got {len(token)}")
    
    if ' ' in token:
        issues.append("Token contains spaces")
    
    if '\n' in token or '\r' in token:
        issues.append("Token contains newlines")
    
    if issues:
        print("❌ Token format issues found:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ Token format looks correct")
        return True

def provide_instructions():
    """Provide step-by-step token creation instructions"""
    print("\n📋 Step-by-Step Token Creation:")
    print("=" * 50)
    print("1. Go to: https://huggingface.co/settings/tokens")
    print("2. If not logged in, create a FREE account first")
    print("3. Click 'New token'")
    print("4. Settings:")
    print("   - Name: 3D-Character-Generator")
    print("   - Type: Read (NOT Write or Fine-grained)")
    print("   - Expiration: No expiration")
    print("5. Click 'Generate a token'")
    print("6. IMMEDIATELY copy the token (you won't see it again)")
    print("7. Make sure to copy ONLY the token (no extra text)")
    print("\n💡 Common Issues:")
    print("- Don't copy the 'hf_' prefix twice")
    print("- Don't include any spaces or newlines")
    print("- Make sure the token type is 'Read'")
    print("- Some browsers add extra characters when copying")

if __name__ == "__main__":
    print("🔧 Hugging Face Token Troubleshooting")
    print("=" * 40)
    
    # Test public access
    public_works = test_without_token()
    
    # Test current token
    current_token = "*************************************"
    format_ok = test_token_format(current_token)
    
    if public_works and format_ok:
        print("\n🤔 Token format is correct and public API works.")
        print("This suggests the token itself is invalid or expired.")
        print("Please create a new token following the instructions below.")
    
    provide_instructions()
