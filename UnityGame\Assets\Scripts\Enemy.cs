using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;

public class Enemy : MonoBehaviour
{
    // Enemy parameters
    public float detectionRange = 10f;
    public float attackRange = 2f;
    public int damage = 10;
    public float attackCooldown = 2f;
    
    // Movement parameters
    public float patrolSpeed = 2f;
    public float chaseSpeed = 4f;
    
    // Patrol points
    public Transform[] patrolPoints;
    private int currentPatrolIndex = 0;
    
    // Component references
    private NavMeshAgent agent;
    private Animator animator;
    
    // Target reference
    private Transform player;
    
    // State variables
    private bool isAttacking = false;
    private float lastAttackTime = 0f;
    
    // Start is called before the first frame update
    void Start()
    {
        // Get component references
        agent = GetComponent<NavMeshAgent>();
        animator = GetComponent<Animator>();
        
        // Find the player
        player = GameObject.FindGameObjectWithTag("Player").transform;
        
        // Set initial destination
        if (patrolPoints.Length > 0)
        {
            agent.SetDestination(patrolPoints[0].position);
        }
    }

    // Update is called once per frame
    void Update()
    {
        // Calculate distance to player
        float distanceToPlayer = Vector3.Distance(transform.position, player.position);
        
        // Check if the player is within detection range
        if (distanceToPlayer <= detectionRange)
        {
            // Player detected, chase them
            ChasePlayer();
            
            // Check if the player is within attack range
            if (distanceToPlayer <= attackRange)
            {
                // Attack the player
                AttackPlayer();
            }
        }
        else
        {
            // Player not detected, continue patrolling
            Patrol();
        }
    }
    
    // Patrol between patrol points
    void Patrol()
    {
        // Set patrol speed
        agent.speed = patrolSpeed;
        
        // Check if we've reached the current patrol point
        if (agent.remainingDistance < 0.5f && !agent.pathPending)
        {
            // Move to the next patrol point
            currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
            agent.SetDestination(patrolPoints[currentPatrolIndex].position);
        }
        
        // Update animation
        if (animator != null)
        {
            animator.SetBool("IsChasing", false);
            animator.SetBool("IsAttacking", false);
        }
    }
    
    // Chase the player
    void ChasePlayer()
    {
        // Set chase speed
        agent.speed = chaseSpeed;
        
        // Set destination to player position
        agent.SetDestination(player.position);
        
        // Update animation
        if (animator != null)
        {
            animator.SetBool("IsChasing", true);
            animator.SetBool("IsAttacking", false);
        }
    }
    
    // Attack the player
    void AttackPlayer()
    {
        // Check if we can attack
        if (Time.time >= lastAttackTime + attackCooldown)
        {
            // Set attacking flag
            isAttacking = true;
            
            // Update last attack time
            lastAttackTime = Time.time;
            
            // Update animation
            if (animator != null)
            {
                animator.SetBool("IsAttacking", true);
            }
            
            // Deal damage to the player
            PlayerHealth playerHealth = player.GetComponent<PlayerHealth>();
            if (playerHealth != null)
            {
                playerHealth.TakeDamage(damage);
            }
            
            // Reset attacking flag after a delay
            StartCoroutine(ResetAttackFlag());
        }
    }
    
    // Reset the attacking flag after a delay
    IEnumerator ResetAttackFlag()
    {
        yield return new WaitForSeconds(1f);
        isAttacking = false;
        
        // Update animation
        if (animator != null)
        {
            animator.SetBool("IsAttacking", false);
        }
    }
    
    // Draw gizmos for visualization
    void OnDrawGizmosSelected()
    {
        // Draw detection range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);
        
        // Draw attack range
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
        
        // Draw patrol path
        if (patrolPoints.Length > 0)
        {
            Gizmos.color = Color.blue;
            for (int i = 0; i < patrolPoints.Length; i++)
            {
                if (patrolPoints[i] != null)
                {
                    Gizmos.DrawSphere(patrolPoints[i].position, 0.3f);
                    
                    // Draw lines between patrol points
                    if (i < patrolPoints.Length - 1 && patrolPoints[i + 1] != null)
                    {
                        Gizmos.DrawLine(patrolPoints[i].position, patrolPoints[i + 1].position);
                    }
                    else if (patrolPoints[0] != null)
                    {
                        Gizmos.DrawLine(patrolPoints[i].position, patrolPoints[0].position);
                    }
                }
            }
        }
    }
}
