{"1": {"inputs": {"text": "a detailed 3D character, high quality, realistic", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "2": {"inputs": {"text": "low quality, blurry, distorted", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative)"}}, "3": {"inputs": {"seed": 42, "steps": 20, "cfg": 7.0, "sampler_name": "euler", "scheduler": "normal", "denoise": 1.0, "model": ["4", 0], "positive": ["1", 0], "negative": ["2", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "sd_xl_base_1.0.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 512, "height": 512, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "7": {"inputs": {"filename_prefix": "character_output", "images": ["6", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "8": {"inputs": {"image": ["6", 0], "depth_estimation_method": "dpt_large", "remove_background": true}, "class_type": "DepthEstimation", "_meta": {"title": "Depth Estimation"}}, "9": {"inputs": {"image": ["6", 0], "depth": ["8", 0], "mesh_resolution": 256, "texture_resolution": 512}, "class_type": "ImageTo3D", "_meta": {"title": "Image to 3D"}}, "10": {"inputs": {"mesh": ["9", 0], "output_format": "glb", "filename_prefix": "character_3d"}, "class_type": "Save3DModel", "_meta": {"title": "Save 3D Model"}}}