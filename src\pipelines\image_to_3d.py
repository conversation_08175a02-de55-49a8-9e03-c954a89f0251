"""
Pipeline for converting images to 3D models.
"""

import os
import sys
import base64
import tempfile
import subprocess
import time

# Import integration modules
from integrations.huggingface.image_to_3d import HuggingFaceImageTo3D
from integrations.comfyui.api import ComfyUIAPI
from integrations.blender.automation import BlenderAutomation
from integrations.comfyui.mickmumpitz_workflows import MickmumpitzWorkflows

class ImageTo3DPipeline:
    """
    Pipeline for converting images to 3D models.

    This pipeline uses:
    1. HuggingFace models for initial 3D mesh generation
    2. ComfyUI for texture generation and refinement
    3. Blender for post-processing and optimization
    4. Mickmumpitz's workflows for enhanced 3D generation
    """

    def __init__(self):
        """Initialize the image to 3D pipeline."""
        self.huggingface = HuggingFaceImageTo3D()
        self.comfyui = ComfyUIAPI()
        self.blender = BlenderAutomation()
        self.mickmumpitz = MickmumpitzWorkflows()

        # Create output directory if it doesn't exist
        os.makedirs('output/3d_models', exist_ok=True)

    def process(self, input_data, parameters):
        """
        Process an image to generate a 3D model.

        Args:
            input_data: Base64 encoded image data
            parameters: Additional parameters for the generation
                - model: Model to use for 3D generation (default: 'shap-e')
                - texture_quality: Quality of textures (default: 'medium')
                - optimize_mesh: Whether to optimize the mesh (default: True)
                - use_mickmumpitz: Whether to use mickmumpitz's workflow (default: False)

        Returns:
            dict: Result containing paths to generated files
        """
        try:
            # Decode base64 image
            image_data = base64.b64decode(input_data.split(',')[1] if ',' in input_data else input_data)

            # Save image to temporary file
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_file.write(image_data)
                image_path = temp_file.name

            # Get parameters
            model = parameters.get('model', 'shap-e')
            texture_quality = parameters.get('texture_quality', 'medium')
            optimize_mesh = parameters.get('optimize_mesh', True)
            use_mickmumpitz = parameters.get('use_mickmumpitz', False)

            if use_mickmumpitz:
                # Use mickmumpitz's workflow for enhanced 3D generation
                return self._process_with_mickmumpitz(image_path, parameters)
            else:
                # Use standard pipeline
                return self._process_standard(image_path, model, texture_quality, optimize_mesh)

        except Exception as e:
            print(f"Error in image to 3D pipeline: {str(e)}")
            raise
        finally:
            # Clean up temporary files
            if 'image_path' in locals() and os.path.exists(image_path):
                os.unlink(image_path)

    def _process_standard(self, image_path, model, texture_quality, optimize_mesh):
        """
        Process an image using the standard pipeline.

        Args:
            image_path: Path to the input image
            model: Model to use for 3D generation
            texture_quality: Quality of textures
            optimize_mesh: Whether to optimize the mesh

        Returns:
            dict: Result containing paths to generated files
        """
        # Step 1: Generate initial 3D mesh using HuggingFace
        print(f"Generating 3D mesh using {model}...")
        mesh_path = self.huggingface.generate_mesh(
            image_path=image_path,
            model=model
        )

        # Step 2: Generate textures using ComfyUI
        print(f"Generating textures with quality {texture_quality}...")
        texture_paths = self.comfyui.generate_textures(
            mesh_path=mesh_path,
            reference_image_path=image_path,
            quality=texture_quality
        )

        # Step 3: Post-process in Blender
        print("Post-processing in Blender...")
        output_path = os.path.join('output/3d_models', f"{os.path.basename(mesh_path)}")
        final_model_path = self.blender.process_model(
            mesh_path=mesh_path,
            texture_paths=texture_paths,
            output_path=output_path,
            optimize=optimize_mesh
        )

        # Return results
        return {
            'model_path': final_model_path,
            'texture_paths': texture_paths,
            'preview_image': self.blender.render_preview(final_model_path)
        }

    def _process_with_mickmumpitz(self, image_path, parameters):
        """
        Process an image using mickmumpitz's workflow.

        This method uses mickmumpitz's ComfyUI workflow for enhanced 3D generation.

        Args:
            image_path: Path to the input image
            parameters: Additional parameters for the generation

        Returns:
            dict: Result containing paths to generated files
        """
        # Create a unique output directory
        timestamp = int(time.time())
        output_dir = os.path.join('output/3d_models', f"mickmumpitz_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)

        # Step 1: Generate a 3D model using Trellis or another image-to-3D model
        print("Generating initial 3D model...")

        # For now, we'll use HuggingFace's image-to-3D model as a placeholder
        # In a real implementation, you would use Trellis or another model recommended by mickmumpitz
        model = parameters.get('model', 'shap-e')
        mesh_path = self.huggingface.generate_mesh(
            image_path=image_path,
            model=model,
            output_dir=output_dir
        )

        # Step 2: Set up Blender for rendering with mickmumpitz's workflow
        print("Setting up Blender for rendering...")
        blender_output_dir = os.path.join(output_dir, 'blender_output')
        os.makedirs(blender_output_dir, exist_ok=True)

        # Run Blender setup script
        blender_script_path = os.path.join(os.path.dirname(__file__), '..', 'scripts', 'run_blender_setup.py')
        cmd = [
            sys.executable,
            blender_script_path,
            '--blender-file', '',  # We'll create a new scene
            '--output-dir', blender_output_dir,
            '--render-type', 'still',
            '--resolution-x', '512',
            '--resolution-y', '512'
        ]

        try:
            subprocess.run(cmd, check=True)
        except subprocess.CalledProcessError as e:
            print(f"Error setting up Blender: {e}")
            # Continue with the process, as we might still be able to use ComfyUI

        # Step 3: Use mickmumpitz's workflow to render the 3D model
        print("Rendering with mickmumpitz's workflow...")
        rendered_output_dir = self.mickmumpitz.render_3d_animation(
            blender_output_dir=blender_output_dir,
            output_dir=os.path.join(output_dir, 'rendered'),
            use_video=False
        )

        # Step 4: Find the rendered output
        rendered_files = []
        for root, _, files in os.walk(rendered_output_dir):
            for file in files:
                if file.endswith(('.png', '.jpg', '.jpeg')):
                    rendered_files.append(os.path.join(root, file))

        if not rendered_files:
            print("No rendered files found. Using standard preview.")
            preview_image = self.blender.render_preview(mesh_path)
        else:
            # Use the first rendered file as preview
            with open(rendered_files[0], 'rb') as f:
                preview_image = base64.b64encode(f.read()).decode('utf-8')

        # Return results
        return {
            'model_path': mesh_path,
            'rendered_files': rendered_files,
            'preview_image': preview_image,
            'workflow': 'mickmumpitz'
        }
