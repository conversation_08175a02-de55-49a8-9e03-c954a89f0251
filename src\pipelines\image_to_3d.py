"""
Pipeline for converting images to 3D models.
"""

import os
import base64
import tempfile
from PIL import Image
import io
import json

# Import integration modules
from integrations.huggingface.image_to_3d import HuggingFaceImageTo3D
from integrations.comfyui.api import ComfyUIAPI
from integrations.blender.automation import BlenderAutomation

class ImageTo3DPipeline:
    """
    Pipeline for converting images to 3D models.
    
    This pipeline uses:
    1. HuggingFace models for initial 3D mesh generation
    2. ComfyUI for texture generation and refinement
    3. Blender for post-processing and optimization
    """
    
    def __init__(self):
        """Initialize the image to 3D pipeline."""
        self.huggingface = HuggingFaceImageTo3D()
        self.comfyui = ComfyUIAPI()
        self.blender = BlenderAutomation()
        
        # Create output directory if it doesn't exist
        os.makedirs('output/3d_models', exist_ok=True)
    
    def process(self, input_data, parameters):
        """
        Process an image to generate a 3D model.
        
        Args:
            input_data: Base64 encoded image data
            parameters: Additional parameters for the generation
                - model: Model to use for 3D generation (default: 'shap-e')
                - texture_quality: Quality of textures (default: 'medium')
                - optimize_mesh: Whether to optimize the mesh (default: True)
                
        Returns:
            dict: Result containing paths to generated files
        """
        try:
            # Decode base64 image
            image_data = base64.b64decode(input_data.split(',')[1] if ',' in input_data else input_data)
            
            # Save image to temporary file
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_file.write(image_data)
                image_path = temp_file.name
            
            # Get parameters
            model = parameters.get('model', 'shap-e')
            texture_quality = parameters.get('texture_quality', 'medium')
            optimize_mesh = parameters.get('optimize_mesh', True)
            
            # Step 1: Generate initial 3D mesh using HuggingFace
            print(f"Generating 3D mesh using {model}...")
            mesh_path = self.huggingface.generate_mesh(
                image_path=image_path,
                model=model
            )
            
            # Step 2: Generate textures using ComfyUI
            print(f"Generating textures with quality {texture_quality}...")
            texture_paths = self.comfyui.generate_textures(
                mesh_path=mesh_path,
                reference_image_path=image_path,
                quality=texture_quality
            )
            
            # Step 3: Post-process in Blender
            print("Post-processing in Blender...")
            output_path = os.path.join('output/3d_models', f"{os.path.basename(mesh_path)}")
            final_model_path = self.blender.process_model(
                mesh_path=mesh_path,
                texture_paths=texture_paths,
                output_path=output_path,
                optimize=optimize_mesh
            )
            
            # Return results
            return {
                'model_path': final_model_path,
                'texture_paths': texture_paths,
                'preview_image': self.blender.render_preview(final_model_path)
            }
            
        except Exception as e:
            print(f"Error in image to 3D pipeline: {str(e)}")
            raise
        finally:
            # Clean up temporary files
            if 'image_path' in locals() and os.path.exists(image_path):
                os.unlink(image_path)
