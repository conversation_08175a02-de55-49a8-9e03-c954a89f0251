#!/usr/bin/env python3
"""
Start the web interface for 3D Character Generator
Handles all startup issues and provides a clean interface
"""

import sys
import os
import time
import webbrowser
import threading
from pathlib import Path

# Add backend to path
sys.path.append('backend')

def start_backend_server():
    """Start the Flask backend server"""
    print("🚀 Starting Backend Server...")
    
    try:
        # Import Flask components
        from flask import Flask, request, jsonify, send_file
        from flask_cors import CORS
        from flask_socketio import SocketIO
        
        # Import our character generation functions
        from app import (
            create_character_glb, 
            analyze_character_prompt,
            active_jobs
        )
        
        # Create Flask app
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'character-generator-2024'
        app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max
        
        CORS(app)
        socketio = SocketIO(app, cors_allowed_origins="*")
        
        print("✅ Flask app created successfully")
        
        # Routes
        @app.route('/')
        def index():
            return jsonify({
                'message': '3D Character Generator API',
                'status': 'running',
                'version': '2.0.0',
                'features': ['procedural_generation', 'ai_ready', 'web_interface']
            })
        
        @app.route('/api/test')
        def test():
            return jsonify({
                'test': 'success',
                'backend': 'working',
                'character_generation': 'available'
            })
        
        @app.route('/api/generate', methods=['POST'])
        def generate_character():
            try:
                data = request.get_json()
                text_prompt = data.get('prompt', 'A 3D character')
                
                print(f"🎯 Generating character: {text_prompt}")
                
                # Analyze character
                character_type = analyze_character_prompt(text_prompt)
                
                # Generate GLB
                glb_content = create_character_glb(text_prompt)
                
                # Save file
                import uuid
                job_id = str(uuid.uuid4())[:8]
                filename = f"character_{job_id}.glb"
                filepath = os.path.join('backend', 'outputs', 'models', filename)
                
                # Ensure directory exists
                os.makedirs(os.path.dirname(filepath), exist_ok=True)
                
                with open(filepath, 'wb') as f:
                    f.write(glb_content)
                
                print(f"✅ Character generated: {filename}")
                
                return jsonify({
                    'success': True,
                    'job_id': job_id,
                    'filename': filename,
                    'character_type': character_type['name'],
                    'file_size': len(glb_content),
                    'download_url': f'/api/download/{filename}'
                })
                
            except Exception as e:
                print(f"❌ Generation error: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @app.route('/api/download/<filename>')
        def download_file(filename):
            try:
                filepath = os.path.join('backend', 'outputs', 'models', filename)
                if os.path.exists(filepath):
                    return send_file(filepath, as_attachment=True)
                else:
                    return jsonify({'error': 'File not found'}), 404
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @app.route('/api/status')
        def status():
            return jsonify({
                'server': 'running',
                'character_generation': 'available',
                'ai_integration': 'ready',
                'active_jobs': len(active_jobs)
            })
        
        print("✅ Routes configured")
        print("🌐 Starting server on http://localhost:5000")
        
        # Start server
        socketio.run(app, 
                    host='0.0.0.0', 
                    port=5000, 
                    debug=False,
                    allow_unsafe_werkzeug=True)
        
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def open_web_interface():
    """Open the web interface in browser"""
    print("🌐 Opening Web Interface...")
    
    # Wait for server to start
    time.sleep(3)
    
    # Get the frontend path
    frontend_path = Path(__file__).parent / "simple_frontend.html"
    
    if frontend_path.exists():
        try:
            # Open in default browser
            webbrowser.open(f"file:///{frontend_path.absolute()}")
            print("✅ Web interface opened in browser")
            print(f"📱 Frontend: {frontend_path}")
            print("🔧 Backend: http://localhost:5000")
        except Exception as e:
            print(f"❌ Failed to open browser: {e}")
            print(f"📱 Manually open: {frontend_path}")
    else:
        print("❌ Frontend file not found")

def main():
    """Main startup function"""
    print("🎮 3D Character Generator - Web Interface Startup")
    print("=" * 50)
    
    # Check if backend directory exists
    if not os.path.exists('backend'):
        print("❌ Backend directory not found")
        input("Press Enter to exit...")
        return
    
    print("✅ Backend directory found")
    
    # Start web interface in separate thread
    web_thread = threading.Thread(target=open_web_interface)
    web_thread.daemon = True
    web_thread.start()
    
    print("🚀 Starting web interface...")
    print("\n💡 Instructions:")
    print("1. Backend server will start on http://localhost:5000")
    print("2. Frontend will open automatically in your browser")
    print("3. Use the web interface to generate characters")
    print("4. Press Ctrl+C to stop the server")
    
    # Start backend server (this will block)
    start_backend_server()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down web interface...")
        print("✅ Server stopped")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
