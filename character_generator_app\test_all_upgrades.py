#!/usr/bin/env python3
"""
Test all upgrades: Enhanced face processing, professional mesh generation, and ComfyUI integration
"""

import sys
import os
import uuid
from datetime import datetime

# Add backend to path
sys.path.append('backend')

def test_enhanced_face_processing():
    """Test enhanced face processing capabilities"""
    print("🔍 Testing Enhanced Face Processing")
    print("=" * 35)
    
    try:
        from enhanced_face_processor import EnhancedFaceProcessor
        
        processor = EnhancedFaceProcessor()
        print("✅ Enhanced face processor initialized")
        
        # Create test image if needed
        test_image_path = create_test_image()
        
        if test_image_path and os.path.exists(test_image_path):
            print(f"📸 Testing with image: {test_image_path}")
            
            # Test enhanced feature extraction
            features = processor.extract_enhanced_face_features(test_image_path)
            
            print("✅ Enhanced face features extracted")
            print(f"   Method: {features.get('method', 'unknown')}")
            print(f"   Face detected: {features.get('face_detected', False)}")
            print(f"   Skin tone: {features.get('skin_tone', {}).get('hex', 'N/A')}")
            print(f"   Face shape: {features.get('face_shape', {}).get('shape', 'unknown')}")
            print(f"   Symmetry: {features.get('facial_structure', {}).get('overall_symmetry', {}).get('symmetry_score', 0):.2f}")
            
            return True, features
        else:
            print("⚠️ No test image available")
            return False, None
            
    except Exception as e:
        print(f"❌ Enhanced face processing test failed: {e}")
        return False, None

def test_professional_mesh_generation():
    """Test professional mesh generation"""
    print("\n🎨 Testing Professional Mesh Generation")
    print("=" * 40)
    
    try:
        from professional_mesh_generator import ProfessionalMeshGenerator
        
        mesh_generator = ProfessionalMeshGenerator()
        print("✅ Professional mesh generator initialized")
        print(f"   Trimesh available: {mesh_generator.trimesh_available}")
        print(f"   PyMeshLab available: {mesh_generator.pymeshlab_available}")
        
        # Create test face features and character params
        test_face_features = {
            'face_detected': True,
            'face_shape': {'shape': 'oval', 'aspect_ratio': 0.8},
            'skin_tone': {'rgb': [200, 180, 160], 'hex': '#c8b4a0'},
            'facial_structure': {'overall_symmetry': {'symmetry_score': 0.85}}
        }
        
        test_character_params = {
            'name': 'test_character',
            'proportions': {
                'head_width': 1.0,
                'head_height': 1.0,
                'body_scale': 1.0,
                'limb_scale': 1.0
            },
            'colors': {
                'skin_primary': [0.8, 0.6, 0.4],
                'accent_color': [0.2, 0.4, 0.8]
            }
        }
        
        print("🔧 Generating professional mesh...")
        vertices, indices, normals = mesh_generator.generate_realistic_character_mesh(
            test_face_features, test_character_params
        )
        
        print("✅ Professional mesh generated")
        print(f"   Vertices: {len(vertices)//3:,}")
        print(f"   Faces: {len(indices)//3:,}")
        print(f"   Normals: {len(normals)//3:,}")
        
        return True, (vertices, indices, normals)
        
    except Exception as e:
        print(f"❌ Professional mesh generation test failed: {e}")
        return False, None

def test_comfyui_integration():
    """Test ComfyUI integration"""
    print("\n🎮 Testing ComfyUI Integration")
    print("=" * 30)
    
    try:
        from comfyui_integration import ComfyUIIntegration
        
        comfyui = ComfyUIIntegration()
        print("✅ ComfyUI integration initialized")
        print(f"   Server available: {comfyui.available}")
        print(f"   Server URL: {comfyui.server_url}")
        
        # Test workflow creation
        print("🔧 Creating mickmumpitz-style workflows...")
        workflow_created = comfyui.create_mickmumpitz_workflows()
        
        if workflow_created:
            print("✅ Workflows created successfully")
        
        # Test character sheet generation (will use fallback if server not available)
        test_image_path = create_test_image()
        if test_image_path:
            print("📋 Testing character sheet generation...")
            character_sheet_result = comfyui.generate_character_sheet(
                test_image_path, "A fantasy warrior character"
            )
            
            print(f"   Character sheet: {'✅ Success' if character_sheet_result.get('success') else '⚠️ Fallback'}")
            print(f"   Method: {character_sheet_result.get('method', 'unknown')}")
        
        # Test 3D rendering image generation
        print("🎯 Testing 3D rendering generation...")
        rendering_result = comfyui.generate_3d_rendering_image(
            "A realistic 3D character", "realistic"
        )
        
        print(f"   3D rendering: {'✅ Success' if rendering_result.get('success') else '⚠️ Fallback'}")
        print(f"   Method: {rendering_result.get('method', 'unknown')}")
        
        return True, comfyui.available
        
    except Exception as e:
        print(f"❌ ComfyUI integration test failed: {e}")
        return False, False

def test_complete_professional_pipeline():
    """Test the complete professional character generation pipeline"""
    print("\n🚀 Testing Complete Professional Pipeline")
    print("=" * 45)
    
    try:
        from app import create_professional_face_matched_character_glb
        
        # Create test image
        test_image_path = create_test_image()
        if not test_image_path:
            print("❌ Could not create test image")
            return False
        
        print("🎯 Running complete professional pipeline...")
        
        # Generate professional character
        glb_content, result_data = create_professional_face_matched_character_glb(
            test_image_path, 
            "A realistic fantasy character with detailed facial features"
        )
        
        if glb_content and result_data:
            # Save the professional character
            job_id = str(uuid.uuid4())[:8]
            filename = f"professional_character_{job_id}.glb"
            
            os.makedirs('backend/outputs/models', exist_ok=True)
            filepath = os.path.join('backend', 'outputs', 'models', filename)
            
            with open(filepath, 'wb') as f:
                f.write(glb_content)
            
            print("✅ Professional character generated successfully!")
            print(f"   File: {filename}")
            print(f"   Size: {len(glb_content):,} bytes")
            print(f"   Vertices: {result_data.get('vertex_count', 0):,}")
            print(f"   Faces: {result_data.get('face_count', 0):,}")
            print(f"   Face matched: {result_data.get('face_matched', False)}")
            print(f"   Mesh quality: {result_data.get('mesh_quality', 'unknown')}")
            print(f"   Processing method: {result_data.get('processing_method', 'unknown')}")
            print(f"   Enhanced image: {result_data.get('enhanced_image', False)}")
            print(f"   Character sheet: {result_data.get('character_sheet', False)}")
            print(f"   Reference image: {result_data.get('reference_image', False)}")
            
            return True, result_data
        else:
            print("❌ Professional character generation failed")
            return False, None
            
    except Exception as e:
        print(f"❌ Complete pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_enhanced_pipeline():
    """Test the enhanced character generation pipeline"""
    print("\n🎯 Testing Enhanced Pipeline (Fallback)")
    print("=" * 40)
    
    try:
        from app import create_enhanced_face_matched_character_glb
        
        test_image_path = create_test_image()
        if not test_image_path:
            print("❌ Could not create test image")
            return False
        
        print("🔧 Running enhanced pipeline...")
        
        glb_content, result_data = create_enhanced_face_matched_character_glb(
            test_image_path,
            "An enhanced character with better quality"
        )
        
        if glb_content and result_data:
            job_id = str(uuid.uuid4())[:8]
            filename = f"enhanced_character_{job_id}.glb"
            
            os.makedirs('backend/outputs/models', exist_ok=True)
            filepath = os.path.join('backend', 'outputs', 'models', filename)
            
            with open(filepath, 'wb') as f:
                f.write(glb_content)
            
            print("✅ Enhanced character generated!")
            print(f"   File: {filename}")
            print(f"   Size: {len(glb_content):,} bytes")
            print(f"   Quality: {result_data.get('mesh_quality', 'unknown')}")
            
            return True, result_data
        else:
            print("❌ Enhanced character generation failed")
            return False, None
            
    except Exception as e:
        print(f"❌ Enhanced pipeline test failed: {e}")
        return False, None

def create_test_image():
    """Create a test image for testing"""
    try:
        from PIL import Image, ImageDraw
        
        # Create a simple face-like image
        width, height = 300, 300
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # Draw a more detailed face
        # Head (circle)
        draw.ellipse([75, 75, 225, 225], fill='#f4c2a1', outline='#d4a574', width=2)
        
        # Eyes
        draw.ellipse([105, 120, 125, 140], fill='white', outline='black', width=2)
        draw.ellipse([175, 120, 195, 140], fill='white', outline='black', width=2)
        draw.ellipse([112, 127, 118, 133], fill='black')  # Left pupil
        draw.ellipse([182, 127, 188, 133], fill='black')  # Right pupil
        
        # Eyebrows
        draw.arc([100, 110, 130, 125], 0, 180, fill='#8B4513', width=3)
        draw.arc([170, 110, 200, 125], 0, 180, fill='#8B4513', width=3)
        
        # Nose
        draw.polygon([(150, 140), (145, 165), (155, 165)], fill='#e6b896', outline='#d4a574')
        
        # Mouth
        draw.arc([130, 175, 170, 195], 0, 180, fill='#d4a574', width=3)
        
        # Hair
        draw.arc([75, 75, 225, 150], 180, 360, fill='#8B4513', width=10)
        
        # Save test image
        test_image_path = 'enhanced_test_face.png'
        image.save(test_image_path)
        
        print(f"✅ Created enhanced test image: {test_image_path}")
        return test_image_path
        
    except ImportError:
        print("⚠️ PIL not available, using existing test image")
        # Check for existing test images
        for filename in ['test_face_sample.png', 'enhanced_test_face.png']:
            if os.path.exists(filename):
                return filename
        return None
    except Exception as e:
        print(f"⚠️ Could not create test image: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 COMPREHENSIVE UPGRADE TEST SUITE")
    print("=" * 40)
    print("Testing all enhancements: Face processing, Mesh generation, ComfyUI integration")
    print()
    
    results = {}
    
    # Test 1: Enhanced Face Processing
    face_success, face_features = test_enhanced_face_processing()
    results['enhanced_face_processing'] = face_success
    
    # Test 2: Professional Mesh Generation
    mesh_success, mesh_data = test_professional_mesh_generation()
    results['professional_mesh_generation'] = mesh_success
    
    # Test 3: ComfyUI Integration
    comfyui_success, comfyui_available = test_comfyui_integration()
    results['comfyui_integration'] = comfyui_success
    
    # Test 4: Complete Professional Pipeline
    professional_success, professional_data = test_complete_professional_pipeline()
    results['professional_pipeline'] = professional_success
    
    # Test 5: Enhanced Pipeline (Fallback)
    enhanced_success, enhanced_data = test_enhanced_pipeline()
    results['enhanced_pipeline'] = enhanced_success
    
    # Summary
    print(f"\n📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 35)
    print(f"Enhanced Face Processing: {'✅' if results['enhanced_face_processing'] else '❌'}")
    print(f"Professional Mesh Generation: {'✅' if results['professional_mesh_generation'] else '❌'}")
    print(f"ComfyUI Integration: {'✅' if results['comfyui_integration'] else '❌'}")
    print(f"Professional Pipeline: {'✅' if results['professional_pipeline'] else '❌'}")
    print(f"Enhanced Pipeline: {'✅' if results['enhanced_pipeline'] else '❌'}")
    
    success_count = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 Overall Success Rate: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count >= 3:
        print(f"\n🎉 UPGRADE IMPLEMENTATION SUCCESSFUL!")
        print(f"✅ Your character generator now has professional-grade capabilities!")
        print(f"📁 Check backend/outputs/models/ for generated characters")
        print(f"🌐 Use the enhanced web interfaces for testing")
        
        if comfyui_available:
            print(f"🎨 ComfyUI integration is active - mickmumpitz workflows available!")
        else:
            print(f"⚠️ ComfyUI not running - using enhanced fallback processing")
            
    else:
        print(f"\n⚠️ Some upgrades need attention:")
        for test_name, success in results.items():
            if not success:
                print(f"   - {test_name.replace('_', ' ').title()}")
    
    print(f"\n💡 Next Steps:")
    print(f"1. Test with real face images")
    print(f"2. Start ComfyUI server for full workflow integration")
    print(f"3. Use face_matching_interface.html for web testing")
    print(f"4. Generate character libraries for your projects")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
