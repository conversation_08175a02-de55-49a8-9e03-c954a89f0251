# 3D Character Generator App

A full-stack web application that allows users to upload images and text prompts to generate high-quality 3D characters for video games. The app integrates ComfyUI workflows, Blender, and other 3D tools to create game-ready characters with animations.

## Features

- **Image & Text Input**: Upload reference images and provide text descriptions
- **AI-Powered Generation**: Uses ComfyUI workflows and mickmumpitz's advanced workflows
- **Real-time Progress**: Live updates on character generation progress
- **3D Viewer**: Interactive 3D preview with animations and controls
- **Multiple Formats**: Download GLB models, textures, and animations
- **Job Management**: Track and manage all your character generation jobs
- **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

### Backend
- **Flask**: Python web framework
- **Flask-SocketIO**: Real-time communication
- **OpenCV**: Image processing
- **Pillow**: Image manipulation
- **NumPy**: Numerical operations

### Frontend
- **React**: User interface framework
- **Three.js**: 3D rendering and visualization
- **React Three Fiber**: React renderer for Three.js
- **Styled Components**: CSS-in-JS styling
- **Framer Motion**: Animations and transitions
- **Socket.IO**: Real-time updates
- **Axios**: HTTP client

## Project Structure

```
character_generator_app/
├── backend/
│   ├── app.py                 # Main Flask application
│   ├── requirements.txt       # Python dependencies
│   ├── uploads/              # Uploaded images
│   └── outputs/              # Generated 3D files
│       ├── models/           # GLB/FBX models
│       ├── textures/         # PNG/JPG textures
│       └── animations/       # Animation files
├── frontend/
│   ├── public/
│   │   └── index.html        # HTML template
│   ├── src/
│   │   ├── components/       # React components
│   │   │   ├── Header.js
│   │   │   ├── CharacterGenerator.js
│   │   │   ├── CharacterViewer.js
│   │   │   └── JobsList.js
│   │   ├── App.js           # Main React app
│   │   ├── index.js         # React entry point
│   │   └── index.css        # Global styles
│   └── package.json         # Node.js dependencies
└── README.md
```

## Installation & Setup

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd character_generator_app/backend
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run the Flask server:
   ```bash
   python app.py
   ```

The backend will be available at `http://localhost:5000`

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd character_generator_app/frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

The frontend will be available at `http://localhost:3000`

## Usage

### 1. Generate a Character

1. **Upload an Image** (optional): Drag and drop or click to upload a reference image
2. **Enter Description**: Provide a text description of your character
3. **Select Style**: Choose from realistic, cartoon, anime, or low-poly styles
4. **Generate**: Click "Generate 3D Character" to start the process

### 2. Monitor Progress

- Real-time progress updates via WebSocket connection
- Status indicators: Queued → Generating Workflow → Processing 3D → Completed
- Progress bar showing completion percentage

### 3. View & Interact

- **3D Viewer**: Interactive 3D preview with orbit controls
- **Animations**: Switch between idle, rotate, bounce, and static modes
- **Controls**: Adjust position, rotation, and scale
- **Lighting**: Professional studio lighting setup

### 4. Download Files

- **3D Model**: GLB format for Unity, Unreal Engine, Blender
- **Texture**: PNG texture files
- **Animation**: FBX animation files

### 5. Manage Jobs

- View all your character generation jobs
- Filter by status (completed, in progress, failed)
- Quick download and preview options

## API Endpoints

### Upload Image
```
POST /api/upload
Content-Type: multipart/form-data
Body: file (image file)
```

### Generate Character
```
POST /api/generate
Content-Type: application/json
Body: {
  "text_prompt": "A brave knight with silver armor",
  "image_path": "/path/to/uploaded/image.jpg",
  "style_options": {
    "realistic": false,
    "cartoon": true,
    "anime": false,
    "lowPoly": false
  }
}
```

### Get Job Status
```
GET /api/status/<job_id>
```

### Download Files
```
GET /api/download/<job_id>/<file_type>
file_type: model | texture | animation
```

### List All Jobs
```
GET /api/jobs
```

## WebSocket Events

### Client → Server
- `join_job`: Join a job room for updates

### Server → Client
- `connected`: Connection established
- `job_update`: Real-time job progress updates

## Integration with Existing Tools

This app integrates with the existing project tools:

- **ComfyUI Workflows**: Uses the workflow generator from `src/workflow_generators/`
- **Mickmumpitz Workflows**: Leverages advanced character generation workflows
- **Blender Integration**: Can export to Blender for further editing
- **Unity/Unreal**: Generated GLB files work directly in game engines

## Customization

### Adding New Styles
Edit the style options in `CharacterGenerator.js`:
```javascript
const [styleOptions, setStyleOptions] = useState({
  realistic: false,
  cartoon: true,
  anime: false,
  lowPoly: false,
  // Add new styles here
  cyberpunk: false,
  fantasy: false
});
```

### Custom 3D Models
Replace the simple character in `CharacterViewer.js` with actual GLB model loading:
```javascript
import { useGLTF } from '@react-three/drei';

const Character3D = ({ modelPath }) => {
  const { scene } = useGLTF(modelPath);
  return <primitive object={scene} />;
};
```

### Workflow Customization
Modify the workflow generation in `app.py`:
```python
def generate_character_workflow(job_id, image_path, text_prompt, style_options):
    # Customize workflow based on style_options
    workflow = workflow_generator.create_character_generation_workflow(
        use_mickmumpitz=True,
        style=style_options
    )
    # Add custom nodes or modifications
    return workflow
```

## Deployment

### Production Setup

1. **Backend**: Deploy Flask app with Gunicorn + Nginx
2. **Frontend**: Build and serve static files
3. **Database**: Add PostgreSQL for persistent job storage
4. **File Storage**: Use AWS S3 or similar for file storage
5. **Queue System**: Add Redis + Celery for background processing

### Environment Variables
```bash
# Backend
FLASK_ENV=production
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://...
REDIS_URL=redis://...

# Frontend
REACT_APP_API_URL=https://your-api-domain.com
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Acknowledgments

- [ComfyUI](https://github.com/comfyanonymous/ComfyUI) for the workflow system
- [mickmumpitz](https://www.patreon.com/Mickmumpitz) for the advanced workflows
- [Three.js](https://threejs.org/) for 3D rendering
- [React Three Fiber](https://github.com/pmndrs/react-three-fiber) for React integration
