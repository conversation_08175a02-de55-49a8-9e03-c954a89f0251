
import unreal
import sys
import os
import json

# Get arguments
args = sys.argv
if len(args) > 1:
    params_json = args[1]
    params = json.loads(params_json)
else:
    params = {}

level_name = params.get('level_name', 'NewLevel')
template = params.get('template', 'Default')
save_path = params.get('save_path', '/Game/Maps')

# Create a new level
level_path = f"{save_path}/{level_name}"
level_factory = unreal.LevelFactory()

# Create the level based on the template
if template == 'Default':
    new_level = level_factory.create_new(level_path)
elif template == 'Empty':
    new_level = level_factory.create_new(level_path, unreal.LevelStreamingAlwaysLoaded)
else:
    # Try to use the template as a path to an existing level
    template_path = template
    if unreal.EditorAssetLibrary.does_asset_exist(template_path):
        new_level = unreal.EditorAssetLibrary.duplicate_asset(template_path, level_path)
    else:
        new_level = level_factory.create_new(level_path)

# Save the new level
unreal.EditorAssetLibrary.save_asset(level_path)

print(f"Level created: {level_path}")
