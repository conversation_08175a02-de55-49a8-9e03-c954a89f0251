{"last_node_id": 373, "last_link_id": 797, "nodes": [{"id": 109, "type": "ADE_ApplyAnimateDiffModel", "pos": {"0": 4979.10302734375, "1": 2314.7001953125}, "size": {"0": 319.20001220703125, "1": 202}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "motion_model", "type": "MOTION_MODEL_ADE", "link": 176}, {"name": "motion_lora", "type": "MOTION_LORA", "link": null, "shape": 7}, {"name": "scale_multival", "type": "MULTIVAL", "link": null, "shape": 7}, {"name": "effect_multival", "type": "MULTIVAL", "link": null, "shape": 7}, {"name": "ad_keyframes", "type": "AD_KEYFRAMES", "link": null, "shape": 7}, {"name": "prev_m_models", "type": "M_MODELS", "link": null, "shape": 7}, {"name": "per_block", "type": "PER_BLOCK", "link": null, "shape": 7}], "outputs": [{"name": "M_MODELS", "type": "M_MODELS", "links": [177], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ADE_ApplyAnimateDiffModel"}, "widgets_values": [0, 1, ""], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 76, "type": "ControlNetLoaderAdvanced", "pos": {"0": 5930, "1": 1610}, "size": {"0": 367.79998779296875, "1": 58}, "flags": {}, "order": 0, "mode": 4, "inputs": [{"name": "tk_optional", "type": "TIMESTEP_KEYFRAME", "link": null, "shape": 7}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [113], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ControlNetLoaderAdvanced"}, "widgets_values": ["control_v11p_sd15_normalbae.pth"], "color": "#223", "bgcolor": "#335"}, {"id": 171, "type": "MaskPreview+", "pos": {"0": 3040, "1": 1050}, "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 123, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 288}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 113, "type": "ControlNetLoaderAdvanced", "pos": {"0": 6420, "1": 1610}, "size": {"0": 367.79998779296875, "1": 58}, "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "tk_optional", "type": "TIMESTEP_KEYFRAME", "link": null, "shape": 7}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [432], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ControlNetLoaderAdvanced"}, "widgets_values": ["control_v11p_sd15_canny.pth"], "color": "#223", "bgcolor": "#335"}, {"id": 108, "type": "ADE_LoopedUniformContextOptions", "pos": {"0": 4582.10302734375, "1": 2216.7001953125}, "size": {"0": 317.4000244140625, "1": 246}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "prev_context", "type": "CONTEXT_OPTIONS", "link": null, "shape": 7}, {"name": "view_opts", "type": "VIEW_OPTS", "link": null, "shape": 7}], "outputs": [{"name": "CONTEXT_OPTS", "type": "CONTEXT_OPTIONS", "links": [178], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ADE_LoopedUniformContextOptions"}, "widgets_values": [16, 1, 4, true, "pyramid", false, 0, 1], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 5, "type": "MaskPreview+", "pos": {"0": 3060, "1": -570}, "size": {"0": 216.18955993652344, "1": 246}, "flags": {}, "order": 112, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 6}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 228, "type": "ImpactImageBatchToImageList", "pos": {"0": 2269.513427734375, "1": -1110}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 74, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 439}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [435], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 227, "type": "ImageScale", "pos": {"0": 1909.7103271484375, "1": -1050}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 55, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 438}, {"name": "width", "type": "INT", "link": 443, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 442, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [439], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 238, "type": "ImpactImageBatchToImageList", "pos": {"0": 2269.513427734375, "1": -760}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 75, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 472}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [469], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 236, "type": "MaskFromRGBCMYBW+", "pos": {"0": 2269.513427734375, "1": -850}, "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 93, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 470}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [473], "slot_index": 6, "shape": 3}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05], "color": "#233", "bgcolor": "#355"}, {"id": 235, "type": "BlendModes", "pos": {"0": 2269.513427734375, "1": -800}, "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 85, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 469}, {"name": "source", "type": "IMAGE", "link": 474}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [470], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 237, "type": "ImageScale", "pos": {"0": 1909.7103271484375, "1": -700}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 56, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 471}, {"name": "width", "type": "INT", "link": 475, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 476, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [472], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 243, "type": "ImpactImageBatchToImageList", "pos": {"0": 2269.513427734375, "1": -450}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 76, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 483}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [480], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 241, "type": "MaskFromRGBCMYBW+", "pos": {"0": 2269.513427734375, "1": -540}, "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 94, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 481}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [484], "slot_index": 6, "shape": 3}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05], "color": "#233", "bgcolor": "#355"}, {"id": 242, "type": "ImageScale", "pos": {"0": 1909.7103271484375, "1": -390}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 57, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 482}, {"name": "width", "type": "INT", "link": 487, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 488, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [483], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 240, "type": "BlendModes", "pos": {"0": 2269.513427734375, "1": -500}, "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 86, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 480}, {"name": "source", "type": "IMAGE", "link": 489}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [481], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 245, "type": "BlendModes", "pos": {"0": 2269.513427734375, "1": -190}, "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 87, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 490}, {"name": "source", "type": "IMAGE", "link": 499}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [491], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 247, "type": "ImageScale", "pos": {"0": 1909.7103271484375, "1": -80}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 58, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 492}, {"name": "width", "type": "INT", "link": 497, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 498, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [493], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 225, "type": "BlendModes", "pos": {"0": 2269.513427734375, "1": -1160}, "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 84, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 435}, {"name": "source", "type": "IMAGE", "link": 436}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [437], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 226, "type": "MaskFromRGBCMYBW+", "pos": {"0": 2269.710205078125, "1": -1250}, "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 92, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 437}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [440], "slot_index": 6, "shape": 3}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05], "color": "#233", "bgcolor": "#355"}, {"id": 253, "type": "ImpactImageBatchToImageList", "pos": {"0": 2259.513427734375, "1": 190}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 78, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 503}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [500], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 251, "type": "MaskFromRGBCMYBW+", "pos": {"0": 2259.513427734375, "1": 100}, "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 96, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 501}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [504], "slot_index": 6, "shape": 3}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05], "color": "#233", "bgcolor": "#355"}, {"id": 250, "type": "BlendModes", "pos": {"0": 2259.513427734375, "1": 140}, "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 88, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 500}, {"name": "source", "type": "IMAGE", "link": 509}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [501], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 252, "type": "ImageScale", "pos": {"0": 1909.7103271484375, "1": 250}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 59, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 502}, {"name": "width", "type": "INT", "link": 507, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 508, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [503], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 258, "type": "ImpactImageBatchToImageList", "pos": {"0": 2259.513427734375, "1": 530}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 79, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 513}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [510], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 256, "type": "MaskFromRGBCMYBW+", "pos": {"0": 2259.513427734375, "1": 440}, "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 97, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 511}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [514], "slot_index": 6, "shape": 3}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05], "color": "#233", "bgcolor": "#355"}, {"id": 255, "type": "BlendModes", "pos": {"0": 2259.513427734375, "1": 480}, "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 89, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 510}, {"name": "source", "type": "IMAGE", "link": 519}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [511], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 257, "type": "ImageScale", "pos": {"0": 1909.7103271484375, "1": 590}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 60, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 512}, {"name": "width", "type": "INT", "link": 515, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 518, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [513], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 260, "type": "BlendModes", "pos": {"0": 2259.513427734375, "1": 800}, "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 90, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 520}, {"name": "source", "type": "IMAGE", "link": 553}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [521], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 273, "type": "ImpactImageBatchToImageList", "pos": {"0": 2239.513427734375, "1": 1170}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 81, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 558}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [555], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 271, "type": "MaskFromRGBCMYBW+", "pos": {"0": 2239.513427734375, "1": 1080}, "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 99, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 556}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [559], "slot_index": 6, "shape": 3}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05], "color": "#233", "bgcolor": "#355"}, {"id": 270, "type": "BlendModes", "pos": {"0": 2239.513427734375, "1": 1120}, "size": {"0": 315, "1": 170}, "flags": {"collapsed": true}, "order": 91, "mode": 0, "inputs": [{"name": "backdrop", "type": "IMAGE", "link": 555}, {"name": "source", "type": "IMAGE", "link": 560}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [556], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BlendModes"}, "widgets_values": ["difference", 1, "stretch", "yes"], "color": "#233", "bgcolor": "#355"}, {"id": 173, "type": "ImpactGaussianBlurMask", "pos": {"0": 2510, "1": 1050}, "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 107, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 559}], "outputs": [{"name": "MASK", "type": "MASK", "links": [288, 549, 578], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 165, "type": "ImpactGaussianBlurMask", "pos": {"0": 2510, "1": 730}, "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 106, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 524}], "outputs": [{"name": "MASK", "type": "MASK", "links": [282, 580], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 54, "type": "ImpactGaussianBlurMask", "pos": {"0": 2509.710205078125, "1": -880}, "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 101, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 473}], "outputs": [{"name": "MASK", "type": "MASK", "links": [95, 543, 590], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 283, "type": "MaskListToMaskBatch", "pos": {"0": 2850, "1": 120}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 117, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 584}], "outputs": [{"name": "MASK", "type": "MASK", "links": [], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 15, "type": "ImageScale", "pos": {"0": 1133.58056640625, "1": 625.64599609375}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 30}, {"name": "width", "type": "INT", "link": 459, "slot_index": 1, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 460, "slot_index": 2, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [441, 605], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 768, 432, "disabled"], "color": "#232", "bgcolor": "#353"}, {"id": 246, "type": "MaskFromRGBCMYBW+", "pos": {"0": 2269.710205078125, "1": -230}, "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 95, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 491}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [494], "slot_index": 6, "shape": 3}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.03, 0.03, 0.03], "color": "#233", "bgcolor": "#355"}, {"id": 248, "type": "ImpactImageBatchToImageList", "pos": {"0": 2269.710205078125, "1": -140}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": false}, "order": 77, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 493}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [490], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 263, "type": "ImpactImageBatchToImageList", "pos": {"0": 2259.710205078125, "1": 850}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 80, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 523}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [520], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 53, "type": "ImpactGaussianBlurMask", "pos": {"0": 2509.710205078125, "1": -1250}, "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 100, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 440}], "outputs": [{"name": "MASK", "type": "MASK", "links": [5, 592], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 56, "type": "ImpactGaussianBlurMask", "pos": {"0": 2509.710205078125, "1": -230}, "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 103, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 494}], "outputs": [{"name": "MASK", "type": "MASK", "links": [7, 545, 586], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 64, "type": "MaskPreview+", "pos": {"0": 3050, "1": -890}, "size": {"0": 219.1777801513672, "1": 246}, "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 95}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 145, "type": "MaskPreview+", "pos": {"0": 3040, "1": 80}, "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 116, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 254}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 249, "type": "SolidColorRGB", "pos": {"0": 1909.7103271484375, "1": 60}, "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 506, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 505, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [502], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#0073f4"], "color": "#233", "bgcolor": "#355"}, {"id": 209, "type": "Reroute", "pos": {"0": 1760, "1": 380}, "size": [75, 26], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 451}], "outputs": [{"name": "", "type": "INT", "links": [442, 444, 476, 478, 486, 488, 496, 498, 506, 508, 517, 518, 552, 554, 563, 564], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 208, "type": "Reroute", "pos": {"0": 1760, "1": 280}, "size": [75, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 450}], "outputs": [{"name": "", "type": "INT", "links": [443, 446, 475, 479, 485, 487, 495, 497, 505, 507, 515, 516, 527, 528, 561, 562], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 92, "type": "Note", "pos": {"0": 363.73712158203125, "1": 945.5263671875}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["<PERSON>ad De<PERSON>h here -->"], "color": "#432", "bgcolor": "#653"}, {"id": 77, "type": "ACN_AdvancedControlNetApply", "pos": {"0": 5940, "1": 1730}, "size": {"0": 355.20001220703125, "1": 286}, "flags": {}, "order": 138, "mode": 4, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 114}, {"name": "negative", "type": "CONDITIONING", "link": 115}, {"name": "control_net", "type": "CONTROL_NET", "link": 113}, {"name": "image", "type": "IMAGE", "link": 727}, {"name": "mask_optional", "type": "MASK", "link": null, "shape": 7}, {"name": "timestep_kf", "type": "TIMESTEP_KEYFRAME", "link": null, "shape": 7}, {"name": "latent_kf_override", "type": "LATENT_KEYFRAME", "link": null, "shape": 7}, {"name": "weights_override", "type": "CONTROL_NET_WEIGHTS", "link": null, "shape": 7}, {"name": "model_optional", "type": "MODEL", "link": null, "shape": 7}, {"name": "vae_optional", "type": "VAE", "link": null, "shape": 7}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [186], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [187], "slot_index": 1, "shape": 3}, {"name": "model_opt", "type": "MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ACN_AdvancedControlNetApply"}, "widgets_values": [0.3, 0, 1, ""], "color": "#223", "bgcolor": "#335"}, {"id": 93, "type": "Note", "pos": {"0": 363.58056640625, "1": 1285.64599609375}, "size": {"0": 210, "1": 75.61187744140625}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["if you have an additional Lineart / Freestyle Pass, activate these nodes and the 2nd ControlNet and load the images here -->"], "color": "#432", "bgcolor": "#653"}, {"id": 118, "type": "Note", "pos": {"0": 343.58056640625, "1": 1605.64599609375}, "size": {"0": 210, "1": 75.61187744140625}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["if you have an additional Normal Pass, activate these nodes and the 2nd ControlNet and load the images here -->"], "color": "#432", "bgcolor": "#653"}, {"id": 61, "type": "ControlNetLoaderAdvanced", "pos": {"0": 5510, "1": 1620}, "size": {"0": 367.79998779296875, "1": 58}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "tk_optional", "type": "TIMESTEP_KEYFRAME", "link": null, "shape": 7}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [92], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ControlNetLoaderAdvanced"}, "widgets_values": ["control_v11f1p_sd15_depth.pth"], "color": "#223", "bgcolor": "#335"}, {"id": 320, "type": "GetNode", "pos": {"0": 5515, "1": 1506}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [689], "slot_index": 0}], "title": "Get_DEPTH", "properties": {}, "widgets_values": ["DEPTH"], "color": "#223", "bgcolor": "#335"}, {"id": 340, "type": "SetNode", "pos": {"0": 1123.23828125, "1": 1988.42333984375}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "VAE", "type": "VAE", "link": 719}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_VAE", "properties": {"previousName": "VAE"}, "widgets_values": ["VAE"], "color": "#323", "bgcolor": "#535"}, {"id": 341, "type": "GetNode", "pos": {"0": 7710, "1": 1610}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [706], "slot_index": 0}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#323", "bgcolor": "#535"}, {"id": 319, "type": "SetNode", "pos": {"0": 1094, "1": 1006}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 715}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_DEPTH", "properties": {"previousName": "DEPTH"}, "widgets_values": ["DEPTH"], "color": "#223", "bgcolor": "#335"}, {"id": 47, "type": "Reroute", "pos": {"0": 5272, "1": 1749}, "size": [75, 26], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 797}], "outputs": [{"name": "", "type": "CONDITIONING", "links": [91], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 46, "type": "Reroute", "pos": {"0": 5272, "1": 1728}, "size": [75, 26], "flags": {}, "order": 136, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 796}], "outputs": [{"name": "", "type": "CONDITIONING", "links": [90]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 229, "type": "ImpactImageBatchToImageList", "pos": {"0": 1371, "1": 552}, "size": {"0": 210, "1": 26}, "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 441}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [436, 474, 489, 499, 509, 519, 553, 560], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": [], "color": "#232", "bgcolor": "#353"}, {"id": 117, "type": "ImageScale", "pos": {"0": 1134, "1": 1606}, "size": {"0": 210, "1": 122}, "flags": {"collapsed": true}, "order": 32, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 191}, {"name": "width", "type": "INT", "link": 465, "slot_index": 1, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 466, "slot_index": 2, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [190, 713], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 768, 432, "disabled"], "color": "#432", "bgcolor": "#653"}, {"id": 79, "type": "ImageScale", "pos": {"0": 1124, "1": 1286}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 119}, {"name": "width", "type": "INT", "link": 572, "slot_index": 1, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 573, "slot_index": 2, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [125, 714], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 768, 432, "disabled"], "color": "#223", "bgcolor": "#335"}, {"id": 59, "type": "ImageScale", "pos": {"0": 1124, "1": 946}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 31, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 716}, {"name": "width", "type": "INT", "link": 570, "slot_index": 1, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 571, "slot_index": 2, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [610, 715], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 768, 432, "disabled"], "color": "#223", "bgcolor": "#335"}, {"id": 4, "type": "MaskPreview+", "pos": {"0": 3050, "1": -1260}, "size": {"0": 219.1777801513672, "1": 246}, "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 5}], "outputs": [], "title": "🔧 Mask 1", "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 1, "type": "VAEDecode", "pos": {"0": 7710, "1": 1710}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 141, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 332}, {"name": "vae", "type": "VAE", "link": 706}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [647], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 194, "type": "VHS_VideoCombine", "pos": {"0": 8548, "1": 1201}, "size": [900, 310], "flags": {}, "order": 143, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 648}, {"name": "audio", "type": "AUDIO", "link": null, "shape": 7}, {"name": "meta_batch", "type": "VHS_BatchManager", "link": null, "shape": 7}, {"name": "vae", "type": "VAE", "link": null, "shape": 7}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "preview", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 4, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "preview_00236.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4"}}}}, {"id": 339, "type": "GetNode", "pos": {"0": 6880, "1": 1833}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [703], "slot_index": 0}], "title": "Get_LATENT", "properties": {}, "widgets_values": ["LATENT"], "color": "#323", "bgcolor": "#535"}, {"id": 115, "type": "VHS_LoadImagesPath", "pos": {"0": 653.58056640625, "1": 1605.64599609375}, "size": {"0": 300, "1": 142}, "flags": {"collapsed": false}, "order": 22, "mode": 0, "inputs": [{"name": "image_load_cap", "type": "INT", "link": 457, "slot_index": 0, "widget": {"name": "image_load_cap"}}, {"name": "skip_first_images", "type": "INT", "link": 635, "widget": {"name": "skip_first_images"}}, {"name": "select_every_nth", "type": "INT", "link": 652, "widget": {"name": "select_every_nth"}}, {"name": "meta_batch", "type": "VHS_BatchManager", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [191], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}, {"name": "frame_count", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_LoadImagesPath"}, "widgets_values": {"directory": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\normal_v01", "image_load_cap": 10, "skip_first_images": 0, "select_every_nth": 1, "choose folder to upload": "image", "videopreview": {"hidden": true, "paused": false, "params": {"frame_load_cap": 10, "skip_first_images": 0, "filename": "E:\\05_Mickmumpitz\\240226_AI_RENDERING\\city\\city_passes_v01\\normal_v01", "type": "path", "format": "folder", "select_every_nth": 1, "skip_first_frames": 0}}}, "color": "#432", "bgcolor": "#653"}, {"id": 323, "type": "SetNode", "pos": {"0": 1095.737060546875, "1": 1661.5263671875}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 713}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_NORMAL", "properties": {"previousName": "NORMAL"}, "widgets_values": ["NORMAL"], "color": "#432", "bgcolor": "#653"}, {"id": 322, "type": "SetNode", "pos": {"0": 1084, "1": 1340}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "IMAGE", "type": "IMAGE", "link": 714}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_LINE", "properties": {"previousName": "LINE"}, "widgets_values": ["LINE"], "color": "#223", "bgcolor": "#335"}, {"id": 330, "type": "GetNode", "pos": {"0": 6416, "1": 1487}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [726], "slot_index": 0}], "title": "Get_LINE", "properties": {}, "widgets_values": ["LINE"], "color": "#223", "bgcolor": "#335"}, {"id": 331, "type": "GetNode", "pos": {"0": 5934, "1": 1501}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 11, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [727], "slot_index": 0}], "title": "Get_NORMAL", "properties": {}, "widgets_values": ["NORMAL"], "color": "#223", "bgcolor": "#335"}, {"id": 110, "type": "ADE_UseEvolvedSampling", "pos": {"0": 4985.31396484375, "1": 2536.87451171875}, "size": {"0": 315, "1": 118}, "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 745}, {"name": "m_models", "type": "M_MODELS", "link": 177, "shape": 7}, {"name": "context_options", "type": "CONTEXT_OPTIONS", "link": 178, "shape": 7}, {"name": "sample_settings", "type": "SAMPLE_SETTINGS", "link": null, "shape": 7}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [328], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ADE_UseEvolvedSampling"}, "widgets_values": ["autoselect"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 65, "type": "CLIPSetLastLayer", "pos": {"0": 974.0007934570312, "1": 2294.************}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 718}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [620], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-1], "color": "#323", "bgcolor": "#535"}, {"id": 62, "type": "ACN_AdvancedControlNetApply", "pos": {"0": 5510, "1": 1730}, "size": {"0": 355.20001220703125, "1": 286}, "flags": {}, "order": 137, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 90}, {"name": "negative", "type": "CONDITIONING", "link": 91}, {"name": "control_net", "type": "CONTROL_NET", "link": 92}, {"name": "image", "type": "IMAGE", "link": 689}, {"name": "mask_optional", "type": "MASK", "link": null, "shape": 7}, {"name": "timestep_kf", "type": "TIMESTEP_KEYFRAME", "link": null, "shape": 7}, {"name": "latent_kf_override", "type": "LATENT_KEYFRAME", "link": null, "shape": 7}, {"name": "weights_override", "type": "CONTROL_NET_WEIGHTS", "link": null, "shape": 7}, {"name": "model_optional", "type": "MODEL", "link": null, "shape": 7}, {"name": "vae_optional", "type": "VAE", "link": null, "shape": 7}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [114], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [115], "slot_index": 1, "shape": 3}, {"name": "model_opt", "type": "MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ACN_AdvancedControlNetApply"}, "widgets_values": [0.98, 0, 1, ""], "color": "#223", "bgcolor": "#335"}, {"id": 6, "type": "MaskPreview+", "pos": {"0": 3040, "1": -240}, "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 7}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 163, "type": "MaskPreview+", "pos": {"0": 3040, "1": 730}, "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 121, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 282}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 196, "type": "ModelSamplingDiscrete", "pos": {"0": 1331.0008544921875, "1": 2120.************}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 684}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [728], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingDiscrete"}, "widgets_values": ["lcm", false], "color": "#323", "bgcolor": "#535"}, {"id": 91, "type": "Note", "pos": {"0": 363.73712158203125, "1": 625.5263671875}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["Load Mask Pass here -->"], "color": "#432", "bgcolor": "#653"}, {"id": 116, "type": "PreviewImage", "pos": {"0": 1384, "1": 1606}, "size": {"0": 251.63223266601562, "1": 248.08242797851562}, "flags": {"collapsed": false}, "order": 48, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 190}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#432", "bgcolor": "#653"}, {"id": 82, "type": "PreviewImage", "pos": {"0": 1374, "1": 1285}, "size": {"0": 251.63223266601562, "1": 248.08242797851562}, "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 125}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 58, "type": "PreviewImage", "pos": {"0": 1375, "1": 946}, "size": {"0": 252.15289306640625, "1": 248.84286499023438}, "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 610}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 297, "type": "DF_Integer", "pos": {"0": 234, "1": 386}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [631, 632, 635, 739], "slot_index": 0, "shape": 3}], "title": "SkipFirstImages", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [0]}, {"id": 300, "type": "DF_Integer", "pos": {"0": 234, "1": 486}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [649, 650, 652, 740], "slot_index": 0, "shape": 3}], "title": "SelectEveryNth", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [1]}, {"id": 132, "type": "FILM VFI", "pos": {"0": 8018, "1": 1710}, "size": {"0": 443.4000244140625, "1": 126}, "flags": {}, "order": 142, "mode": 4, "inputs": [{"name": "frames", "type": "IMAGE", "link": 647}, {"name": "optional_interpolation_states", "type": "INTERPOLATION_STATES", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [648], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FILM VFI"}, "widgets_values": ["film_net_fp32.pt", 10, 2]}, {"id": 187, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": 7153, "1": 1710}, "size": {"0": 315, "1": 474}, "flags": {}, "order": 140, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 328}, {"name": "positive", "type": "CONDITIONING", "link": 329}, {"name": "negative", "type": "CONDITIONING", "link": 330}, {"name": "latent_image", "type": "LATENT", "link": 703}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [332], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [9984526452378, "fixed", 8, 1.6, "lcm", "sgm_uniform", 1], "color": "#323", "bgcolor": "#535"}, {"id": 233, "type": "DF_Integer", "pos": {"0": 234, "1": 286}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [454, 455, 457, 594, 738], "slot_index": 0, "shape": 3}], "title": "ImageLoadCap", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [100]}, {"id": 231, "type": "DF_Integer", "pos": {"0": 614, "1": 281}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [450, 459, 465, 570, 572, 575], "slot_index": 0, "shape": 3}], "title": "<PERSON><PERSON><PERSON>", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [768]}, {"id": 269, "type": "SolidColorRGB", "pos": {"0": 1910, "1": 1050}, "size": {"0": 315.2945251464844, "1": 150}, "flags": {"collapsed": false}, "order": 43, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 563, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 561, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [557], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, ""], "color": "#233", "bgcolor": "#355"}, {"id": 259, "type": "SolidColorRGB", "pos": {"0": 1910, "1": 730}, "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 554, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 528, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [522], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#00dec8"], "color": "#233", "bgcolor": "#355"}, {"id": 261, "type": "MaskFromRGBCMYBW+", "pos": {"0": 2260, "1": 730}, "size": {"0": 315, "1": 294}, "flags": {"collapsed": true}, "order": 98, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 521}], "outputs": [{"name": "red", "type": "MASK", "links": null, "shape": 3}, {"name": "green", "type": "MASK", "links": null, "shape": 3}, {"name": "blue", "type": "MASK", "links": null, "shape": 3}, {"name": "cyan", "type": "MASK", "links": null, "shape": 3}, {"name": "magenta", "type": "MASK", "links": null, "shape": 3}, {"name": "yellow", "type": "MASK", "links": null, "shape": 3}, {"name": "black", "type": "MASK", "links": [524], "slot_index": 6, "shape": 3}, {"name": "white", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "MaskFromRGBCMYBW+"}, "widgets_values": [0.05, 0.05, 0.05], "color": "#233", "bgcolor": "#355"}, {"id": 272, "type": "ImageScale", "pos": {"0": 1910, "1": 1240}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 62, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 557}, {"name": "width", "type": "INT", "link": 562, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 564, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [558], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 262, "type": "ImageScale", "pos": {"0": 1910, "1": 930}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": true}, "order": 61, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 522}, {"name": "width", "type": "INT", "link": 527, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 552, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [523], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 720, "disabled"], "color": "#233", "bgcolor": "#355"}, {"id": 157, "type": "ImpactGaussianBlurMask", "pos": {"0": 2510, "1": 410}, "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 105, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 514}], "outputs": [{"name": "MASK", "type": "MASK", "links": [271, 547, 582], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 254, "type": "SolidColorRGB", "pos": {"0": 1910, "1": 410}, "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 517, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 516, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [512], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#0094d7"], "color": "#233", "bgcolor": "#355"}, {"id": 155, "type": "MaskPreview+", "pos": {"0": 3040, "1": 410}, "size": {"0": 224.18955993652344, "1": 246}, "flags": {}, "order": 119, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 271}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 343, "type": "CheckpointLoaderSimple", "pos": {"0": 588.0007934570312, "1": 2016.9473876953125}, "size": {"0": 315, "1": 98}, "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [717], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [718], "slot_index": 1, "shape": 3}, {"name": "VAE", "type": "VAE", "links": [719], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["juggernaut_reborn.safetensors"], "color": "#323", "bgcolor": "#535"}, {"id": 348, "type": "FreeU_V2", "pos": {"0": 1329, "1": 2260}, "size": {"0": 315, "1": 130}, "flags": {"collapsed": false}, "order": 63, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 728}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [745], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FreeU_V2"}, "widgets_values": [1.3, 1.4, 0.9, 0.2], "color": "#323", "bgcolor": "#535"}, {"id": 90, "type": "Reroute", "pos": {"0": 603, "1": 2407}, "size": [75, 26], "flags": {"pinned": false}, "order": 23, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 594, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [134], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#323", "bgcolor": "#535"}, {"id": 7, "type": "EmptyLatentImage", "pos": {"0": 969, "1": 2408}, "size": {"0": 315, "1": 106}, "flags": {"collapsed": false}, "order": 33, "mode": 0, "inputs": [{"name": "batch_size", "type": "INT", "link": 134, "widget": {"name": "batch_size"}}, {"name": "height", "type": "INT", "link": 574, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 575, "widget": {"name": "width"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [746], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 432, 10], "color": "#323", "bgcolor": "#535"}, {"id": 338, "type": "SetNode", "pos": {"0": 1410, "1": 2441}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "LATENT", "type": "LATENT", "link": 746}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_LATENT", "properties": {"previousName": "LATENT"}, "widgets_values": ["LATENT"], "color": "#323", "bgcolor": "#535"}, {"id": 352, "type": "ConditioningSetMaskAndCombine4", "pos": {"0": 4690, "1": -540}, "size": {"0": 355.20001220703125, "1": 374}, "flags": {"collapsed": true}, "order": 132, "mode": 0, "inputs": [{"name": "positive_1", "type": "CONDITIONING", "link": 749}, {"name": "negative_1", "type": "CONDITIONING", "link": 750}, {"name": "positive_2", "type": "CONDITIONING", "link": 751}, {"name": "negative_2", "type": "CONDITIONING", "link": 752}, {"name": "positive_3", "type": "CONDITIONING", "link": 753}, {"name": "negative_3", "type": "CONDITIONING", "link": 754}, {"name": "positive_4", "type": "CONDITIONING", "link": 755}, {"name": "negative_4", "type": "CONDITIONING", "link": 756}, {"name": "mask_1", "type": "MASK", "link": 757}, {"name": "mask_2", "type": "MASK", "link": 758}, {"name": "mask_3", "type": "MASK", "link": 759}, {"name": "mask_4", "type": "MASK", "link": 760}], "outputs": [{"name": "combined_positive", "type": "CONDITIONING", "links": [773], "slot_index": 0, "shape": 3}, {"name": "combined_negative", "type": "CONDITIONING", "links": [], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "ConditioningSetMaskAndCombine4"}, "widgets_values": [1, 1, 1, 1, "default"]}, {"id": 353, "type": "Reroute", "pos": {"0": 4480, "1": -1230}, "size": [75, 26], "flags": {}, "order": 125, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 795}], "outputs": [{"name": "", "type": "MASK", "links": [757]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 354, "type": "Reroute", "pos": {"0": 4480, "1": -870}, "size": [75, 26], "flags": {}, "order": 126, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 794}], "outputs": [{"name": "", "type": "MASK", "links": [758]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 355, "type": "Reroute", "pos": {"0": 4480, "1": -550}, "size": [75, 26], "flags": {}, "order": 127, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 793}], "outputs": [{"name": "", "type": "MASK", "links": [759]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 356, "type": "Reroute", "pos": {"0": 4480, "1": -220}, "size": [75, 26], "flags": {}, "order": 128, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 791}], "outputs": [{"name": "", "type": "MASK", "links": [760]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 357, "type": "ConditioningSetMaskAndCombine4", "pos": {"0": 4690, "1": 640}, "size": {"0": 355.20001220703125, "1": 374}, "flags": {"collapsed": true}, "order": 133, "mode": 0, "inputs": [{"name": "positive_1", "type": "CONDITIONING", "link": 761}, {"name": "negative_1", "type": "CONDITIONING", "link": 762}, {"name": "positive_2", "type": "CONDITIONING", "link": 763}, {"name": "negative_2", "type": "CONDITIONING", "link": 764}, {"name": "positive_3", "type": "CONDITIONING", "link": 765}, {"name": "negative_3", "type": "CONDITIONING", "link": 766}, {"name": "positive_4", "type": "CONDITIONING", "link": 767}, {"name": "negative_4", "type": "CONDITIONING", "link": 768}, {"name": "mask_1", "type": "MASK", "link": 769}, {"name": "mask_2", "type": "MASK", "link": 770}, {"name": "mask_3", "type": "MASK", "link": 771}, {"name": "mask_4", "type": "MASK", "link": 772}], "outputs": [{"name": "combined_positive", "type": "CONDITIONING", "links": [774], "slot_index": 0, "shape": 3}, {"name": "combined_negative", "type": "CONDITIONING", "links": [], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "ConditioningSetMaskAndCombine4"}, "widgets_values": [1, 1, 1, 1, "default"]}, {"id": 358, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": {"0": 4970, "1": 50}, "size": {"0": 342.5999755859375, "1": 46}, "flags": {"collapsed": true}, "order": 134, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 773}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 774}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [776], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 359, "type": "Reroute", "pos": {"0": 4480, "1": 130}, "size": [75, 26], "flags": {}, "order": 118, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 790}], "outputs": [{"name": "", "type": "MASK", "links": [769]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 360, "type": "Reroute", "pos": {"0": 4480, "1": 430}, "size": [75, 26], "flags": {}, "order": 129, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 789}], "outputs": [{"name": "", "type": "MASK", "links": [770]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 361, "type": "Reroute", "pos": {"0": 4480, "1": 750}, "size": [75, 26], "flags": {}, "order": 130, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 788}], "outputs": [{"name": "", "type": "MASK", "links": [771]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 362, "type": "Reroute", "pos": {"0": 4480, "1": 1070}, "size": [75, 26], "flags": {}, "order": 131, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 787}], "outputs": [{"name": "", "type": "MASK", "links": [772]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#233", "bgcolor": "#355"}, {"id": 363, "type": "CLIPTextEncode", "pos": {"0": 3400, "1": -1550}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 786}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [775], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cinematic film still, (masterpiece), cinematic, best quality, 8k, high detail, photography, bokeh, 35mm film movie still, photorealism, 24mm lens, sharp focus, (perfect real extremely details), sony fe 12-24mm f/2.8 gm, close up, 32k uhd, underwater, long exposure photography, wildlife photography, nature documentary, backlight, fog, atmosphere, "], "color": "#223", "bgcolor": "#335"}, {"id": 369, "type": "CLIPTextEncode", "pos": {"0": 3400, "1": 1100}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 778}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [767], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["alien planet, oceans, atmosphere, clouds, satellite image"], "color": "#232", "bgcolor": "#353"}, {"id": 370, "type": "CLIPTextEncode", "pos": {"0": 3400, "1": 760}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 779}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [765], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["alien planet, oceans, atmosphere, clouds, satellite image"], "color": "#232", "bgcolor": "#353"}, {"id": 371, "type": "CLIPTextEncode", "pos": {"0": 3400, "1": 450}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 780}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [763], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["alien planet, oceans, atmosphere, clouds, satellite image"], "color": "#232", "bgcolor": "#353"}, {"id": 372, "type": "CLIPTextEncode", "pos": {"0": 3400, "1": 100}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 781}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [761], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["alien planet, oceans, atmosphere, clouds, satellite image"], "color": "#232", "bgcolor": "#353"}, {"id": 14, "type": "Reroute", "pos": {"0": 3167, "1": 1705}, "size": [75, 26], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 741}], "outputs": [{"name": "", "type": "CLIP", "links": [777, 778, 779, 780, 781, 782, 783, 784, 785, 786], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 280, "type": "MaskListToMaskBatch", "pos": {"0": 2850, "1": 1050}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 124, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 578}], "outputs": [{"name": "MASK", "type": "MASK", "links": [787], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 281, "type": "MaskListToMaskBatch", "pos": {"0": 2850, "1": 725}, "size": {"0": 193.1999969482422, "1": 26}, "flags": {"collapsed": true}, "order": 122, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 580}], "outputs": [{"name": "MASK", "type": "MASK", "links": [788], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 282, "type": "MaskListToMaskBatch", "pos": {"0": 2850, "1": 410}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 120, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 582}], "outputs": [{"name": "MASK", "type": "MASK", "links": [789], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 147, "type": "ImpactGaussianBlurMask", "pos": {"0": 2509.710205078125, "1": 90}, "size": {"0": 315, "1": 82}, "flags": {"collapsed": false}, "order": 104, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 504}], "outputs": [{"name": "MASK", "type": "MASK", "links": [254, 584, 790], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 284, "type": "MaskListToMaskBatch", "pos": {"0": 2850, "1": -190}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 115, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 586}], "outputs": [{"name": "MASK", "type": "MASK", "links": [791], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 55, "type": "ImpactGaussianBlurMask", "pos": {"0": 2509.710205078125, "1": -560}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 484}], "outputs": [{"name": "MASK", "type": "MASK", "links": [6, 588], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactGaussianBlurMask"}, "widgets_values": [5, 5], "color": "#233", "bgcolor": "#355"}, {"id": 285, "type": "MaskListToMaskBatch", "pos": {"0": 2850, "1": -535}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 113, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 588}], "outputs": [{"name": "MASK", "type": "MASK", "links": [793], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 286, "type": "MaskListToMaskBatch", "pos": {"0": 2850, "1": -840}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 111, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 590}], "outputs": [{"name": "MASK", "type": "MASK", "links": [794], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 287, "type": "MaskListToMaskBatch", "pos": {"0": 2850, "1": -1240}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 109, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 592}], "outputs": [{"name": "MASK", "type": "MASK", "links": [795], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskListToMaskBatch"}, "widgets_values": [], "color": "#233", "bgcolor": "#355"}, {"id": 368, "type": "CLIPTextEncode", "pos": {"0": 3410, "1": 1440}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 777}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [750, 752, 754, 756, 762, 764, 766, 768, 797], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark, uniform, patterns, underexposed, ugly, dark, night, oversaturated, high contrast, jpeg, (worst quality, low quality, normal quality, lowres, low details, oversaturated, undersaturated, overexposed, underexposed, grayscale, bw, bad photo, photography, bad art:1.4), (font, username, error, logo, words, letters, digits, autograph, trademark, name:1.2), (blur, blurry, grainy), poorly lit, bad shadow, draft, cropped, out of frame, cut off,, jpeg artifacts, out of focus, glitch, duplicate, (amateur:1.3), "], "color": "#322", "bgcolor": "#533"}, {"id": 373, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": {"0": 5060, "1": 100}, "size": {"0": 342.5999755859375, "1": 46}, "flags": {"collapsed": true}, "order": 135, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 775}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 776}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [796], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 107, "type": "ADE_LoadAnimateDiffModel", "pos": {"0": 4979, "1": 2209}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "ad_settings", "type": "AD_SETTINGS", "link": null, "shape": 7}], "outputs": [{"name": "MOTION_MODEL", "type": "MOTION_MODEL_ADE", "links": [176], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ADE_LoadAnimateDiffModel"}, "widgets_values": ["AnimateLCM_sd15_t2v.ckpt"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 57, "type": "VHS_LoadImagesPath", "pos": {"0": 653.58056640625, "1": 945.64599609375}, "size": [293.7653503417969, 142], "flags": {"collapsed": false}, "order": 21, "mode": 0, "inputs": [{"name": "image_load_cap", "type": "INT", "link": 455, "widget": {"name": "image_load_cap"}}, {"name": "skip_first_images", "type": "INT", "link": 632, "widget": {"name": "skip_first_images"}}, {"name": "select_every_nth", "type": "INT", "link": 650, "widget": {"name": "select_every_nth"}}, {"name": "meta_batch", "type": "VHS_BatchManager", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [716], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}, {"name": "frame_count", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_LoadImagesPath"}, "widgets_values": {"directory": "D:\\02_MICKMUMPITZ\\01_PROJECTS\\240226_AI_RENDERING\\squid\\squid_render_depth_v01\\squid_render_depth_v01_", "image_load_cap": 10, "skip_first_images": 0, "select_every_nth": 1, "choose folder to upload": "image", "videopreview": {"hidden": true, "paused": false, "params": {"frame_load_cap": 10, "skip_first_images": 0, "filename": "D:\\02_MICKMUMPITZ\\01_PROJECTS\\240226_AI_RENDERING\\squid\\squid_render_depth_v01\\squid_render_depth_v01_", "type": "path", "format": "folder", "select_every_nth": 1, "skip_first_frames": 0}}}, "color": "#223", "bgcolor": "#335"}, {"id": 78, "type": "VHS_LoadImagesPath", "pos": {"0": 662, "1": 1259}, "size": [300, 142], "flags": {"collapsed": false}, "order": 24, "mode": 0, "inputs": [{"name": "image_load_cap", "type": "INT", "link": 738, "slot_index": 0, "widget": {"name": "image_load_cap"}}, {"name": "skip_first_images", "type": "INT", "link": 739, "widget": {"name": "skip_first_images"}}, {"name": "select_every_nth", "type": "INT", "link": 740, "widget": {"name": "select_every_nth"}}, {"name": "meta_batch", "type": "VHS_BatchManager", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [119], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}, {"name": "frame_count", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_LoadImagesPath"}, "widgets_values": {"directory": "D:\\02_MICKMUMPITZ\\01_PROJECTS\\240226_AI_RENDERING\\squid\\squid_render_freestyle_v01\\squid_render_freestyle_v01_", "image_load_cap": 10, "skip_first_images": 0, "select_every_nth": 1, "choose folder to upload": "image", "videopreview": {"hidden": true, "paused": false, "params": {"frame_load_cap": 10, "skip_first_images": 0, "filename": "D:\\02_MICKMUMPITZ\\01_PROJECTS\\240226_AI_RENDERING\\squid\\squid_render_freestyle_v01\\squid_render_freestyle_v01_", "type": "path", "format": "folder", "select_every_nth": 1, "skip_first_frames": 0}}}, "color": "#223", "bgcolor": "#335"}, {"id": 114, "type": "ACN_AdvancedControlNetApply", "pos": {"0": 6420, "1": 1730}, "size": {"0": 355.20001220703125, "1": 286}, "flags": {}, "order": 139, "mode": 4, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 186}, {"name": "negative", "type": "CONDITIONING", "link": 187}, {"name": "control_net", "type": "CONTROL_NET", "link": 432}, {"name": "image", "type": "IMAGE", "link": 726}, {"name": "mask_optional", "type": "MASK", "link": null, "shape": 7}, {"name": "timestep_kf", "type": "TIMESTEP_KEYFRAME", "link": null, "shape": 7}, {"name": "latent_kf_override", "type": "LATENT_KEYFRAME", "link": null, "shape": 7}, {"name": "weights_override", "type": "CONTROL_NET_WEIGHTS", "link": null, "shape": 7}, {"name": "model_optional", "type": "MODEL", "link": null, "shape": 7}, {"name": "vae_optional", "type": "VAE", "link": null, "shape": 7}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [329], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [330], "slot_index": 1, "shape": 3}, {"name": "model_opt", "type": "MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ACN_AdvancedControlNetApply"}, "widgets_values": [0.23, 0, 1, ""], "color": "#223", "bgcolor": "#335"}, {"id": 224, "type": "SolidColorRGB", "pos": {"0": 1910, "1": -1250}, "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 444, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 446, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [438], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#05ff04"], "color": "#233", "bgcolor": "#355"}, {"id": 234, "type": "SolidColorRGB", "pos": {"0": 1909.7103271484375, "1": -890}, "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 478, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 479, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [471], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#00b2ff"], "color": "#233", "bgcolor": "#355"}, {"id": 239, "type": "SolidColorRGB", "pos": {"0": 1909.7103271484375, "1": -580}, "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 486, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 485, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [482], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#ff9000"], "color": "#233", "bgcolor": "#355"}, {"id": 2, "type": "VHS_LoadImagesPath", "pos": {"0": 654, "1": 626}, "size": [291.70172119140625, 142], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "image_load_cap", "type": "INT", "link": 454, "widget": {"name": "image_load_cap"}}, {"name": "skip_first_images", "type": "INT", "link": 631, "widget": {"name": "skip_first_images"}}, {"name": "select_every_nth", "type": "INT", "link": 649, "widget": {"name": "select_every_nth"}}, {"name": "meta_batch", "type": "VHS_BatchManager", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [30], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}, {"name": "frame_count", "type": "INT", "links": null, "shape": 3}], "title": "Load Images (Path) MASK 🎥🅥🅗🅢", "properties": {"Node name for S&R": "VHS_LoadImagesPath"}, "widgets_values": {"directory": "D:\\02_MICKMUMPITZ\\01_PROJECTS\\240226_AI_RENDERING\\squid\\squid_render_mask_v01", "image_load_cap": 10, "skip_first_images": 0, "select_every_nth": 1, "choose folder to upload": "image", "videopreview": {"hidden": true, "paused": false, "params": {"frame_load_cap": 10, "skip_first_images": 0, "filename": "D:\\02_MICKMUMPITZ\\01_PROJECTS\\240226_AI_RENDERING\\squid\\squid_render_mask_v01", "type": "path", "format": "folder", "select_every_nth": 1, "skip_first_frames": 0}}}, "color": "#232", "bgcolor": "#353"}, {"id": 3, "type": "PreviewImage", "pos": {"0": 1374, "1": 626}, "size": {"0": 252.57337951660156, "1": 246}, "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 605}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#232", "bgcolor": "#353"}, {"id": 232, "type": "DF_Integer", "pos": {"0": 609, "1": 381}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [451, 460, 466, 571, 573, 574], "slot_index": 0, "shape": 3}], "title": "Height", "properties": {"Node name for S&R": "DF_Integer"}, "widgets_values": [432]}, {"id": 244, "type": "SolidColorRGB", "pos": {"0": 1909.7103271484375, "1": -270}, "size": {"0": 315.2945251464844, "1": 150}, "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "height", "type": "INT", "link": 496, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 495, "widget": {"name": "width"}}], "outputs": [{"name": "solid color image", "type": "IMAGE", "links": [492], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SolidColorRGB"}, "widgets_values": [0, 0, 0, 1080, 768, "#ec00ff"], "color": "#233", "bgcolor": "#355"}, {"id": 367, "type": "CLIPTextEncode", "pos": {"0": 3400, "1": -210}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 782}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [755], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["glasses"], "color": "#232", "bgcolor": "#353"}, {"id": 366, "type": "CLIPTextEncode", "pos": {"0": 3400, "1": -520}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 783}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [753], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["alien planet, saturn"], "color": "#232", "bgcolor": "#353"}, {"id": 365, "type": "CLIPTextEncode", "pos": {"0": 3400, "1": -840}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 784}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [751], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["((squid)), organic, intricate design, cute, alien"], "color": "#232", "bgcolor": "#353"}, {"id": 364, "type": "CLIPTextEncode", "pos": {"0": 3400, "1": -1170}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 785}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [749], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["space, stars"], "color": "#232", "bgcolor": "#353"}, {"id": 13, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": 969.0007934570312, "1": 2120.************}, "size": {"0": 315, "1": 126}, "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 717}, {"name": "clip", "type": "CLIP", "link": 620}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [684], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [741], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["sd15_lora_beta.safetensors", 1, 1], "color": "#323", "bgcolor": "#535"}], "links": [[5, 53, 0, 4, 0, "MASK"], [6, 55, 0, 5, 0, "MASK"], [7, 56, 0, 6, 0, "MASK"], [30, 2, 0, 15, 0, "IMAGE"], [90, 46, 0, 62, 0, "CONDITIONING"], [91, 47, 0, 62, 1, "CONDITIONING"], [92, 61, 0, 62, 2, "CONTROL_NET"], [95, 54, 0, 64, 0, "MASK"], [113, 76, 0, 77, 2, "CONTROL_NET"], [114, 62, 0, 77, 0, "CONDITIONING"], [115, 62, 1, 77, 1, "CONDITIONING"], [119, 78, 0, 79, 0, "IMAGE"], [125, 79, 0, 82, 0, "IMAGE"], [134, 90, 0, 7, 0, "INT"], [176, 107, 0, 109, 0, "MOTION_MODEL_ADE"], [177, 109, 0, 110, 1, "M_MODELS"], [178, 108, 0, 110, 2, "CONTEXT_OPTIONS"], [186, 77, 0, 114, 0, "CONDITIONING"], [187, 77, 1, 114, 1, "CONDITIONING"], [190, 117, 0, 116, 0, "IMAGE"], [191, 115, 0, 117, 0, "IMAGE"], [254, 147, 0, 145, 0, "MASK"], [271, 157, 0, 155, 0, "MASK"], [282, 165, 0, 163, 0, "MASK"], [288, 173, 0, 171, 0, "MASK"], [328, 110, 0, 187, 0, "MODEL"], [329, 114, 0, 187, 1, "CONDITIONING"], [330, 114, 1, 187, 2, "CONDITIONING"], [332, 187, 0, 1, 0, "LATENT"], [432, 113, 0, 114, 2, "CONTROL_NET"], [435, 228, 0, 225, 0, "IMAGE"], [436, 229, 0, 225, 1, "IMAGE"], [437, 225, 0, 226, 0, "IMAGE"], [438, 224, 0, 227, 0, "IMAGE"], [439, 227, 0, 228, 0, "IMAGE"], [440, 226, 6, 53, 0, "MASK"], [441, 15, 0, 229, 0, "IMAGE"], [442, 209, 0, 227, 2, "INT"], [443, 208, 0, 227, 1, "INT"], [444, 209, 0, 224, 0, "INT"], [446, 208, 0, 224, 1, "INT"], [450, 231, 0, 208, 0, "*"], [451, 232, 0, 209, 0, "*"], [454, 233, 0, 2, 0, "INT"], [455, 233, 0, 57, 0, "INT"], [457, 233, 0, 115, 0, "INT"], [459, 231, 0, 15, 1, "INT"], [460, 232, 0, 15, 2, "INT"], [465, 231, 0, 117, 1, "INT"], [466, 232, 0, 117, 2, "INT"], [469, 238, 0, 235, 0, "IMAGE"], [470, 235, 0, 236, 0, "IMAGE"], [471, 234, 0, 237, 0, "IMAGE"], [472, 237, 0, 238, 0, "IMAGE"], [473, 236, 6, 54, 0, "MASK"], [474, 229, 0, 235, 1, "IMAGE"], [475, 208, 0, 237, 1, "INT"], [476, 209, 0, 237, 2, "INT"], [478, 209, 0, 234, 0, "INT"], [479, 208, 0, 234, 1, "INT"], [480, 243, 0, 240, 0, "IMAGE"], [481, 240, 0, 241, 0, "IMAGE"], [482, 239, 0, 242, 0, "IMAGE"], [483, 242, 0, 243, 0, "IMAGE"], [484, 241, 6, 55, 0, "MASK"], [485, 208, 0, 239, 1, "INT"], [486, 209, 0, 239, 0, "INT"], [487, 208, 0, 242, 1, "INT"], [488, 209, 0, 242, 2, "INT"], [489, 229, 0, 240, 1, "IMAGE"], [490, 248, 0, 245, 0, "IMAGE"], [491, 245, 0, 246, 0, "IMAGE"], [492, 244, 0, 247, 0, "IMAGE"], [493, 247, 0, 248, 0, "IMAGE"], [494, 246, 6, 56, 0, "MASK"], [495, 208, 0, 244, 1, "INT"], [496, 209, 0, 244, 0, "INT"], [497, 208, 0, 247, 1, "INT"], [498, 209, 0, 247, 2, "INT"], [499, 229, 0, 245, 1, "IMAGE"], [500, 253, 0, 250, 0, "IMAGE"], [501, 250, 0, 251, 0, "IMAGE"], [502, 249, 0, 252, 0, "IMAGE"], [503, 252, 0, 253, 0, "IMAGE"], [504, 251, 6, 147, 0, "MASK"], [505, 208, 0, 249, 1, "INT"], [506, 209, 0, 249, 0, "INT"], [507, 208, 0, 252, 1, "INT"], [508, 209, 0, 252, 2, "INT"], [509, 229, 0, 250, 1, "IMAGE"], [510, 258, 0, 255, 0, "IMAGE"], [511, 255, 0, 256, 0, "IMAGE"], [512, 254, 0, 257, 0, "IMAGE"], [513, 257, 0, 258, 0, "IMAGE"], [514, 256, 6, 157, 0, "MASK"], [515, 208, 0, 257, 1, "INT"], [516, 208, 0, 254, 1, "INT"], [517, 209, 0, 254, 0, "INT"], [518, 209, 0, 257, 2, "INT"], [519, 229, 0, 255, 1, "IMAGE"], [520, 263, 0, 260, 0, "IMAGE"], [521, 260, 0, 261, 0, "IMAGE"], [522, 259, 0, 262, 0, "IMAGE"], [523, 262, 0, 263, 0, "IMAGE"], [524, 261, 6, 165, 0, "MASK"], [527, 208, 0, 262, 1, "INT"], [528, 208, 0, 259, 1, "INT"], [543, 54, 0, 266, 0, "MASK"], [545, 56, 0, 266, 1, "MASK"], [547, 157, 0, 266, 2, "MASK"], [549, 173, 0, 266, 3, "MASK"], [552, 209, 0, 262, 2, "INT"], [553, 229, 0, 260, 1, "IMAGE"], [554, 209, 0, 259, 0, "INT"], [555, 273, 0, 270, 0, "IMAGE"], [556, 270, 0, 271, 0, "IMAGE"], [557, 269, 0, 272, 0, "IMAGE"], [558, 272, 0, 273, 0, "IMAGE"], [559, 271, 6, 173, 0, "MASK"], [560, 229, 0, 270, 1, "IMAGE"], [561, 208, 0, 269, 1, "INT"], [562, 208, 0, 272, 1, "INT"], [563, 209, 0, 269, 0, "INT"], [564, 209, 0, 272, 2, "INT"], [570, 231, 0, 59, 1, "INT"], [571, 232, 0, 59, 2, "INT"], [572, 231, 0, 79, 1, "INT"], [573, 232, 0, 79, 2, "INT"], [574, 232, 0, 7, 1, "INT"], [575, 231, 0, 7, 2, "INT"], [578, 173, 0, 280, 0, "MASK"], [580, 165, 0, 281, 0, "MASK"], [582, 157, 0, 282, 0, "MASK"], [584, 147, 0, 283, 0, "MASK"], [586, 56, 0, 284, 0, "MASK"], [588, 55, 0, 285, 0, "MASK"], [590, 54, 0, 286, 0, "MASK"], [592, 53, 0, 287, 0, "MASK"], [594, 233, 0, 90, 0, "*"], [605, 15, 0, 3, 0, "IMAGE"], [610, 59, 0, 58, 0, "IMAGE"], [620, 65, 0, 13, 1, "CLIP"], [631, 297, 0, 2, 1, "INT"], [632, 297, 0, 57, 1, "INT"], [635, 297, 0, 115, 1, "INT"], [647, 1, 0, 132, 0, "IMAGE"], [648, 132, 0, 194, 0, "IMAGE"], [649, 300, 0, 2, 2, "INT"], [650, 300, 0, 57, 2, "INT"], [652, 300, 0, 115, 2, "INT"], [684, 13, 0, 196, 0, "MODEL"], [689, 320, 0, 62, 3, "IMAGE"], [703, 339, 0, 187, 3, "LATENT"], [706, 341, 0, 1, 1, "VAE"], [713, 117, 0, 323, 0, "IMAGE"], [714, 79, 0, 322, 0, "IMAGE"], [715, 59, 0, 319, 0, "IMAGE"], [716, 57, 0, 59, 0, "IMAGE"], [717, 343, 0, 13, 0, "MODEL"], [718, 343, 1, 65, 0, "CLIP"], [719, 343, 2, 340, 0, "VAE"], [726, 330, 0, 114, 3, "IMAGE"], [727, 331, 0, 77, 3, "IMAGE"], [728, 196, 0, 348, 0, "MODEL"], [738, 233, 0, 78, 0, "INT"], [739, 297, 0, 78, 1, "INT"], [740, 300, 0, 78, 2, "INT"], [741, 13, 1, 14, 0, "*"], [745, 348, 0, 110, 0, "MODEL"], [746, 7, 0, 338, 0, "*"], [749, 364, 0, 352, 0, "CONDITIONING"], [750, 368, 0, 352, 1, "CONDITIONING"], [751, 365, 0, 352, 2, "CONDITIONING"], [752, 368, 0, 352, 3, "CONDITIONING"], [753, 366, 0, 352, 4, "CONDITIONING"], [754, 368, 0, 352, 5, "CONDITIONING"], [755, 367, 0, 352, 6, "CONDITIONING"], [756, 368, 0, 352, 7, "CONDITIONING"], [757, 353, 0, 352, 8, "MASK"], [758, 354, 0, 352, 9, "MASK"], [759, 355, 0, 352, 10, "MASK"], [760, 356, 0, 352, 11, "MASK"], [761, 372, 0, 357, 0, "CONDITIONING"], [762, 368, 0, 357, 1, "CONDITIONING"], [763, 371, 0, 357, 2, "CONDITIONING"], [764, 368, 0, 357, 3, "CONDITIONING"], [765, 370, 0, 357, 4, "CONDITIONING"], [766, 368, 0, 357, 5, "CONDITIONING"], [767, 369, 0, 357, 6, "CONDITIONING"], [768, 368, 0, 357, 7, "CONDITIONING"], [769, 359, 0, 357, 8, "MASK"], [770, 360, 0, 357, 9, "MASK"], [771, 361, 0, 357, 10, "MASK"], [772, 362, 0, 357, 11, "MASK"], [773, 352, 0, 358, 0, "CONDITIONING"], [774, 357, 0, 358, 1, "CONDITIONING"], [775, 363, 0, 373, 0, "CONDITIONING"], [776, 358, 0, 373, 1, "CONDITIONING"], [777, 14, 0, 368, 0, "CLIP"], [778, 14, 0, 369, 0, "CLIP"], [779, 14, 0, 370, 0, "CLIP"], [780, 14, 0, 371, 0, "CLIP"], [781, 14, 0, 372, 0, "CLIP"], [782, 14, 0, 367, 0, "CLIP"], [783, 14, 0, 366, 0, "CLIP"], [784, 14, 0, 365, 0, "CLIP"], [785, 14, 0, 364, 0, "CLIP"], [786, 14, 0, 363, 0, "CLIP"], [787, 280, 0, 362, 0, "*"], [788, 281, 0, 361, 0, "*"], [789, 282, 0, 360, 0, "*"], [790, 147, 0, 359, 0, "*"], [791, 284, 0, 356, 0, "*"], [793, 285, 0, 355, 0, "*"], [794, 286, 0, 354, 0, "*"], [795, 287, 0, 353, 0, "*"], [796, 373, 0, 46, 0, "*"], [797, 368, 0, 47, 0, "*"]], "groups": [{"title": "EXTRACT MASKS", "bounding": [1833, -1335, 1463, 2681], "color": "#8AA", "font_size": 24, "flags": {}}, {"title": "PROMPTS", "bounding": [3351, -1661, 479, 3326], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "CONTROLNET", "bounding": [5441, 1414, 1383, 601], "color": "#88A", "font_size": 24, "flags": {}}, {"title": "ANIMATEDIFF", "bounding": [4562, 2132, 752, 546], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "INPUT", "bounding": [207, 113, 1470, 1766], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "MODELS", "bounding": [560, 1920, 1124, 611], "color": "#a1309b", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.2143588810000054, "offset": [1407.980757358122, 2387.212474122582]}}, "version": 0.4}