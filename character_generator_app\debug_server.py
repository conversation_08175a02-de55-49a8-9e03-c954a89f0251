#!/usr/bin/env python3
"""
Debug server to identify the issue
"""

print("🔍 Starting debug server...")

try:
    print("1. Importing Flask...")
    from flask import Flask
    print("   ✅ Flask imported")
    
    print("2. Creating Flask app...")
    app = Flask(__name__)
    print("   ✅ Flask app created")
    
    print("3. Adding route...")
    @app.route('/')
    def hello():
        return "<h1>🎉 Server is working!</h1><p>Flask server is running successfully.</p>"
    print("   ✅ Route added")
    
    print("4. Starting server...")
    print("   🌐 Opening: http://localhost:5000")
    print("   ⚠️  If you see this message, the server is starting...")
    
    app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
