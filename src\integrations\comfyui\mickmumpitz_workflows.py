"""
Integration module for mickmumpitz's ComfyUI workflows.

This module provides functionality to use mickmumpitz's workflows for:
1. 3D animation rendering
2. Consistent character generation
3. Multiple character control

References:
- 3D Animation Rendering: https://www.patreon.com/posts/free-workflows-104795004
- Character Generation: https://www.patreon.com/posts/free-workflows-113743435
"""

import os
import json
import subprocess
import tempfile
import shutil
import requests
from pathlib import Path

class MickmumpitzWorkflows:
    """
    Class for integrating mickmumpitz's ComfyUI workflows.
    """
    
    def __init__(self, comfyui_path=None, workflows_dir=None):
        """
        Initialize the mickmumpitz workflows integration.
        
        Args:
            comfyui_path: Path to ComfyUI installation (default: from environment variable)
            workflows_dir: Directory to store workflow files (default: 'workflows' in current directory)
        """
        self.comfyui_path = comfyui_path or os.environ.get('COMFYUI_PATH', 'external/ComfyUI')
        self.workflows_dir = workflows_dir or 'workflows/mickmumpitz'
        
        # Create workflows directory if it doesn't exist
        os.makedirs(self.workflows_dir, exist_ok=True)
        
        # Define workflow file paths
        self.workflow_paths = {
            '3d_rendering_image': os.path.join(self.workflows_dir, '200524_Mickmumpitz_3D-RENDERING_IMG_SDXL_SIMPLE_v3.json'),
            '3d_rendering_video': os.path.join(self.workflows_dir, '200524_Mickmumpitz_3D-RENDERING_VIDEO_SD15LCM_SIMPLE_v3.json'),
            'character_sheet': os.path.join(self.workflows_dir, '241027_MICKMUMPITZ_CHARACTER_SHEET_V05_FLUX_SMPL.json'),
            'flux_lora': os.path.join(self.workflows_dir, '241007_MICKMUMPITZ_FLUX+LORA.json')
        }
        
        # Check if workflows are downloaded
        self._check_workflows()
    
    def _check_workflows(self):
        """Check if workflow files exist and download them if needed."""
        missing_workflows = []
        
        for name, path in self.workflow_paths.items():
            if not os.path.exists(path):
                missing_workflows.append(name)
        
        if missing_workflows:
            print(f"Missing workflows: {missing_workflows}")
            print("Please download the workflow files from mickmumpitz's Patreon:")
            print("- 3D Animation Rendering: https://www.patreon.com/posts/free-workflows-104795004")
            print("- Character Generation: https://www.patreon.com/posts/free-workflows-113743435")
            print(f"And place them in the {self.workflows_dir} directory with the correct filenames.")
    
    def setup_required_nodes(self):
        """
        Set up the required ComfyUI custom nodes for mickmumpitz's workflows.
        
        This function installs the necessary custom nodes based on mickmumpitz's installation guides.
        """
        # Create custom_nodes directory if it doesn't exist
        custom_nodes_dir = os.path.join(self.comfyui_path, 'custom_nodes')
        os.makedirs(custom_nodes_dir, exist_ok=True)
        
        # List of required custom nodes repositories
        required_nodes = [
            # For 3D rendering workflow
            {'repo': 'https://github.com/Fannovel16/comfyui_controlnet_aux.git', 'name': 'comfyui_controlnet_aux'},
            {'repo': 'https://github.com/Kosinkadink/ComfyUI-AnimateDiff-Evolved.git', 'name': 'ComfyUI-AnimateDiff-Evolved'},
            {'repo': 'https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git', 'name': 'ComfyUI-VideoHelperSuite'},
            {'repo': 'https://github.com/cubiq/ComfyUI_IPAdapter_plus.git', 'name': 'ComfyUI_IPAdapter_plus'},
            
            # For character generation workflow
            {'repo': 'https://github.com/ssitu/ComfyUI_UltimateSDUpscale.git', 'name': 'ComfyUI_UltimateSDUpscale'},
            {'repo': 'https://github.com/ltdrdata/ComfyUI-Manager.git', 'name': 'ComfyUI-Manager'},
            {'repo': 'https://github.com/rgthree/rgthree-comfy.git', 'name': 'rgthree-comfy'},
            {'repo': 'https://github.com/coreyryanhanson/ComfyQR.git', 'name': 'ComfyQR'},
            {'repo': 'https://github.com/kijai/ComfyUI-KJNodes.git', 'name': 'ComfyUI-KJNodes'},
            {'repo': 'https://github.com/WASasquatch/was-node-suite-comfyui.git', 'name': 'was-node-suite-comfyui'},
            {'repo': 'https://github.com/Gourieff/comfyui-reactor-node.git', 'name': 'comfyui-reactor-node'},
            {'repo': 'https://github.com/ZHO-ZHO-ZHO/ComfyUI-Flowty-LDSR.git', 'name': 'ComfyUI-Flowty-LDSR'},
            {'repo': 'https://github.com/Kosinkadink/ComfyUI-Advanced-ControlNet.git', 'name': 'ComfyUI-Advanced-ControlNet'},
            {'repo': 'https://github.com/Acly/comfyui-inpaint-nodes.git', 'name': 'comfyui-inpaint-nodes'},
            {'repo': 'https://github.com/Fannovel16/ComfyUI-Frame-Interpolation.git', 'name': 'ComfyUI-Frame-Interpolation'},
            {'repo': 'https://github.com/Kosinkadink/ComfyUI-FluxNodes.git', 'name': 'ComfyUI-FluxNodes'}
        ]
        
        # Clone or update each repository
        for node in required_nodes:
            node_path = os.path.join(custom_nodes_dir, node['name'])
            
            if os.path.exists(node_path):
                print(f"Updating {node['name']}...")
                try:
                    subprocess.run(['git', 'pull'], cwd=node_path, check=True)
                except subprocess.CalledProcessError:
                    print(f"Failed to update {node['name']}. Skipping.")
            else:
                print(f"Cloning {node['name']}...")
                try:
                    subprocess.run(['git', 'clone', node['repo'], node_path], check=True)
                except subprocess.CalledProcessError:
                    print(f"Failed to clone {node['name']}. Skipping.")
        
        print("Custom nodes setup complete. Please restart ComfyUI to load the new nodes.")
    
    def render_3d_animation(self, blender_output_dir, output_dir=None, use_video=True):
        """
        Render a 3D animation using mickmumpitz's workflow.
        
        Args:
            blender_output_dir: Directory containing Blender output files (depth, mask, outline)
            output_dir: Directory to save the rendered output (default: 'output/animations')
            use_video: Whether to use the video workflow (True) or image workflow (False)
            
        Returns:
            str: Path to the rendered output
        """
        if output_dir is None:
            output_dir = 'output/animations'
        os.makedirs(output_dir, exist_ok=True)
        
        # Check if required files exist
        required_dirs = ['depth', 'mask', 'outline']
        for dir_name in required_dirs:
            dir_path = os.path.join(blender_output_dir, dir_name)
            if not os.path.exists(dir_path):
                raise ValueError(f"Missing required directory: {dir_path}")
        
        # Select the appropriate workflow
        workflow_path = self.workflow_paths['3d_rendering_video'] if use_video else self.workflow_paths['3d_rendering_image']
        
        if not os.path.exists(workflow_path):
            raise ValueError(f"Workflow file not found: {workflow_path}. Please download it from mickmumpitz's Patreon.")
        
        # Load the workflow
        with open(workflow_path, 'r') as f:
            workflow = json.load(f)
        
        # Update the workflow with the correct paths
        # This would need to be customized based on the specific workflow structure
        # For now, we'll just print instructions
        print(f"To use this workflow:")
        print(f"1. Open ComfyUI and load the workflow: {workflow_path}")
        print(f"2. Update the input paths to point to:")
        print(f"   - Depth maps: {os.path.join(blender_output_dir, 'depth')}")
        print(f"   - Mask maps: {os.path.join(blender_output_dir, 'mask')}")
        print(f"   - Outline maps: {os.path.join(blender_output_dir, 'outline')}")
        print(f"3. Update the output path to: {output_dir}")
        print(f"4. Run the workflow")
        
        return output_dir
    
    def generate_character_sheet(self, character_prompt, output_dir=None):
        """
        Generate a character sheet using mickmumpitz's workflow.
        
        Args:
            character_prompt: Text prompt describing the character
            output_dir: Directory to save the character sheet (default: 'output/characters')
            
        Returns:
            str: Path to the generated character sheet
        """
        if output_dir is None:
            output_dir = 'output/characters'
        os.makedirs(output_dir, exist_ok=True)
        
        # Check if workflow file exists
        workflow_path = self.workflow_paths['character_sheet']
        if not os.path.exists(workflow_path):
            raise ValueError(f"Workflow file not found: {workflow_path}. Please download it from mickmumpitz's Patreon.")
        
        # Load the workflow
        with open(workflow_path, 'r') as f:
            workflow = json.load(f)
        
        # Print instructions for now
        print(f"To generate a character sheet:")
        print(f"1. Open ComfyUI and load the workflow: {workflow_path}")
        print(f"2. Update the character prompt to: {character_prompt}")
        print(f"3. Update the output path to: {output_dir}")
        print(f"4. Run the workflow")
        
        return output_dir
    
    def prepare_blender_outputs(self, blender_scene_path, output_dir=None):
        """
        Prepare the necessary outputs from Blender for the 3D rendering workflow.
        
        This function provides instructions on how to set up Blender to generate
        the required depth maps, mask maps, and outline maps.
        
        Args:
            blender_scene_path: Path to the Blender scene file
            output_dir: Directory to save the Blender outputs (default: 'output/blender')
            
        Returns:
            str: Path to the output directory
        """
        if output_dir is None:
            output_dir = 'output/blender'
        
        # Create output directories
        os.makedirs(os.path.join(output_dir, 'depth'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'mask'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'outline'), exist_ok=True)
        
        # Print instructions for setting up Blender
        print("To prepare Blender outputs for the 3D rendering workflow:")
        print("1. Open your Blender scene")
        print("2. Set up the following render passes:")
        print("   a. Depth Pass:")
        print("      - Go to View Layer Properties")
        print("      - Activate the Z pass")
        print("      - In the Compositing tab, connect a viewer node to the depth output")
        print("      - Normalize the depth values using a Map Range node")
        print("      - Save the output to:", os.path.join(output_dir, 'depth'))
        print("   b. Mask Pass:")
        print("      - Assign simple emission shaders with distinct colors to each object")
        print("      - Note the Hex Codes for these colors")
        print("      - Save the output to:", os.path.join(output_dir, 'mask'))
        print("   c. Outline Pass:")
        print("      - Use the Freestyle tool to create outlines")
        print("      - Set the color to white and adjust line thickness")
        print("      - Save the output to:", os.path.join(output_dir, 'outline'))
        print("3. Render the animation or still image with these passes")
        
        return output_dir
