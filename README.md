# AI Content Generation Pipeline

A comprehensive system for automatically creating 3D models, videos, images, characters, and video games from various inputs such as images, text prompts, 3D files, or videos.

## Overview

This project integrates multiple AI and content creation tools into a unified pipeline:

- **ComfyUI**: For image generation and manipulation
- **Hugging Face**: For various AI models (text-to-image, image-to-3D, etc.)
- **Blender**: For 3D model processing and optimization
- **Unity/Unreal Engine**: For game asset integration and game creation
- **GitHub**: For version control and collaboration
- **mickmumpitz's Workflows**: For enhanced 3D rendering and character generation

## Features

- Convert images to 3D models
- Generate 3D models from text descriptions
- Create images from text prompts
- Convert images to videos
- Generate complete video games from text descriptions or assets
- Process and optimize 3D models for various use cases
- Web-based interface for easy interaction
- Create custom ComfyUI workflows programmatically
- Directly edit 3D models in Blender
- Directly edit and create content in Unreal Engine
- Integrated workflow across ComfyUI, Blender, and Unreal Engine

## Prerequisites

- Python 3.8+
- Flask
- ComfyUI
- Blender
- Unity or Unreal Engine
- Hugging Face account with API token

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/ai-content-pipeline.git
   cd ai-content-pipeline
   ```

2. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```
   HUGGINGFACE_API_TOKEN=your_token_here
   COMFYUI_API_URL=http://localhost:8188
   BLENDER_PATH=path_to_blender_executable
   UNITY_PATH=path_to_unity_executable
   UNITY_PROJECT_PATH=path_to_unity_project
   ```

4. Install and configure external tools:
   - ComfyUI: Follow installation instructions at [ComfyUI GitHub](https://github.com/comfyanonymous/ComfyUI)
   - Blender: Download from [Blender.org](https://www.blender.org/download/)
   - Unity: Download from [Unity.com](https://unity.com/download)
   - Unreal Engine: Download from [UnrealEngine.com](https://www.unrealengine.com/download)

## Usage

1. Start the Flask server:
   ```
   python src/app.py
   ```

2. Open your web browser and navigate to:
   ```
   http://localhost:5000
   ```

3. Use the web interface to:
   - Upload input files (images, 3D models, videos)
   - Enter text prompts
   - Select input and output types
   - Configure generation parameters
   - Generate content
   - Download or share results

## Pipeline Workflows

### Image to 3D Model
1. Upload an image
2. Select "Image" as input type and "3D Model" as output type
3. Choose model and quality settings
4. Generate the 3D model
5. Download or view the result

### Text to Game
1. Enter a text description
2. Select "Text" as input type and "Video Game" as output type
3. Configure game settings
4. Generate the game
5. Download the game files

### Image to Video
1. Upload an image
2. Select "Image" as input type and "Video" as output type
3. Configure video settings
4. Generate the video
5. Download or share the result

## Architecture

The system is built with a modular architecture:

- **Frontend**: Web interface for user interaction
- **Backend**: Flask server for API endpoints and job management
- **Pipeline Manager**: Coordinates different processing pipelines
- **Integration Modules**: Connect to external tools (ComfyUI, Hugging Face, Blender, Unity)
- **Processing Pipelines**: Specialized workflows for different conversion tasks

## Extending the System

To add new pipelines or integrations:

1. Create a new integration module in `src/integrations/`
2. Create a new pipeline module in `src/pipelines/`
3. Register the pipeline in `PipelineManager`
4. Update the frontend to support the new pipeline

## mickmumpitz's ComfyUI Workflows

This project includes integration with mickmumpitz's ComfyUI workflows for enhanced 3D rendering and character generation. These workflows provide powerful tools for:

1. **3D Animation Rendering**: Convert Blender scenes to AI-rendered animations
2. **Consistent Character Generation**: Create character sheets with consistent appearances
3. **Multiple Character Control**: Control multiple characters and camera movements

### Getting Started with mickmumpitz's Workflows

1. Run the download script to get the workflow files:
   ```
   python src/scripts/download_mickmumpitz_workflows.py
   ```

2. Follow the installation guides to set up the required ComfyUI nodes.

3. Try the demo script to see the workflows in action:
   ```
   python src/examples/mickmumpitz_workflow_demo.py --setup
   python src/examples/mickmumpitz_workflow_demo.py --render
   ```

For more information, see the [mickmumpitz workflows README](workflows/mickmumpitz/README.md).

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [ComfyUI](https://github.com/comfyanonymous/ComfyUI) for the node-based interface
- [Hugging Face](https://huggingface.co/) for AI models
- [Blender](https://www.blender.org/) for 3D processing capabilities
- [Unity](https://unity.com/) and [Unreal Engine](https://www.unrealengine.com/) for game creation
- [mickmumpitz](https://www.patreon.com/Mickmumpitz) for the amazing ComfyUI workflows

## Custom Workflows and Direct Editing

For information on how to create custom workflows and directly edit content in Blender and Unreal Engine, see [CUSTOM_WORKFLOWS.md](CUSTOM_WORKFLOWS.md).
