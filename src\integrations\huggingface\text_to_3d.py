"""
Hugging Face integration for text-to-3D conversion.
"""

import os
import requests
import json
import tempfile
import time
from PIL import Image
import io
import base64

class HuggingFaceTextTo3D:
    """
    Client for Hugging Face text-to-3D models.
    
    This class provides methods to interact with various Hugging Face models
    that can convert text prompts to 3D models.
    """
    
    def __init__(self, api_token=None):
        """
        Initialize the Hugging Face client.
        
        Args:
            api_token: Hugging Face API token (default: from environment variable)
        """
        self.api_token = api_token or os.environ.get('HUGGINGFACE_API_TOKEN')
        self.api_url = "https://api-inference.huggingface.co/models"
        
        # Available models for text-to-3D conversion
        self.available_models = {
            'shap-e': 'openai/shap-e',
            'dreamfusion': 'dreamfusion/dreamfusion-v1',
            'magic3d': 'bytedance/magic3d',
            'threestudio': 'threestudio-project/threestudio',
            'prolificdreamer': 'ashawkey/prolificdreamer'
        }
    
    def generate_mesh_from_text(self, text_prompt, model='shap-e', output_dir=None):
        """
        Generate a 3D mesh from a text prompt using a Hugging Face model.
        
        Args:
            text_prompt: Text prompt describing the 3D model
            model: Model to use for 3D generation (default: 'shap-e')
            output_dir: Directory to save the output (default: temporary directory)
            
        Returns:
            str: Path to the generated 3D model file
        """
        if model not in self.available_models:
            raise ValueError(f"Model {model} not available. Choose from: {list(self.available_models.keys())}")
        
        model_id = self.available_models[model]
        
        # Create output directory if not provided
        if output_dir is None:
            output_dir = tempfile.mkdtemp()
        os.makedirs(output_dir, exist_ok=True)
        
        # Prepare headers
        headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
        
        # Prepare payload
        payload = {
            "inputs": text_prompt
        }
        
        # Make API request
        print(f"Sending request to Hugging Face model: {model_id}")
        response = requests.post(
            f"{self.api_url}/{model_id}",
            headers=headers,
            json=payload
        )
        
        # Check for errors
        if response.status_code != 200:
            raise Exception(f"Error from Hugging Face API: {response.text}")
        
        # Save the response (which should be a 3D model file)
        output_path = os.path.join(output_dir, f"model_{int(time.time())}.glb")
        
        # The response format depends on the model
        # For some models, it might be a direct binary response
        # For others, it might be a JSON with a URL to download the model
        
        if response.headers.get('Content-Type') == 'application/json':
            # Handle JSON response
            data = response.json()
            if 'download_url' in data:
                # Download the model from the provided URL
                model_response = requests.get(data['download_url'])
                with open(output_path, 'wb') as f:
                    f.write(model_response.content)
            else:
                # Handle other JSON response formats
                # This is model-specific and would need to be implemented
                # based on the actual response format
                raise NotImplementedError(f"JSON response format not implemented for model {model}")
        else:
            # Handle direct binary response
            with open(output_path, 'wb') as f:
                f.write(response.content)
        
        print(f"3D model saved to: {output_path}")
        return output_path
    
    def list_available_models(self):
        """
        List available models for text-to-3D conversion.
        
        Returns:
            dict: Dictionary of available models
        """
        return self.available_models
