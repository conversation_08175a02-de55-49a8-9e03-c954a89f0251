{"1": {"inputs": {"text": "masterpiece, best quality, highly detailed 3D character, professional modeling, realistic proportions", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Enhanced Positive Prompt"}}, "2": {"inputs": {"text": "worst quality, low quality, normal quality, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, watermark, username, blurry", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Enhanced Negative Prompt"}}, "3": {"inputs": {"seed": 42, "steps": 30, "cfg": 8.5, "sampler_name": "dpmpp_2m_sde", "scheduler": "karras", "denoise": 1.0, "model": ["4", 0], "positive": ["1", 0], "negative": ["2", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "<PERSON>han<PERSON>"}}, "4": {"inputs": {"ckpt_name": "realisticVisionV60B1_v51VAE.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Realistic Vision Model"}}, "5": {"inputs": {"width": 768, "height": 768, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "High Resolution Latent"}}, "6": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "7": {"inputs": {"upscale_method": "nearest-exact", "width": 1024, "height": 1024, "crop": "disabled", "image": ["6", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "8": {"inputs": {"image": ["7", 0], "control_net_name": "control_v11p_sd15_normalbae_fp16.pth"}, "class_type": "ControlNetPreprocessor", "_meta": {"title": "Normal Map Generation"}}, "9": {"inputs": {"image": ["7", 0], "depth_estimation_method": "midas", "remove_background": true, "background_threshold": 0.1}, "class_type": "DepthEstimation", "_meta": {"title": "Enhanced Depth Estimation"}}, "10": {"inputs": {"image": ["7", 0], "depth": ["9", 0], "normal": ["8", 0], "mesh_resolution": 512, "texture_resolution": 1024, "smoothing_iterations": 3, "subdivision_levels": 2}, "class_type": "EnhancedImageTo3D", "_meta": {"title": "Enhanced Image to 3D"}}, "11": {"inputs": {"mesh": ["10", 0], "optimization_method": "quadric_decimation", "target_faces": 5000, "preserve_boundaries": true}, "class_type": "MeshOptimization", "_meta": {"title": "Mesh Optimization"}}, "12": {"inputs": {"mesh": ["11", 0], "texture_size": 1024, "bake_normal_maps": true, "bake_ambient_occlusion": true, "material_type": "PBR"}, "class_type": "TextureBaking", "_meta": {"title": "PBR Texture Baking"}}, "13": {"inputs": {"mesh": ["12", 0], "output_format": "glb", "include_animations": false, "compression": "draco", "filename_prefix": "enhanced_character_3d"}, "class_type": "Save3DModel", "_meta": {"title": "Save Enhanced 3D Model"}}, "14": {"inputs": {"filename_prefix": "enhanced_character_2d", "images": ["7", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Enhanced Image"}}}