#!/usr/bin/env python3
"""
Test face matching character generation
"""

import sys
import os
import uuid
from datetime import datetime

# Add backend to path
sys.path.append('backend')

def test_face_matching_system():
    """Test the complete face matching system"""
    print("🎭 Testing Face Matching Character Generation")
    print("=" * 45)
    
    # Test image paths (you can replace these with actual image paths)
    test_images = [
        "test_face_1.jpg",  # Replace with actual image path
        "test_face_2.png",  # Replace with actual image path
    ]
    
    # Create test images if they don't exist
    create_test_images()
    
    try:
        # Test face processor
        print("\n🔍 Testing Face Processor...")
        from face_processor import FaceProcessor
        
        face_processor = FaceProcessor()
        print("✅ Face processor initialized")
        
        # Test face-to-character mapper
        print("\n🎨 Testing Face-to-Character Mapper...")
        from face_to_character import FaceToCharacterMapper
        
        mapper = FaceToCharacterMapper()
        print("✅ Face mapper initialized")
        
        # Test with a sample image (create a simple test image)
        test_image_path = create_sample_test_image()
        
        if os.path.exists(test_image_path):
            print(f"\n📸 Testing with image: {test_image_path}")
            
            # Test face feature extraction
            face_features = face_processor.extract_face_features(test_image_path)
            print("✅ Face features extracted")
            print(f"   Face detected: {not face_features.get('analysis_failed', False)}")
            print(f"   Skin tone: {face_features.get('skin_tone', {}).get('hex', 'N/A')}")
            
            # Test character mapping
            character_params = mapper.map_face_to_character(test_image_path, "A fantasy character")
            print("✅ Character parameters generated")
            print(f"   Face matched: {character_params.get('face_matched', False)}")
            print(f"   Character type: {character_params.get('name', 'unknown')}")
            
            # Test GLB generation with face matching
            print("\n🎮 Testing Face-Matched GLB Generation...")
            from app import create_face_matched_character_glb
            
            glb_content, char_params = create_face_matched_character_glb(
                test_image_path, 
                "A realistic character with this face"
            )
            
            if glb_content:
                # Save the face-matched character
                job_id = str(uuid.uuid4())[:8]
                filename = f"face_matched_character_{job_id}.glb"
                
                os.makedirs('backend/outputs/models', exist_ok=True)
                filepath = os.path.join('backend', 'outputs', 'models', filename)
                
                with open(filepath, 'wb') as f:
                    f.write(glb_content)
                
                print(f"✅ Face-matched GLB generated: {filename}")
                print(f"   File size: {len(glb_content):,} bytes")
                print(f"   Face matching: {'✅ Success' if char_params and char_params.get('face_matched') else '⚠️ Fallback'}")
                
                return True
            else:
                print("❌ GLB generation failed")
                return False
        else:
            print("⚠️ No test image available, testing with fallback")
            
            # Test without image (fallback mode)
            character_params = mapper.create_fallback_character("A test character")
            print("✅ Fallback character parameters generated")
            
            return True
            
    except Exception as e:
        print(f"❌ Face matching test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_test_image():
    """Create a simple test image for testing"""
    try:
        from PIL import Image, ImageDraw
        import numpy as np
        
        # Create a simple face-like image
        width, height = 200, 200
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # Draw a simple face
        # Head (circle)
        draw.ellipse([50, 50, 150, 150], fill='#f4c2a1', outline='#d4a574')
        
        # Eyes
        draw.ellipse([70, 80, 85, 95], fill='white', outline='black')
        draw.ellipse([115, 80, 130, 95], fill='white', outline='black')
        draw.ellipse([75, 85, 80, 90], fill='black')  # Left pupil
        draw.ellipse([120, 85, 125, 90], fill='black')  # Right pupil
        
        # Nose
        draw.polygon([(100, 95), (95, 110), (105, 110)], fill='#e6b896')
        
        # Mouth
        draw.arc([85, 115, 115, 135], 0, 180, fill='#d4a574', width=2)
        
        # Save test image
        test_image_path = 'test_face_sample.png'
        image.save(test_image_path)
        
        print(f"✅ Created test image: {test_image_path}")
        return test_image_path
        
    except ImportError:
        print("⚠️ PIL not available, creating placeholder")
        # Create a placeholder file
        test_image_path = 'test_face_placeholder.txt'
        with open(test_image_path, 'w') as f:
            f.write("Test face image placeholder")
        return test_image_path
    except Exception as e:
        print(f"⚠️ Could not create test image: {e}")
        return None

def create_test_images():
    """Create or check for test images"""
    print("📁 Checking for test images...")
    
    # Check if we have any image files in the directory
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(file)
    
    if image_files:
        print(f"✅ Found {len(image_files)} image files: {image_files[:3]}...")
    else:
        print("⚠️ No image files found, will create test image")

def test_opencv_installation():
    """Test if OpenCV is properly installed"""
    print("\n🔍 Testing OpenCV Installation...")
    
    try:
        import cv2
        print(f"✅ OpenCV version: {cv2.__version__}")
        
        # Test cascade loading
        cascade_path = cv2.data.haarcascades
        face_cascade_path = os.path.join(cascade_path, 'haarcascade_frontalface_default.xml')
        
        if os.path.exists(face_cascade_path):
            print("✅ Face detection cascades available")
            
            face_cascade = cv2.CascadeClassifier(face_cascade_path)
            if not face_cascade.empty():
                print("✅ Face cascade loaded successfully")
                return True
            else:
                print("⚠️ Face cascade failed to load")
                return False
        else:
            print("⚠️ Face detection cascades not found")
            return False
            
    except ImportError:
        print("❌ OpenCV not installed")
        print("   Install with: pip install opencv-python")
        return False
    except Exception as e:
        print(f"⚠️ OpenCV test error: {e}")
        return False

def main():
    """Main test function"""
    print("🎭 Face Matching System Test Suite")
    print("=" * 35)
    
    # Test OpenCV
    opencv_ok = test_opencv_installation()
    
    # Test face matching system
    face_matching_ok = test_face_matching_system()
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 25)
    print(f"OpenCV installation: {'✅' if opencv_ok else '❌'}")
    print(f"Face matching system: {'✅' if face_matching_ok else '❌'}")
    
    if opencv_ok and face_matching_ok:
        print(f"\n🎉 Face matching system is working!")
        print(f"📁 Check backend/outputs/models/ for generated characters")
        print(f"🌐 Use face_matching_interface.html to test the web interface")
    else:
        print(f"\n⚠️ Some components need attention:")
        if not opencv_ok:
            print("   - Install OpenCV: pip install opencv-python")
        if not face_matching_ok:
            print("   - Check face processing modules")
    
    print(f"\n💡 Next Steps:")
    print("1. Upload a real face image to test with")
    print("2. Open face_matching_interface.html in your browser")
    print("3. Test the complete face-to-character pipeline")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
