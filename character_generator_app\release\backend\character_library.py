#!/usr/bin/env python3
"""
Character library system for organizing and managing generated characters
"""

import os
import json
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path
import hashlib

class CharacterLibrary:
    """Manage a library of generated 3D characters"""
    
    def __init__(self, library_path="character_library"):
        self.library_path = library_path
        self.db_path = os.path.join(library_path, "character_library.db")
        
        # Library structure
        self.directories = {
            "models": os.path.join(library_path, "models"),
            "thumbnails": os.path.join(library_path, "thumbnails"),
            "source_images": os.path.join(library_path, "source_images"),
            "analysis": os.path.join(library_path, "analysis"),
            "assets": os.path.join(library_path, "assets"),
            "collections": os.path.join(library_path, "collections"),
            "exports": os.path.join(library_path, "exports")
        }
        
        # Character categories
        self.categories = {
            "realistic": "Photorealistic human characters",
            "anime": "Anime and manga style characters",
            "cartoon": "Cartoon and stylized characters",
            "fantasy": "Fantasy and mythical characters",
            "sci_fi": "Science fiction and futuristic characters",
            "historical": "Historical and period characters",
            "creature": "Non-human creatures and monsters",
            "robot": "Robotic and mechanical characters"
        }
        
        # Quality levels
        self.quality_levels = {
            "low": "Basic quality for prototyping",
            "medium": "Standard quality for most uses",
            "high": "High quality for professional use",
            "ultra": "Ultra quality for film and showcase"
        }
        
        self.setup_library()
    
    def setup_library(self):
        """Setup library directories and database"""
        # Create directories
        for name, path in self.directories.items():
            os.makedirs(path, exist_ok=True)
        
        # Setup database
        self.setup_database()
        
        print(f"✅ Character library ready: {self.library_path}")
    
    def setup_database(self):
        """Setup SQLite database for character metadata"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Characters table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS characters (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    character_id TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    category TEXT,
                    art_style TEXT,
                    quality_level TEXT,
                    use_case TEXT,
                    created_date TEXT,
                    source_image_path TEXT,
                    glb_file_path TEXT,
                    thumbnail_path TEXT,
                    file_size INTEGER,
                    vertex_count INTEGER,
                    face_count INTEGER,
                    face_matched BOOLEAN,
                    ai_enhanced BOOLEAN,
                    tags TEXT,
                    metadata_json TEXT
                )
            ''')
            
            # Collections table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS collections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    collection_name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    created_date TEXT,
                    character_count INTEGER DEFAULT 0
                )
            ''')
            
            # Character-Collection mapping
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS character_collections (
                    character_id TEXT,
                    collection_name TEXT,
                    added_date TEXT,
                    PRIMARY KEY (character_id, collection_name)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            print(f"✅ Database initialized: {self.db_path}")
            
        except Exception as e:
            print(f"❌ Database setup failed: {e}")
    
    def add_character(self, character_result, source_image_path=None, category="realistic", 
                     art_style="realistic", use_case="general", tags=None):
        """Add a character to the library"""
        try:
            if not character_result or not character_result.get('success'):
                print("❌ Invalid character result")
                return None
            
            # Generate unique character ID
            character_id = self.generate_character_id(character_result)
            
            print(f"📚 Adding character to library: {character_id}")
            
            # Copy files to library
            library_files = self.copy_character_files(character_result, character_id)
            
            # Generate thumbnail
            thumbnail_path = self.generate_thumbnail(library_files['glb_path'], character_id)
            
            # Extract metadata
            metadata = self.extract_character_metadata(character_result)
            
            # Add to database
            self.add_to_database(
                character_id=character_id,
                name=character_result.get('character_params', {}).get('name', 'Unnamed Character'),
                description=character_result.get('character_params', {}).get('description', ''),
                category=category,
                art_style=art_style,
                quality_level=character_result.get('character_params', {}).get('quality', 'medium'),
                use_case=use_case,
                source_image_path=library_files.get('source_image_path'),
                glb_file_path=library_files['glb_path'],
                thumbnail_path=thumbnail_path,
                file_size=character_result.get('file_size', 0),
                vertex_count=character_result.get('vertex_count', 0),
                face_count=character_result.get('face_count', 0),
                face_matched=character_result.get('character_params', {}).get('face_matched', False),
                ai_enhanced=character_result.get('ai_enhanced', False),
                tags=','.join(tags) if tags else '',
                metadata_json=json.dumps(metadata)
            )
            
            print(f"✅ Character added to library: {character_id}")
            return character_id
            
        except Exception as e:
            print(f"❌ Failed to add character to library: {e}")
            return None
    
    def generate_character_id(self, character_result):
        """Generate unique character ID"""
        # Use file content hash for uniqueness
        glb_file = character_result.get('glb_path', '')
        if os.path.exists(glb_file):
            with open(glb_file, 'rb') as f:
                content_hash = hashlib.md5(f.read()).hexdigest()[:8]
        else:
            content_hash = hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"char_{timestamp}_{content_hash}"
    
    def copy_character_files(self, character_result, character_id):
        """Copy character files to library"""
        try:
            library_files = {}
            
            # Copy GLB file
            source_glb = character_result.get('glb_path', '')
            if os.path.exists(source_glb):
                glb_filename = f"{character_id}.glb"
                glb_dest = os.path.join(self.directories['models'], glb_filename)
                shutil.copy2(source_glb, glb_dest)
                library_files['glb_path'] = glb_dest
                print(f"   📦 GLB copied: {glb_filename}")
            
            # Copy source image if available
            source_image = character_result.get('source_image', '')
            if source_image and os.path.exists(source_image):
                image_ext = Path(source_image).suffix
                image_filename = f"{character_id}_source{image_ext}"
                image_dest = os.path.join(self.directories['source_images'], image_filename)
                shutil.copy2(source_image, image_dest)
                library_files['source_image_path'] = image_dest
                print(f"   📸 Source image copied: {image_filename}")
            
            # Copy analysis file if available
            analysis_file = character_result.get('analysis_path', '')
            if analysis_file and os.path.exists(analysis_file):
                analysis_filename = f"{character_id}_analysis.json"
                analysis_dest = os.path.join(self.directories['analysis'], analysis_filename)
                shutil.copy2(analysis_file, analysis_dest)
                library_files['analysis_path'] = analysis_dest
                print(f"   📊 Analysis copied: {analysis_filename}")
            
            # Copy assets directory if available
            assets_dir = character_result.get('assets_directory', '')
            if assets_dir and os.path.exists(assets_dir):
                assets_dest = os.path.join(self.directories['assets'], character_id)
                shutil.copytree(assets_dir, assets_dest, dirs_exist_ok=True)
                library_files['assets_path'] = assets_dest
                print(f"   🎨 Assets copied: {character_id}/")
            
            return library_files
            
        except Exception as e:
            print(f"❌ Failed to copy character files: {e}")
            return {}
    
    def generate_thumbnail(self, glb_path, character_id):
        """Generate thumbnail for character (placeholder implementation)"""
        try:
            # This would ideally render the GLB file to create a thumbnail
            # For now, create a placeholder
            thumbnail_filename = f"{character_id}_thumb.png"
            thumbnail_path = os.path.join(self.directories['thumbnails'], thumbnail_filename)
            
            # Create a simple placeholder thumbnail
            try:
                from PIL import Image, ImageDraw
                
                img = Image.new('RGB', (256, 256), color='lightgray')
                draw = ImageDraw.Draw(img)
                draw.text((10, 10), f"Character\n{character_id}", fill='black')
                img.save(thumbnail_path)
                
                print(f"   🖼️ Thumbnail created: {thumbnail_filename}")
                return thumbnail_path
                
            except ImportError:
                # Create empty file as placeholder
                with open(thumbnail_path, 'w') as f:
                    f.write('')
                return thumbnail_path
            
        except Exception as e:
            print(f"❌ Thumbnail generation failed: {e}")
            return None
    
    def extract_character_metadata(self, character_result):
        """Extract comprehensive metadata from character result"""
        return {
            "generation_info": character_result.get('character_params', {}).get('generation_info', {}),
            "face_analysis": character_result.get('face_analysis', {}),
            "enhancement_info": character_result.get('enhancement_method', 'basic'),
            "workflow_version": "2.0",
            "library_added": datetime.now().isoformat()
        }
    
    def add_to_database(self, **kwargs):
        """Add character record to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO characters (
                    character_id, name, description, category, art_style, quality_level,
                    use_case, created_date, source_image_path, glb_file_path, thumbnail_path,
                    file_size, vertex_count, face_count, face_matched, ai_enhanced, tags, metadata_json
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                kwargs['character_id'], kwargs['name'], kwargs['description'],
                kwargs['category'], kwargs['art_style'], kwargs['quality_level'],
                kwargs['use_case'], datetime.now().isoformat(),
                kwargs.get('source_image_path'), kwargs['glb_file_path'],
                kwargs.get('thumbnail_path'), kwargs['file_size'],
                kwargs['vertex_count'], kwargs['face_count'],
                kwargs['face_matched'], kwargs['ai_enhanced'],
                kwargs['tags'], kwargs['metadata_json']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ Database insertion failed: {e}")
    
    def search_characters(self, query=None, category=None, art_style=None, 
                         face_matched=None, ai_enhanced=None, tags=None):
        """Search characters in the library"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Build query
            where_conditions = []
            params = []
            
            if query:
                where_conditions.append("(name LIKE ? OR description LIKE ?)")
                params.extend([f"%{query}%", f"%{query}%"])
            
            if category:
                where_conditions.append("category = ?")
                params.append(category)
            
            if art_style:
                where_conditions.append("art_style = ?")
                params.append(art_style)
            
            if face_matched is not None:
                where_conditions.append("face_matched = ?")
                params.append(face_matched)
            
            if ai_enhanced is not None:
                where_conditions.append("ai_enhanced = ?")
                params.append(ai_enhanced)
            
            if tags:
                for tag in tags:
                    where_conditions.append("tags LIKE ?")
                    params.append(f"%{tag}%")
            
            # Construct SQL
            sql = "SELECT * FROM characters"
            if where_conditions:
                sql += " WHERE " + " AND ".join(where_conditions)
            sql += " ORDER BY created_date DESC"
            
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            # Convert to dictionaries
            columns = [description[0] for description in cursor.description]
            characters = [dict(zip(columns, row)) for row in results]
            
            conn.close()
            
            print(f"🔍 Found {len(characters)} characters")
            return characters
            
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return []
    
    def create_collection(self, collection_name, description="", character_ids=None):
        """Create a new character collection"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create collection
            cursor.execute('''
                INSERT INTO collections (collection_name, description, created_date, character_count)
                VALUES (?, ?, ?, ?)
            ''', (collection_name, description, datetime.now().isoformat(), 0))
            
            # Add characters to collection
            if character_ids:
                for character_id in character_ids:
                    cursor.execute('''
                        INSERT INTO character_collections (character_id, collection_name, added_date)
                        VALUES (?, ?, ?)
                    ''', (character_id, collection_name, datetime.now().isoformat()))
                
                # Update character count
                cursor.execute('''
                    UPDATE collections SET character_count = ? WHERE collection_name = ?
                ''', (len(character_ids), collection_name))
            
            conn.commit()
            conn.close()
            
            print(f"📁 Collection created: {collection_name}")
            return True
            
        except Exception as e:
            print(f"❌ Collection creation failed: {e}")
            return False
    
    def export_collection(self, collection_name, export_format="zip"):
        """Export a collection of characters"""
        try:
            # Get collection characters
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT c.* FROM characters c
                JOIN character_collections cc ON c.character_id = cc.character_id
                WHERE cc.collection_name = ?
            ''', (collection_name,))
            
            characters = cursor.fetchall()
            conn.close()
            
            if not characters:
                print(f"❌ Collection '{collection_name}' not found or empty")
                return None
            
            # Create export directory
            export_dir = os.path.join(self.directories['exports'], f"{collection_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            os.makedirs(export_dir, exist_ok=True)
            
            # Copy character files
            for char in characters:
                char_dir = os.path.join(export_dir, char[1])  # char[1] is character_id
                os.makedirs(char_dir, exist_ok=True)
                
                # Copy GLB file
                if char[9] and os.path.exists(char[9]):  # glb_file_path
                    shutil.copy2(char[9], char_dir)
                
                # Copy source image
                if char[8] and os.path.exists(char[8]):  # source_image_path
                    shutil.copy2(char[8], char_dir)
                
                # Copy thumbnail
                if char[10] and os.path.exists(char[10]):  # thumbnail_path
                    shutil.copy2(char[10], char_dir)
            
            # Create collection manifest
            manifest = {
                "collection_name": collection_name,
                "export_date": datetime.now().isoformat(),
                "character_count": len(characters),
                "characters": [
                    {
                        "character_id": char[1],
                        "name": char[2],
                        "description": char[3],
                        "category": char[4],
                        "art_style": char[5]
                    } for char in characters
                ]
            }
            
            manifest_path = os.path.join(export_dir, "collection_manifest.json")
            with open(manifest_path, 'w') as f:
                json.dump(manifest, f, indent=2)
            
            print(f"📦 Collection exported: {export_dir}")
            return export_dir
            
        except Exception as e:
            print(f"❌ Collection export failed: {e}")
            return None
    
    def get_library_stats(self):
        """Get library statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total characters
            cursor.execute("SELECT COUNT(*) FROM characters")
            total_characters = cursor.fetchone()[0]
            
            # Characters by category
            cursor.execute("SELECT category, COUNT(*) FROM characters GROUP BY category")
            by_category = dict(cursor.fetchall())
            
            # Characters by art style
            cursor.execute("SELECT art_style, COUNT(*) FROM characters GROUP BY art_style")
            by_art_style = dict(cursor.fetchall())
            
            # Face matched characters
            cursor.execute("SELECT COUNT(*) FROM characters WHERE face_matched = 1")
            face_matched_count = cursor.fetchone()[0]
            
            # AI enhanced characters
            cursor.execute("SELECT COUNT(*) FROM characters WHERE ai_enhanced = 1")
            ai_enhanced_count = cursor.fetchone()[0]
            
            # Total file size
            cursor.execute("SELECT SUM(file_size) FROM characters")
            total_size = cursor.fetchone()[0] or 0
            
            # Collections count
            cursor.execute("SELECT COUNT(*) FROM collections")
            collections_count = cursor.fetchone()[0]
            
            conn.close()
            
            stats = {
                "total_characters": total_characters,
                "by_category": by_category,
                "by_art_style": by_art_style,
                "face_matched": face_matched_count,
                "ai_enhanced": ai_enhanced_count,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "collections_count": collections_count,
                "library_path": self.library_path
            }
            
            return stats
            
        except Exception as e:
            print(f"❌ Stats calculation failed: {e}")
            return {}

def main():
    """Main library function"""
    library = CharacterLibrary()
    
    print("📚 CHARACTER LIBRARY SYSTEM")
    print("=" * 30)
    
    # Show library stats
    stats = library.get_library_stats()
    
    print(f"\n📊 Library Statistics:")
    print(f"   Total characters: {stats.get('total_characters', 0)}")
    print(f"   Face matched: {stats.get('face_matched', 0)}")
    print(f"   AI enhanced: {stats.get('ai_enhanced', 0)}")
    print(f"   Total size: {stats.get('total_size_mb', 0)} MB")
    print(f"   Collections: {stats.get('collections_count', 0)}")
    
    if stats.get('by_category'):
        print(f"\n📂 By Category:")
        for category, count in stats['by_category'].items():
            print(f"   {category}: {count}")
    
    if stats.get('by_art_style'):
        print(f"\n🎨 By Art Style:")
        for style, count in stats['by_art_style'].items():
            print(f"   {style}: {count}")
    
    print(f"\n📁 Library Location: {library.library_path}")
    print(f"💡 Use the library to organize and manage your generated characters")
    
    input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
