#!/usr/bin/env python3
"""
Real face photo testing system for character generation
Tests with actual face photos for production validation
"""

import os
import sys
import glob
import shutil
from datetime import datetime
from pathlib import Path

# Add backend to path
sys.path.append('backend')

class RealFaceTester:
    """Test character generation with real face photos"""
    
    def __init__(self):
        self.test_photos_dir = "test_photos"
        self.results_dir = "test_results"
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
        
        # Setup directories
        self.setup_directories()
        
        # Initialize generators
        self.setup_generators()
    
    def setup_directories(self):
        """Setup test directories"""
        directories = [
            self.test_photos_dir,
            self.results_dir,
            os.path.join(self.results_dir, "characters"),
            os.path.join(self.results_dir, "analysis"),
            os.path.join(self.results_dir, "comparisons")
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        print(f"✅ Test directories ready")
    
    def setup_generators(self):
        """Setup character generators"""
        try:
            from enhanced_character_generator import EnhancedCharacterGenerator
            from character_generator import CharacterGenerator
            
            self.enhanced_generator = EnhancedCharacterGenerator()
            self.basic_generator = CharacterGenerator()
            
            print(f"✅ Character generators initialized")
            print(f"   Enhanced (AI): {self.enhanced_generator.comfyui.available}")
            print(f"   Basic: Available")
            
        except Exception as e:
            print(f"❌ Generator setup failed: {e}")
            self.enhanced_generator = None
            self.basic_generator = None
    
    def find_test_photos(self):
        """Find test photos in the test directory"""
        photo_files = []
        
        for ext in self.supported_formats:
            pattern = os.path.join(self.test_photos_dir, f"*{ext}")
            photo_files.extend(glob.glob(pattern))
            pattern = os.path.join(self.test_photos_dir, f"*{ext.upper()}")
            photo_files.extend(glob.glob(pattern))
        
        return sorted(photo_files)
    
    def create_sample_photos(self):
        """Create sample face photos for testing"""
        try:
            from PIL import Image, ImageDraw
            
            print("🎨 Creating sample face photos for testing...")
            
            # Sample face variations
            face_configs = [
                {
                    "name": "sample_face_1_oval.png",
                    "face_shape": "oval",
                    "skin_tone": "#f4c2a1",
                    "hair_color": "#8B4513",
                    "eye_color": "#2E8B57"
                },
                {
                    "name": "sample_face_2_round.png", 
                    "face_shape": "round",
                    "skin_tone": "#d4a574",
                    "hair_color": "#654321",
                    "eye_color": "#4169E1"
                },
                {
                    "name": "sample_face_3_square.png",
                    "face_shape": "square", 
                    "skin_tone": "#c8b4a0",
                    "hair_color": "#2F4F4F",
                    "eye_color": "#8B4513"
                },
                {
                    "name": "sample_face_4_long.png",
                    "face_shape": "long",
                    "skin_tone": "#e6b896",
                    "hair_color": "#FF6347",
                    "eye_color": "#228B22"
                }
            ]
            
            for config in face_configs:
                self.create_sample_face(config)
            
            print(f"✅ Created {len(face_configs)} sample face photos")
            return len(face_configs)
            
        except ImportError:
            print("⚠️ PIL not available for sample creation")
            return 0
        except Exception as e:
            print(f"❌ Sample creation failed: {e}")
            return 0
    
    def create_sample_face(self, config):
        """Create a single sample face"""
        try:
            from PIL import Image, ImageDraw
            
            width, height = 512, 512
            image = Image.new('RGB', (width, height), color='white')
            draw = ImageDraw.Draw(image)
            
            # Face shape variations
            if config["face_shape"] == "oval":
                face_coords = [140, 140, 372, 400]
            elif config["face_shape"] == "round":
                face_coords = [128, 128, 384, 384]
            elif config["face_shape"] == "square":
                face_coords = [140, 140, 372, 372]
            else:  # long
                face_coords = [150, 120, 362, 420]
            
            # Draw face
            draw.ellipse(face_coords, fill=config["skin_tone"], outline='#d4a574', width=3)
            
            # Calculate face center and proportions
            face_center_x = (face_coords[0] + face_coords[2]) // 2
            face_center_y = (face_coords[1] + face_coords[3]) // 2
            face_width = face_coords[2] - face_coords[0]
            face_height = face_coords[3] - face_coords[1]
            
            # Eyes (proportional to face)
            eye_y = face_center_y - face_height // 6
            eye_size = face_width // 12
            left_eye_x = face_center_x - face_width // 6
            right_eye_x = face_center_x + face_width // 6
            
            # Draw eyes
            draw.ellipse([left_eye_x - eye_size, eye_y - eye_size//2, 
                         left_eye_x + eye_size, eye_y + eye_size//2], 
                        fill='white', outline='black', width=2)
            draw.ellipse([right_eye_x - eye_size, eye_y - eye_size//2,
                         right_eye_x + eye_size, eye_y + eye_size//2],
                        fill='white', outline='black', width=2)
            
            # Pupils
            pupil_size = eye_size // 3
            draw.ellipse([left_eye_x - pupil_size//2, eye_y - pupil_size//2,
                         left_eye_x + pupil_size//2, eye_y + pupil_size//2],
                        fill=config["eye_color"])
            draw.ellipse([right_eye_x - pupil_size//2, eye_y - pupil_size//2,
                         right_eye_x + pupil_size//2, eye_y + pupil_size//2],
                        fill=config["eye_color"])
            
            # Eyebrows
            brow_y = eye_y - eye_size
            draw.arc([left_eye_x - eye_size, brow_y - eye_size//4,
                     left_eye_x + eye_size, brow_y + eye_size//4], 
                    0, 180, fill=config["hair_color"], width=4)
            draw.arc([right_eye_x - eye_size, brow_y - eye_size//4,
                     right_eye_x + eye_size, brow_y + eye_size//4],
                    0, 180, fill=config["hair_color"], width=4)
            
            # Nose
            nose_y = face_center_y
            nose_width = face_width // 20
            draw.polygon([(face_center_x, nose_y - nose_width),
                         (face_center_x - nose_width, nose_y + nose_width),
                         (face_center_x + nose_width, nose_y + nose_width)],
                        fill=config["skin_tone"], outline='#d4a574')
            
            # Mouth
            mouth_y = face_center_y + face_height // 6
            mouth_width = face_width // 8
            draw.arc([face_center_x - mouth_width, mouth_y - mouth_width//4,
                     face_center_x + mouth_width, mouth_y + mouth_width//4],
                    0, 180, fill='#d4a574', width=4)
            
            # Hair
            hair_coords = [face_coords[0], face_coords[1], 
                          face_coords[2], face_center_y]
            draw.arc(hair_coords, 180, 360, fill=config["hair_color"], width=20)
            
            # Save
            file_path = os.path.join(self.test_photos_dir, config["name"])
            image.save(file_path)
            
            print(f"   Created: {config['name']} ({config['face_shape']} face)")
            
        except Exception as e:
            print(f"❌ Failed to create {config['name']}: {e}")
    
    def test_single_photo(self, photo_path, test_name=None):
        """Test character generation with a single photo"""
        try:
            if not test_name:
                test_name = Path(photo_path).stem
            
            print(f"\n🧪 Testing: {test_name}")
            print(f"   Photo: {photo_path}")
            
            results = {}
            
            # Test 1: Basic character generation
            if self.basic_generator:
                print("   🔧 Basic generation...")
                basic_result = self.basic_generator.generate_character_from_image(
                    photo_path,
                    f"A realistic character based on {test_name}",
                    quality='high',
                    style='realistic'
                )
                results['basic'] = basic_result
            
            # Test 2: Enhanced AI generation (if available)
            if self.enhanced_generator and self.enhanced_generator.comfyui.available:
                print("   🎨 AI-enhanced generation...")
                enhanced_result = self.enhanced_generator.generate_enhanced_character_from_image(
                    photo_path,
                    f"A realistic character with AI enhancement based on {test_name}",
                    quality='high',
                    style='realistic',
                    use_ai_enhancement=True
                )
                results['enhanced'] = enhanced_result
            
            # Save test results
            self.save_test_results(test_name, photo_path, results)
            
            return results
            
        except Exception as e:
            print(f"❌ Test failed for {photo_path}: {e}")
            return None
    
    def save_test_results(self, test_name, photo_path, results):
        """Save test results for analysis"""
        try:
            import json
            
            # Create test result data
            test_data = {
                "test_name": test_name,
                "source_photo": photo_path,
                "timestamp": datetime.now().isoformat(),
                "results": {}
            }
            
            # Process results
            for method, result in results.items():
                if result and result.get('success'):
                    test_data["results"][method] = {
                        "success": True,
                        "glb_file": result.get('glb_file'),
                        "file_size": result.get('file_size'),
                        "vertex_count": result.get('vertex_count'),
                        "face_count": result.get('face_count'),
                        "face_matched": result.get('character_params', {}).get('face_matched', False),
                        "ai_enhanced": result.get('ai_enhanced', False),
                        "character_sheet": result.get('has_character_sheet', False),
                        "processing_method": result.get('enhancement_method', 'basic')
                    }
                else:
                    test_data["results"][method] = {
                        "success": False,
                        "error": result.get('error', 'Unknown error') if result else 'Generation failed'
                    }
            
            # Save test data
            result_file = os.path.join(self.results_dir, "analysis", f"{test_name}_test_results.json")
            with open(result_file, 'w') as f:
                json.dump(test_data, f, indent=2)
            
            print(f"   💾 Results saved: {result_file}")
            
        except Exception as e:
            print(f"❌ Failed to save test results: {e}")
    
    def run_comprehensive_test(self):
        """Run comprehensive testing with all available photos"""
        print("🧪 COMPREHENSIVE REAL FACE TESTING")
        print("=" * 40)
        
        # Find test photos
        photo_files = self.find_test_photos()
        
        if not photo_files:
            print("⚠️ No test photos found, creating samples...")
            sample_count = self.create_sample_photos()
            photo_files = self.find_test_photos()
        
        if not photo_files:
            print("❌ No test photos available")
            return False
        
        print(f"📸 Found {len(photo_files)} test photos:")
        for photo in photo_files:
            print(f"   - {os.path.basename(photo)}")
        
        # Run tests
        test_results = {}
        successful_tests = 0
        
        for i, photo_path in enumerate(photo_files, 1):
            print(f"\n{'='*50}")
            print(f"TEST {i}/{len(photo_files)}")
            print(f"{'='*50}")
            
            test_name = f"test_{i:02d}_{Path(photo_path).stem}"
            result = self.test_single_photo(photo_path, test_name)
            
            if result:
                test_results[test_name] = result
                if any(r and r.get('success') for r in result.values()):
                    successful_tests += 1
        
        # Generate summary report
        self.generate_test_report(test_results, photo_files)
        
        print(f"\n📊 COMPREHENSIVE TEST COMPLETE")
        print(f"   Total photos tested: {len(photo_files)}")
        print(f"   Successful tests: {successful_tests}")
        print(f"   Success rate: {successful_tests/len(photo_files)*100:.1f}%")
        
        return successful_tests > 0
    
    def generate_test_report(self, test_results, photo_files):
        """Generate comprehensive test report"""
        try:
            report = {
                "test_summary": {
                    "timestamp": datetime.now().isoformat(),
                    "total_photos": len(photo_files),
                    "total_tests": len(test_results),
                    "photos_tested": [os.path.basename(p) for p in photo_files]
                },
                "method_comparison": {
                    "basic_generation": {"successful": 0, "failed": 0},
                    "ai_enhanced": {"successful": 0, "failed": 0}
                },
                "detailed_results": test_results,
                "recommendations": []
            }
            
            # Analyze results
            for test_name, results in test_results.items():
                for method, result in results.items():
                    if result and result.get('success'):
                        if method == 'basic':
                            report["method_comparison"]["basic_generation"]["successful"] += 1
                        elif method == 'enhanced':
                            report["method_comparison"]["ai_enhanced"]["successful"] += 1
                    else:
                        if method == 'basic':
                            report["method_comparison"]["basic_generation"]["failed"] += 1
                        elif method == 'enhanced':
                            report["method_comparison"]["ai_enhanced"]["failed"] += 1
            
            # Generate recommendations
            basic_success_rate = report["method_comparison"]["basic_generation"]["successful"] / max(1, sum(report["method_comparison"]["basic_generation"].values()))
            ai_success_rate = report["method_comparison"]["ai_enhanced"]["successful"] / max(1, sum(report["method_comparison"]["ai_enhanced"].values()))
            
            if basic_success_rate > 0.8:
                report["recommendations"].append("✅ Basic character generation is working well")
            else:
                report["recommendations"].append("⚠️ Basic character generation needs improvement")
            
            if ai_success_rate > 0.8:
                report["recommendations"].append("✅ AI-enhanced generation is working excellently")
            elif ai_success_rate > 0:
                report["recommendations"].append("⚠️ AI-enhanced generation is partially working")
            else:
                report["recommendations"].append("❌ AI-enhanced generation needs setup")
            
            # Save report
            report_file = os.path.join(self.results_dir, f"comprehensive_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            print(f"\n📋 Test report saved: {report_file}")
            
            # Print summary
            print(f"\n📊 METHOD COMPARISON:")
            print(f"   Basic Generation: {report['method_comparison']['basic_generation']['successful']}/{sum(report['method_comparison']['basic_generation'].values())} successful")
            print(f"   AI Enhanced: {report['method_comparison']['ai_enhanced']['successful']}/{sum(report['method_comparison']['ai_enhanced'].values())} successful")
            
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in report["recommendations"]:
                print(f"   {rec}")
            
        except Exception as e:
            print(f"❌ Failed to generate test report: {e}")
    
    def setup_test_photos_instructions(self):
        """Provide instructions for setting up test photos"""
        instructions = f"""
🎯 REAL FACE PHOTO TESTING SETUP
================================

To test with real face photos:

1. 📁 Create test photos directory:
   - Directory: {os.path.abspath(self.test_photos_dir)}
   - Add your face photos (.jpg, .png, .bmp, .webp)

2. 📸 Photo requirements:
   - Clear, well-lit face photos
   - Front-facing or slight angle
   - Good resolution (512x512 or higher)
   - Single person per photo

3. 🏃 Run testing:
   - python real_face_tester.py
   - Or use the comprehensive test function

4. 📊 View results:
   - Results saved in: {os.path.abspath(self.results_dir)}
   - GLB files in: backend/outputs/models/
   - Analysis in: {os.path.abspath(self.results_dir)}/analysis/

5. 🎨 Sample photos:
   - If no photos found, samples will be created automatically
   - Use your own photos for best results
        """
        
        print(instructions)
        return instructions

def main():
    """Main testing function"""
    tester = RealFaceTester()
    
    # Show setup instructions
    tester.setup_test_photos_instructions()
    
    # Check for photos
    photos = tester.find_test_photos()
    
    if not photos:
        print(f"\n⚠️ No test photos found in {tester.test_photos_dir}")
        create_samples = input("Create sample photos for testing? (y/n): ").lower().strip()
        
        if create_samples == 'y':
            tester.create_sample_photos()
        else:
            print("Add your own photos to the test_photos directory and run again.")
            return
    
    # Run comprehensive test
    print(f"\n🚀 Starting comprehensive testing...")
    success = tester.run_comprehensive_test()
    
    if success:
        print(f"\n🎉 REAL FACE TESTING SUCCESSFUL!")
        print(f"✅ Character generation validated with real photos")
        print(f"📁 Check results in: {tester.results_dir}")
    else:
        print(f"\n⚠️ TESTING COMPLETED WITH ISSUES")
        print(f"Check the results for detailed analysis")
    
    input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
