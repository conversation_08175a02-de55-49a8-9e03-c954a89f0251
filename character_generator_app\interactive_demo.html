<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 3D Character Generator - Interactive Demo</title>
    <!-- Three.js for 3D rendering -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-full {
            width: 100%;
        }

        .progress-section {
            margin-top: 2rem;
            padding: 1.5rem;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(102, 126, 234, 0.2);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            border-radius: 10px;
            width: 0%;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-processing { background-color: #17a2b8; color: white; }
        .status-completed { background-color: #28a745; color: white; }

        .viewer-container {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 1rem 0;
            position: relative;
        }

        .viewer-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
            z-index: 100;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            z-index: 200;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .character-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .example-btn {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e0e0e0;
            padding: 1rem;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
        }

        .example-btn:hover {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
        }

        .download-section {
            background: rgba(40, 167, 69, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            border: 2px solid rgba(40, 167, 69, 0.2);
            margin-top: 1rem;
        }

        .download-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            margin: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 3D Character Generator</h1>
            <p>Interactive Demo - Create 3D characters instantly!</p>
        </div>

        <!-- Character Generation Form -->
        <div class="demo-card">
            <h2>Generate Your Character</h2>
            
            <div class="form-group">
                <label class="form-label">Character Description</label>
                <textarea
                    id="textPrompt"
                    class="form-control"
                    placeholder="Describe your character... (e.g., 'A brave blue robot warrior with silver armor')"
                >A brave blue robot warrior with silver armor</textarea>
            </div>

            <div class="form-group">
                <label class="form-label">Quick Examples</label>
                <div class="character-examples">
                    <div class="example-btn" onclick="setExample('A blue robot warrior with silver armor')">
                        🤖 Robot Warrior
                    </div>
                    <div class="example-btn" onclick="setExample('A mystical alien wizard with purple robes')">
                        👽 Alien Wizard
                    </div>
                    <div class="example-btn" onclick="setExample('A fierce dragon knight with golden scales')">
                        🐉 Dragon Knight
                    </div>
                    <div class="example-btn" onclick="setExample('A cute cartoon cat character')">
                        🐱 Cartoon Cat
                    </div>
                </div>
            </div>

            <button onclick="generateCharacter()" class="btn btn-full" id="generateBtn">
                Generate 3D Character
            </button>
        </div>

        <!-- Progress Section -->
        <div class="demo-card hidden" id="progressSection">
            <h3>Generation Progress</h3>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <span id="jobId">Job ID: </span>
                <span class="status-badge status-processing" id="statusBadge">Processing</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="progressText">0% complete</p>
        </div>

        <!-- 3D Viewer Section -->
        <div class="demo-card hidden" id="viewerSection">
            <h3>🎮 3D Character Viewer</h3>
            
            <div class="viewer-container" id="viewerContainer">
                <div class="loading-overlay" id="loadingOverlay">
                    <div>
                        <div class="loading"></div>
                        <p>Loading 3D model...</p>
                    </div>
                </div>
                <div class="viewer-info" id="viewerInfo">
                    Use mouse to rotate, zoom, and pan
                </div>
            </div>

            <div class="download-section" id="downloadSection">
                <h4>Download Your Character</h4>
                <p>Your 3D character is ready! Download it for use in games, Blender, Unity, etc.</p>
                <button class="btn download-btn" onclick="downloadCharacter()">
                    📥 Download GLB File
                </button>
                <button class="btn download-btn" onclick="viewInBlender()">
                    🎨 Open in Blender
                </button>
                <button class="btn download-btn" onclick="generateAnother()">
                    🔄 Generate Another
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let scene, camera, renderer, controls;
        let currentModel = null;
        let currentCharacterData = null;

        // Set example prompt
        function setExample(prompt) {
            document.getElementById('textPrompt').value = prompt;
        }

        // Generate character (demo mode)
        async function generateCharacter() {
            const textPrompt = document.getElementById('textPrompt').value.trim();
            
            if (!textPrompt) {
                alert('Please enter a character description.');
                return;
            }

            const generateBtn = document.getElementById('generateBtn');
            const progressSection = document.getElementById('progressSection');
            
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<div class="loading"></div> Generating...';
            progressSection.classList.remove('hidden');

            try {
                // Generate job ID
                const jobId = Date.now().toString(36);
                document.getElementById('jobId').textContent = `Job ID: ${jobId}`;
                
                // Analyze character type
                const characterType = analyzeCharacterPrompt(textPrompt);
                
                // Simulate progress
                await simulateProgress(characterType, textPrompt);
                
            } catch (error) {
                console.error('Generation error:', error);
                alert(`Generation failed: ${error.message}`);
                
                generateBtn.disabled = false;
                generateBtn.innerHTML = 'Generate 3D Character';
                progressSection.classList.add('hidden');
            }
        }

        // Analyze character prompt (client-side)
        function analyzeCharacterPrompt(prompt) {
            const lowerPrompt = prompt.toLowerCase();
            
            if (lowerPrompt.includes('robot') || lowerPrompt.includes('android') || lowerPrompt.includes('cyborg')) {
                return { name: 'robot', color: [0.2, 0.4, 0.9], description: 'Mechanical warrior with advanced armor' };
            } else if (lowerPrompt.includes('alien') || lowerPrompt.includes('extraterrestrial')) {
                return { name: 'alien', color: [0.7, 0.2, 0.8], description: 'Mysterious being from another world' };
            } else if (lowerPrompt.includes('warrior') || lowerPrompt.includes('knight') || lowerPrompt.includes('fighter')) {
                return { name: 'warrior', color: [0.9, 0.8, 0.2], description: 'Battle-hardened fighter with golden armor' };
            } else if (lowerPrompt.includes('wizard') || lowerPrompt.includes('mage') || lowerPrompt.includes('sorcerer')) {
                return { name: 'wizard', color: [0.5, 0.2, 0.9], description: 'Mystical spellcaster with magical powers' };
            } else if (lowerPrompt.includes('cat') || lowerPrompt.includes('feline')) {
                return { name: 'cat', color: [0.9, 0.7, 0.4], description: 'Adorable feline character' };
            } else {
                return { name: 'humanoid', color: [0.8, 0.6, 0.4], description: 'Versatile humanoid character' };
            }
        }

        // Simulate generation progress
        async function simulateProgress(characterType, prompt) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const statusBadge = document.getElementById('statusBadge');
            
            // Stage 1: Analysis
            statusBadge.textContent = 'Analyzing';
            await updateProgress(20, 'Analyzing character description...');
            
            // Stage 2: Geometry Generation
            await updateProgress(40, 'Generating 3D geometry...');
            
            // Stage 3: Materials
            await updateProgress(60, 'Creating materials and textures...');
            
            // Stage 4: Optimization
            await updateProgress(80, 'Optimizing for web display...');
            
            // Stage 5: Complete
            await updateProgress(100, 'Character generation complete!');
            
            statusBadge.textContent = 'Completed';
            statusBadge.className = 'status-badge status-completed';
            
            // Store character data
            currentCharacterData = {
                prompt: prompt,
                type: characterType,
                timestamp: new Date().toISOString()
            };
            
            // Show completion and load 3D model
            showCompletion(characterType, prompt);
        }

        async function updateProgress(percent, message) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressFill.style.width = `${percent}%`;
            progressText.textContent = `${percent}% - ${message}`;
            
            // Wait for animation
            await new Promise(resolve => setTimeout(resolve, 800));
        }

        // Show completion and 3D viewer
        function showCompletion(characterType, prompt) {
            const viewerSection = document.getElementById('viewerSection');
            viewerSection.classList.remove('hidden');
            
            // Initialize 3D viewer
            init3DViewer();
            
            // Load demo model
            loadDemoModel(characterType);
            
            // Reset generate button
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = false;
            generateBtn.innerHTML = 'Generate 3D Character';
        }

        // Initialize 3D viewer
        function init3DViewer() {
            const container = document.getElementById('viewerContainer');
            
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x2a5298);
            
            // Camera
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.set(3, 2, 3);
            
            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            container.appendChild(renderer.domElement);
            
            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            
            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(5, 5, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            // Start animation loop
            animate();
            
            console.log('3D viewer initialized');
        }

        // Load demo model based on character type
        function loadDemoModel(characterType) {
            // Remove existing model
            if (currentModel) {
                scene.remove(currentModel);
            }
            
            // Create character based on type
            const group = new THREE.Group();
            const color = new THREE.Color().setRGB(characterType.color[0], characterType.color[1], characterType.color[2]);
            
            // Body
            const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.2, 8);
            const bodyMaterial = new THREE.MeshLambertMaterial({ color: color });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.y = 0;
            body.castShadow = true;
            group.add(body);
            
            // Head
            const headGeometry = new THREE.SphereGeometry(0.25, 16, 16);
            const headMaterial = new THREE.MeshLambertMaterial({ color: 0xf4c2a1 });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.y = 0.8;
            head.castShadow = true;
            group.add(head);
            
            // Character-specific features
            if (characterType.name === 'robot') {
                // Add antenna
                const antennaGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.3, 8);
                const antennaMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
                const antenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
                antenna.position.y = 1.2;
                group.add(antenna);
            } else if (characterType.name === 'wizard') {
                // Add hat
                const hatGeometry = new THREE.ConeGeometry(0.3, 0.5, 8);
                const hatMaterial = new THREE.MeshLambertMaterial({ color: 0x4a0080 });
                const hat = new THREE.Mesh(hatGeometry, hatMaterial);
                hat.position.y = 1.3;
                group.add(hat);
            }
            
            // Arms and legs
            const armGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.8, 8);
            const legGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.8, 8);
            
            const leftArm = new THREE.Mesh(armGeometry, bodyMaterial);
            leftArm.position.set(-0.5, 0.2, 0);
            leftArm.castShadow = true;
            group.add(leftArm);
            
            const rightArm = new THREE.Mesh(armGeometry, bodyMaterial);
            rightArm.position.set(0.5, 0.2, 0);
            rightArm.castShadow = true;
            group.add(rightArm);
            
            const leftLeg = new THREE.Mesh(legGeometry, new THREE.MeshLambertMaterial({ color: 0x2c3e50 }));
            leftLeg.position.set(-0.15, -0.8, 0);
            leftLeg.castShadow = true;
            group.add(leftLeg);
            
            const rightLeg = new THREE.Mesh(legGeometry, new THREE.MeshLambertMaterial({ color: 0x2c3e50 }));
            rightLeg.position.set(0.15, -0.8, 0);
            rightLeg.castShadow = true;
            group.add(rightLeg);
            
            currentModel = group;
            scene.add(currentModel);
            
            // Hide loading overlay
            document.getElementById('loadingOverlay').style.display = 'none';
            
            console.log(`${characterType.name} character loaded`);
        }

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            // Rotate model slowly
            if (currentModel) {
                currentModel.rotation.y += 0.005;
            }
            
            if (controls) controls.update();
            if (renderer && scene && camera) renderer.render(scene, camera);
        }

        // Download character
        function downloadCharacter() {
            if (!currentCharacterData) {
                alert('No character generated yet!');
                return;
            }
            
            // Create a mock GLB file download
            const filename = `character_${currentCharacterData.type.name}_${Date.now()}.glb`;
            
            // In a real implementation, this would download the actual GLB file
            alert(`Character download started!\n\nFile: ${filename}\nType: ${currentCharacterData.type.name}\nDescription: ${currentCharacterData.type.description}\n\nNote: This is a demo. In the full version, a real GLB file would be downloaded.`);
        }

        // View in Blender
        function viewInBlender() {
            alert('In the full version, this would:\n\n1. Download the GLB file\n2. Open Blender automatically\n3. Import the character\n4. Set up the scene for editing\n\nFor now, download the GLB and import manually into Blender.');
        }

        // Generate another character
        function generateAnother() {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('viewerSection').classList.add('hidden');
            document.getElementById('textPrompt').focus();
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                const container = document.getElementById('viewerContainer');
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        });

        // Initialize on page load
        window.addEventListener('load', () => {
            console.log('3D Character Generator Demo loaded');
        });
    </script>
</body>
</html>
