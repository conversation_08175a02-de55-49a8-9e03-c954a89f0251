using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CameraController : MonoBehaviour
{
    // Target to follow
    public Transform target;
    
    // Camera parameters
    public float smoothSpeed = 0.125f;
    public Vector3 offset = new Vector3(0, 2, -5);
    
    // Mouse look parameters
    public float mouseSensitivity = 100f;
    
    // Rotation variables
    private float xRotation = 0f;
    
    // Start is called before the first frame update
    void Start()
    {
        // Lock and hide the cursor
        Cursor.lockState = CursorLockMode.Locked;
    }

    // Update is called once per frame
    void Update()
    {
        // Get mouse input
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity * Time.deltaTime;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity * Time.deltaTime;
        
        // Calculate rotation
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -90f, 90f);
        
        // Apply rotation to camera
        transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        
        // Rotate the player based on mouse X input
        target.Rotate(Vector3.up * mouseX);
    }
    
    // LateUpdate is called after all Update functions have been called
    void LateUpdate()
    {
        // Calculate the desired position
        Vector3 desiredPosition = target.position + offset;
        
        // Smoothly move the camera to the desired position
        Vector3 smoothedPosition = Vector3.Lerp(transform.position, desiredPosition, smoothSpeed);
        
        // Update the camera position
        transform.position = smoothedPosition;
    }
}
