#!/usr/bin/env python3
"""
Advanced face processing with MediaPipe, dlib, and professional face reconstruction
"""

import cv2
import numpy as np
import os
import json
from PIL import Image
import math

class AdvancedFaceProcessor:
    """Professional face processing with MediaPipe and advanced algorithms"""
    
    def __init__(self):
        self.mediapipe_available = False
        self.dlib_available = False
        self.face_recognition_available = False
        
        self.init_mediapipe()
        self.init_dlib()
        self.init_face_recognition()
        
        # Fallback to basic OpenCV if advanced tools unavailable
        if not any([self.mediapipe_available, self.dlib_available]):
            from face_processor import FaceProcessor
            self.fallback_processor = FaceProcessor()
    
    def init_mediapipe(self):
        """Initialize MediaPipe Face Mesh"""
        try:
            import mediapipe as mp
            
            self.mp_face_mesh = mp.solutions.face_mesh
            self.mp_drawing = mp.solutions.drawing_utils
            self.mp_drawing_styles = mp.solutions.drawing_styles
            
            # Initialize face mesh with high-quality settings
            self.face_mesh = self.mp_face_mesh.FaceMesh(
                static_image_mode=True,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.7,
                min_tracking_confidence=0.5
            )
            
            self.mediapipe_available = True
            print("✅ MediaPipe Face Mesh initialized (468 landmarks)")
            
        except ImportError:
            print("⚠️ MediaPipe not available, using fallback")
            self.mediapipe_available = False
        except Exception as e:
            print(f"⚠️ MediaPipe initialization error: {e}")
            self.mediapipe_available = False
    
    def init_dlib(self):
        """Initialize dlib face detection"""
        try:
            import dlib
            import face_recognition
            
            # Try to load dlib's face detector
            self.dlib_detector = dlib.get_frontal_face_detector()
            
            # Try to load facial landmark predictor
            predictor_path = "shape_predictor_68_face_landmarks.dat"
            if os.path.exists(predictor_path):
                self.dlib_predictor = dlib.shape_predictor(predictor_path)
                print("✅ dlib 68-point landmark predictor loaded")
            else:
                print("⚠️ dlib landmark predictor not found")
                print("📥 Download from: http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2")
            
            self.dlib_available = True
            
        except ImportError:
            print("⚠️ dlib not available")
            self.dlib_available = False
        except Exception as e:
            print(f"⚠️ dlib initialization error: {e}")
            self.dlib_available = False
    
    def init_face_recognition(self):
        """Initialize face_recognition library"""
        try:
            import face_recognition
            self.face_recognition_available = True
            print("✅ face_recognition library available")
        except ImportError:
            print("⚠️ face_recognition not available")
            self.face_recognition_available = False
    
    def extract_advanced_face_features(self, image_path):
        """Extract comprehensive face features using all available methods"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Could not load image")
            
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Try MediaPipe first (most detailed)
            if self.mediapipe_available:
                features = self.extract_mediapipe_features(image_rgb)
                if features and not features.get('analysis_failed'):
                    features['method'] = 'mediapipe'
                    features['landmark_count'] = 468
                    return features
            
            # Try dlib as backup
            if self.dlib_available:
                features = self.extract_dlib_features(image_rgb)
                if features and not features.get('analysis_failed'):
                    features['method'] = 'dlib'
                    features['landmark_count'] = 68
                    return features
            
            # Try face_recognition as backup
            if self.face_recognition_available:
                features = self.extract_face_recognition_features(image_rgb)
                if features and not features.get('analysis_failed'):
                    features['method'] = 'face_recognition'
                    return features
            
            # Fallback to basic processing
            print("⚠️ Using fallback face processing")
            features = self.fallback_processor.extract_face_features(image_path)
            features['method'] = 'opencv_fallback'
            return features
            
        except Exception as e:
            print(f"❌ Advanced face feature extraction failed: {e}")
            return self.get_default_features()
    
    def extract_mediapipe_features(self, image_rgb):
        """Extract features using MediaPipe Face Mesh (468 landmarks)"""
        try:
            results = self.face_mesh.process(image_rgb)
            
            if not results.multi_face_landmarks:
                print("⚠️ MediaPipe: No faces detected")
                return None
            
            # Get the first (and typically only) face
            face_landmarks = results.multi_face_landmarks[0]
            
            # Convert landmarks to numpy array
            landmarks = []
            h, w = image_rgb.shape[:2]
            
            for landmark in face_landmarks.landmark:
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                z = landmark.z  # Relative depth
                landmarks.append([x, y, z])
            
            landmarks = np.array(landmarks)
            
            # Extract detailed features
            features = {
                'landmarks_3d': landmarks.tolist(),
                'face_detected': True,
                'method': 'mediapipe',
                'landmark_count': len(landmarks),
                
                # Detailed facial regions
                'face_oval': self.get_face_oval_mediapipe(landmarks),
                'left_eye': self.get_eye_region_mediapipe(landmarks, 'left'),
                'right_eye': self.get_eye_region_mediapipe(landmarks, 'right'),
                'nose': self.get_nose_region_mediapipe(landmarks),
                'mouth': self.get_mouth_region_mediapipe(landmarks),
                'eyebrows': self.get_eyebrow_regions_mediapipe(landmarks),
                
                # Measurements
                'face_width': self.calculate_face_width_mediapipe(landmarks),
                'face_height': self.calculate_face_height_mediapipe(landmarks),
                'eye_distance': self.calculate_eye_distance_mediapipe(landmarks),
                'nose_width': self.calculate_nose_width_mediapipe(landmarks),
                'mouth_width': self.calculate_mouth_width_mediapipe(landmarks),
                
                # Advanced analysis
                'facial_symmetry': self.calculate_facial_symmetry_mediapipe(landmarks),
                'face_angle': self.calculate_face_angle_mediapipe(landmarks),
                'expression_analysis': self.analyze_expression_mediapipe(landmarks),
                
                # Color analysis
                'skin_tone': self.extract_skin_tone_advanced(image_rgb, landmarks),
                'eye_color': self.extract_eye_color(image_rgb, landmarks),
                'lip_color': self.extract_lip_color(image_rgb, landmarks),
                
                # 3D structure
                'face_depth_map': self.create_depth_map_mediapipe(landmarks),
                'facial_planes': self.analyze_facial_planes_mediapipe(landmarks)
            }
            
            return features
            
        except Exception as e:
            print(f"MediaPipe feature extraction error: {e}")
            return None
    
    def get_face_oval_mediapipe(self, landmarks):
        """Extract face oval landmarks"""
        # MediaPipe face oval indices
        face_oval_indices = [10, 338, 297, 332, 284, 251, 389, 356, 454, 323, 361, 288, 397, 365, 379, 378, 400, 377, 152, 148, 176, 149, 150, 136, 172, 58, 132, 93, 234, 127, 162, 21, 54, 103, 67, 109]
        
        face_oval_points = []
        for idx in face_oval_indices:
            if idx < len(landmarks):
                face_oval_points.append(landmarks[idx][:2].tolist())  # x, y only
        
        return face_oval_points
    
    def get_eye_region_mediapipe(self, landmarks, side):
        """Extract eye region landmarks"""
        if side == 'left':
            # Left eye indices
            eye_indices = [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246]
        else:
            # Right eye indices  
            eye_indices = [362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398]
        
        eye_points = []
        for idx in eye_indices:
            if idx < len(landmarks):
                eye_points.append(landmarks[idx][:2].tolist())
        
        return eye_points
    
    def get_nose_region_mediapipe(self, landmarks):
        """Extract nose region landmarks"""
        nose_indices = [1, 2, 5, 4, 6, 19, 20, 94, 125, 141, 235, 236, 3, 51, 48, 115, 131, 134, 102, 49, 220, 305, 281, 360, 279]
        
        nose_points = []
        for idx in nose_indices:
            if idx < len(landmarks):
                nose_points.append(landmarks[idx][:2].tolist())
        
        return nose_points
    
    def get_mouth_region_mediapipe(self, landmarks):
        """Extract mouth region landmarks"""
        mouth_indices = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318, 402, 317, 14, 87, 178, 88, 95, 78, 191, 80, 81, 82, 13, 312, 311, 310, 415, 308]
        
        mouth_points = []
        for idx in mouth_indices:
            if idx < len(landmarks):
                mouth_points.append(landmarks[idx][:2].tolist())
        
        return mouth_points
    
    def get_eyebrow_regions_mediapipe(self, landmarks):
        """Extract eyebrow landmarks"""
        left_eyebrow_indices = [46, 53, 52, 51, 48, 115, 131, 134, 102, 48, 64, 68]
        right_eyebrow_indices = [276, 283, 282, 295, 285, 336, 296, 334, 293, 300, 276, 283]
        
        left_eyebrow = []
        right_eyebrow = []
        
        for idx in left_eyebrow_indices:
            if idx < len(landmarks):
                left_eyebrow.append(landmarks[idx][:2].tolist())
        
        for idx in right_eyebrow_indices:
            if idx < len(landmarks):
                right_eyebrow.append(landmarks[idx][:2].tolist())
        
        return {'left': left_eyebrow, 'right': right_eyebrow}
    
    def calculate_face_width_mediapipe(self, landmarks):
        """Calculate face width from landmarks"""
        # Use temple points for width
        if len(landmarks) > 356:
            left_temple = landmarks[356][:2]
            right_temple = landmarks[127][:2]
            width = np.linalg.norm(np.array(left_temple) - np.array(right_temple))
            return float(width)
        return 0.0
    
    def calculate_face_height_mediapipe(self, landmarks):
        """Calculate face height from landmarks"""
        # Use forehead to chin
        if len(landmarks) > 10:
            forehead = landmarks[10][:2]
            chin = landmarks[152][:2]
            height = np.linalg.norm(np.array(forehead) - np.array(chin))
            return float(height)
        return 0.0
    
    def calculate_eye_distance_mediapipe(self, landmarks):
        """Calculate distance between eyes"""
        # Inner eye corners
        if len(landmarks) > 362:
            left_eye_inner = landmarks[133][:2]
            right_eye_inner = landmarks[362][:2]
            distance = np.linalg.norm(np.array(left_eye_inner) - np.array(right_eye_inner))
            return float(distance)
        return 0.0
    
    def calculate_nose_width_mediapipe(self, landmarks):
        """Calculate nose width"""
        if len(landmarks) > 360:
            left_nostril = landmarks[235][:2]
            right_nostril = landmarks[31][:2]
            width = np.linalg.norm(np.array(left_nostril) - np.array(right_nostril))
            return float(width)
        return 0.0
    
    def calculate_mouth_width_mediapipe(self, landmarks):
        """Calculate mouth width"""
        if len(landmarks) > 308:
            left_mouth = landmarks[61][:2]
            right_mouth = landmarks[291][:2]
            width = np.linalg.norm(np.array(left_mouth) - np.array(right_mouth))
            return float(width)
        return 0.0
    
    def calculate_facial_symmetry_mediapipe(self, landmarks):
        """Calculate facial symmetry score"""
        try:
            # Compare left and right sides of face
            symmetry_pairs = [
                (33, 362),    # Eye corners
                (61, 291),    # Mouth corners
                (235, 31),    # Nostrils
                (127, 356),   # Temples
            ]
            
            symmetry_scores = []
            face_center_x = np.mean([landmarks[i][0] for i in range(len(landmarks))])
            
            for left_idx, right_idx in symmetry_pairs:
                if left_idx < len(landmarks) and right_idx < len(landmarks):
                    left_point = landmarks[left_idx][:2]
                    right_point = landmarks[right_idx][:2]
                    
                    # Mirror right point across face center
                    mirrored_right = [2 * face_center_x - right_point[0], right_point[1]]
                    
                    # Calculate distance between left point and mirrored right
                    distance = np.linalg.norm(np.array(left_point) - np.array(mirrored_right))
                    
                    # Convert to symmetry score (lower distance = higher symmetry)
                    max_distance = 50  # Normalize based on typical face size
                    symmetry_score = max(0, 1 - (distance / max_distance))
                    symmetry_scores.append(symmetry_score)
            
            return float(np.mean(symmetry_scores)) if symmetry_scores else 0.8
            
        except Exception as e:
            print(f"Symmetry calculation error: {e}")
            return 0.8
    
    def calculate_face_angle_mediapipe(self, landmarks):
        """Calculate face rotation angle"""
        try:
            # Use eye centers to determine face angle
            if len(landmarks) > 362:
                left_eye_center = np.mean([landmarks[i][:2] for i in [33, 133, 157, 158]], axis=0)
                right_eye_center = np.mean([landmarks[i][:2] for i in [362, 263, 373, 374]], axis=0)
                
                # Calculate angle
                dx = right_eye_center[0] - left_eye_center[0]
                dy = right_eye_center[1] - left_eye_center[1]
                angle = math.degrees(math.atan2(dy, dx))
                
                return float(angle)
            return 0.0
        except Exception as e:
            print(f"Face angle calculation error: {e}")
            return 0.0
    
    def analyze_expression_mediapipe(self, landmarks):
        """Analyze facial expression"""
        try:
            expression = {
                'smile_score': 0.0,
                'eye_openness': {'left': 0.5, 'right': 0.5},
                'eyebrow_raise': 0.0,
                'mouth_openness': 0.0
            }
            
            # Analyze smile (mouth corner elevation)
            if len(landmarks) > 291:
                mouth_left = landmarks[61]
                mouth_right = landmarks[291]
                mouth_center = landmarks[13]
                
                # Calculate if mouth corners are raised
                corner_height = (mouth_left[1] + mouth_right[1]) / 2
                center_height = mouth_center[1]
                
                if corner_height < center_height:  # Corners higher than center
                    expression['smile_score'] = min(1.0, (center_height - corner_height) / 10)
            
            # Analyze eye openness
            # Left eye
            if len(landmarks) > 159:
                left_eye_top = landmarks[159][1]
                left_eye_bottom = landmarks[145][1]
                left_eye_openness = abs(left_eye_top - left_eye_bottom) / 20
                expression['eye_openness']['left'] = min(1.0, left_eye_openness)
            
            # Right eye
            if len(landmarks) > 386:
                right_eye_top = landmarks[386][1]
                right_eye_bottom = landmarks[374][1]
                right_eye_openness = abs(right_eye_top - right_eye_bottom) / 20
                expression['eye_openness']['right'] = min(1.0, right_eye_openness)
            
            return expression
            
        except Exception as e:
            print(f"Expression analysis error: {e}")
            return {'smile_score': 0.0, 'eye_openness': {'left': 0.5, 'right': 0.5}}
    
    def extract_skin_tone_advanced(self, image_rgb, landmarks):
        """Extract skin tone using facial landmarks"""
        try:
            # Define skin regions (cheeks, forehead)
            skin_regions = [
                [116, 117, 118, 119, 120],  # Left cheek
                [345, 346, 347, 348, 349],  # Right cheek
                [9, 10, 151, 337, 299]      # Forehead
            ]
            
            skin_pixels = []
            h, w = image_rgb.shape[:2]
            
            for region in skin_regions:
                for idx in region:
                    if idx < len(landmarks):
                        x, y = int(landmarks[idx][0]), int(landmarks[idx][1])
                        if 0 <= x < w and 0 <= y < h:
                            # Sample area around landmark
                            for dx in range(-5, 6):
                                for dy in range(-5, 6):
                                    nx, ny = x + dx, y + dy
                                    if 0 <= nx < w and 0 <= ny < h:
                                        skin_pixels.append(image_rgb[ny, nx])
            
            if skin_pixels:
                avg_color = np.mean(skin_pixels, axis=0)
                return {
                    'rgb': avg_color.tolist(),
                    'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
                    'tone_category': self.categorize_skin_tone(avg_color),
                    'method': 'landmark_based'
                }
            else:
                # Fallback to center region
                center_y, center_x = h//2, w//2
                center_region = image_rgb[center_y-20:center_y+20, center_x-20:center_x+20]
                avg_color = np.mean(center_region.reshape(-1, 3), axis=0)
                return {
                    'rgb': avg_color.tolist(),
                    'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_color[0]), int(avg_color[1]), int(avg_color[2])),
                    'tone_category': self.categorize_skin_tone(avg_color),
                    'method': 'center_fallback'
                }
                
        except Exception as e:
            print(f"Skin tone extraction error: {e}")
            return {'rgb': [200, 180, 160], 'hex': '#c8b4a0', 'tone_category': 'medium'}
    
    def extract_eye_color(self, image_rgb, landmarks):
        """Extract eye color from eye regions"""
        try:
            # Eye center indices
            left_eye_center = [468, 469, 470, 471, 472]  # Iris landmarks
            right_eye_center = [473, 474, 475, 476, 477]
            
            eye_colors = []
            h, w = image_rgb.shape[:2]
            
            for eye_indices in [left_eye_center, right_eye_center]:
                eye_pixels = []
                for idx in eye_indices:
                    if idx < len(landmarks):
                        x, y = int(landmarks[idx][0]), int(landmarks[idx][1])
                        if 0 <= x < w and 0 <= y < h:
                            eye_pixels.append(image_rgb[y, x])
                
                if eye_pixels:
                    avg_eye_color = np.mean(eye_pixels, axis=0)
                    eye_colors.append(avg_eye_color)
            
            if eye_colors:
                final_eye_color = np.mean(eye_colors, axis=0)
                return {
                    'rgb': final_eye_color.tolist(),
                    'hex': '#{:02x}{:02x}{:02x}'.format(int(final_eye_color[0]), int(final_eye_color[1]), int(final_eye_color[2])),
                    'detected': True
                }
            else:
                return {'rgb': [100, 100, 100], 'hex': '#646464', 'detected': False}
                
        except Exception as e:
            print(f"Eye color extraction error: {e}")
            return {'rgb': [100, 100, 100], 'hex': '#646464', 'detected': False}
    
    def extract_lip_color(self, image_rgb, landmarks):
        """Extract lip color"""
        try:
            # Lip landmarks
            lip_indices = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308]
            
            lip_pixels = []
            h, w = image_rgb.shape[:2]
            
            for idx in lip_indices:
                if idx < len(landmarks):
                    x, y = int(landmarks[idx][0]), int(landmarks[idx][1])
                    if 0 <= x < w and 0 <= y < h:
                        lip_pixels.append(image_rgb[y, x])
            
            if lip_pixels:
                avg_lip_color = np.mean(lip_pixels, axis=0)
                return {
                    'rgb': avg_lip_color.tolist(),
                    'hex': '#{:02x}{:02x}{:02x}'.format(int(avg_lip_color[0]), int(avg_lip_color[1]), int(avg_lip_color[2])),
                    'detected': True
                }
            else:
                return {'rgb': [180, 120, 120], 'hex': '#b47878', 'detected': False}
                
        except Exception as e:
            print(f"Lip color extraction error: {e}")
            return {'rgb': [180, 120, 120], 'hex': '#b47878', 'detected': False}
    
    def create_depth_map_mediapipe(self, landmarks):
        """Create depth map from z-coordinates"""
        try:
            depth_map = []
            for landmark in landmarks:
                if len(landmark) > 2:
                    depth_map.append(float(landmark[2]))
                else:
                    depth_map.append(0.0)
            
            # Normalize depth values
            if depth_map:
                min_depth = min(depth_map)
                max_depth = max(depth_map)
                if max_depth > min_depth:
                    depth_map = [(d - min_depth) / (max_depth - min_depth) for d in depth_map]
            
            return depth_map
        except Exception as e:
            print(f"Depth map creation error: {e}")
            return []
    
    def analyze_facial_planes_mediapipe(self, landmarks):
        """Analyze facial planes for 3D reconstruction"""
        try:
            planes = {
                'forehead_plane': self.calculate_plane_normal(landmarks, [9, 10, 151]),
                'cheek_planes': {
                    'left': self.calculate_plane_normal(landmarks, [116, 117, 118]),
                    'right': self.calculate_plane_normal(landmarks, [345, 346, 347])
                },
                'nose_plane': self.calculate_plane_normal(landmarks, [1, 2, 5]),
                'chin_plane': self.calculate_plane_normal(landmarks, [175, 199, 208])
            }
            return planes
        except Exception as e:
            print(f"Facial planes analysis error: {e}")
            return {}
    
    def calculate_plane_normal(self, landmarks, indices):
        """Calculate normal vector for a plane defined by 3 points"""
        try:
            if len(indices) >= 3 and all(i < len(landmarks) for i in indices):
                p1 = np.array(landmarks[indices[0]])
                p2 = np.array(landmarks[indices[1]])
                p3 = np.array(landmarks[indices[2]])
                
                v1 = p2 - p1
                v2 = p3 - p1
                normal = np.cross(v1, v2)
                
                # Normalize
                norm = np.linalg.norm(normal)
                if norm > 0:
                    normal = normal / norm
                
                return normal.tolist()
            return [0, 0, 1]
        except Exception as e:
            print(f"Plane normal calculation error: {e}")
            return [0, 0, 1]
    
    def categorize_skin_tone(self, rgb_color):
        """Categorize skin tone"""
        brightness = np.mean(rgb_color)
        
        if brightness < 100:
            return 'very_dark'
        elif brightness < 140:
            return 'dark'
        elif brightness < 180:
            return 'medium'
        elif brightness < 220:
            return 'light'
        else:
            return 'very_light'
    
    def extract_dlib_features(self, image_rgb):
        """Extract features using dlib (68 landmarks)"""
        # Implementation for dlib would go here
        # For now, return None to fall back to next method
        return None
    
    def extract_face_recognition_features(self, image_rgb):
        """Extract features using face_recognition library"""
        # Implementation for face_recognition would go here
        # For now, return None to fall back to next method
        return None
    
    def get_default_features(self):
        """Return default features when all methods fail"""
        return {
            'face_detected': False,
            'method': 'default_fallback',
            'analysis_failed': True,
            'skin_tone': {'rgb': [200, 180, 160], 'hex': '#c8b4a0', 'tone_category': 'medium'},
            'landmarks_3d': [],
            'facial_symmetry': 0.8
        }
