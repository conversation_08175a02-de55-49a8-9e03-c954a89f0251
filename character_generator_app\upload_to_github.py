#!/usr/bin/env python3
"""
GitHub Repository Upload Script
Prepares and uploads the AI-Powered 3D Character Generator to GitHub
"""

import os
import sys
import subprocess
import json
from pathlib import Path
import shutil

def check_git_installed():
    """Check if Git is installed"""
    try:
        subprocess.run(["git", "--version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def initialize_git_repo():
    """Initialize Git repository"""
    print("🔧 Initializing Git repository...")
    
    # Initialize git repo
    subprocess.run(["git", "init"], check=True)
    
    # Add all files
    subprocess.run(["git", "add", "."], check=True)
    
    # Initial commit
    subprocess.run([
        "git", "commit", "-m", 
        "Initial commit: AI-Powered 3D Character Generator v1.0.0"
    ], check=True)
    
    print("✓ Git repository initialized")

def create_github_repo_info():
    """Create repository information file"""
    repo_info = {
        "name": "ai-3d-character-generator",
        "description": "AI-Powered 3D Character Generator with ComfyUI, Hugging Face, and AgenticSeek integration",
        "topics": [
            "ai", "3d-modeling", "character-generation", "comfyui", 
            "huggingface", "stable-diffusion", "python", "fastapi",
            "3d-graphics", "game-development", "unity", "unreal-engine"
        ],
        "homepage": "",
        "private": False,
        "has_issues": True,
        "has_projects": True,
        "has_wiki": True,
        "license": "MIT"
    }
    
    with open("github_repo_info.json", "w") as f:
        json.dump(repo_info, f, indent=2)
    
    print("✓ GitHub repository info created")

def clean_for_upload():
    """Clean up files that shouldn't be uploaded"""
    print("🧹 Cleaning up for upload...")
    
    # Files and directories to exclude
    exclude_patterns = [
        "__pycache__",
        "*.pyc",
        "*.pyo", 
        "*.pyd",
        ".Python",
        "venv",
        "env",
        ".venv",
        ".env",
        "outputs/*",
        "uploads/*", 
        "temp/*",
        "logs/*",
        "models/*.ckpt",
        "models/*.safetensors",
        "models/*.pth",
        "models/*.bin",
        "*.log",
        ".DS_Store",
        "Thumbs.db"
    ]
    
    # Create .gitignore if it doesn't exist
    if not Path(".gitignore").exists():
        with open(".gitignore", "w") as f:
            f.write("\n".join(exclude_patterns))
    
    # Remove large model files (they'll be downloaded separately)
    model_dirs = ["models", "comfyui/models"]
    for model_dir in model_dirs:
        if Path(model_dir).exists():
            for file_path in Path(model_dir).rglob("*"):
                if file_path.is_file() and file_path.suffix in ['.ckpt', '.safetensors', '.pth', '.bin']:
                    if file_path.stat().st_size > 100 * 1024 * 1024:  # > 100MB
                        print(f"  Removing large model file: {file_path}")
                        file_path.unlink()
    
    print("✓ Cleanup completed")

def create_release_package():
    """Create a release package with essential files"""
    print("📦 Creating release package...")
    
    # Essential files for the release
    essential_files = [
        "README_GITHUB.md",
        "requirements.txt",
        "install.bat",
        "install.sh", 
        "start.bat",
        "start_app.sh",
        "setup_config.py",
        "download_ai_models.py",
        "comprehensive_backend.py",
        "comprehensive_character_generator.html",
        "LICENSE",
        "CONTRIBUTING.md",
        ".gitignore"
    ]
    
    # Essential directories
    essential_dirs = [
        "backend",
        "workflows",
        "frontend"
    ]
    
    # Create release directory
    release_dir = Path("release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # Copy essential files
    for file_name in essential_files:
        if Path(file_name).exists():
            shutil.copy2(file_name, release_dir / file_name)
    
    # Copy essential directories
    for dir_name in essential_dirs:
        if Path(dir_name).exists():
            shutil.copytree(dir_name, release_dir / dir_name, 
                          ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
    
    print(f"✓ Release package created in {release_dir}")

def display_upload_instructions():
    """Display instructions for uploading to GitHub"""
    print("\n" + "="*60)
    print("🚀 GITHUB UPLOAD INSTRUCTIONS")
    print("="*60)
    
    print("\n1. CREATE GITHUB REPOSITORY:")
    print("   - Go to https://github.com/new")
    print("   - Repository name: ai-3d-character-generator")
    print("   - Description: AI-Powered 3D Character Generator with ComfyUI integration")
    print("   - Make it Public")
    print("   - Don't initialize with README (we have our own)")
    
    print("\n2. UPLOAD YOUR CODE:")
    print("   Run these commands in your terminal:")
    print("   ```")
    print("   git remote add origin https://github.com/YOUR_USERNAME/ai-3d-character-generator.git")
    print("   git branch -M main")
    print("   git push -u origin main")
    print("   ```")
    
    print("\n3. CONFIGURE REPOSITORY:")
    print("   - Go to repository Settings")
    print("   - Add topics: ai, 3d-modeling, character-generation, comfyui")
    print("   - Enable Issues and Discussions")
    print("   - Set up GitHub Pages (optional)")
    
    print("\n4. CREATE RELEASES:")
    print("   - Go to Releases tab")
    print("   - Click 'Create a new release'")
    print("   - Tag: v1.0.0")
    print("   - Title: AI-Powered 3D Character Generator v1.0.0")
    print("   - Upload the release/ folder as assets")
    
    print("\n5. IMPORTANT NOTES:")
    print("   ⚠️  Large AI models are NOT included in the repository")
    print("   ✓ Users will download models using download_ai_models.py")
    print("   ✓ This keeps the repository size manageable")
    print("   ✓ Models are downloaded from official sources")
    
    print("\n6. REPOSITORY STRUCTURE:")
    print("   ```")
    print("   ai-3d-character-generator/")
    print("   ├── README.md (rename from README_GITHUB.md)")
    print("   ├── requirements.txt")
    print("   ├── install.bat / install.sh")
    print("   ├── start.bat / start_app.sh")
    print("   ├── comprehensive_backend.py")
    print("   ├── backend/")
    print("   ├── workflows/")
    print("   └── frontend/")
    print("   ```")

def main():
    """Main upload preparation function"""
    print("🚀 AI-Powered 3D Character Generator")
    print("📤 GitHub Upload Preparation")
    print("=" * 50)
    
    # Check prerequisites
    if not check_git_installed():
        print("❌ Git is not installed. Please install Git first.")
        print("   Download from: https://git-scm.com/downloads")
        sys.exit(1)
    
    print("✓ Git is installed")
    
    try:
        # Prepare for upload
        clean_for_upload()
        create_github_repo_info()
        create_release_package()
        
        # Initialize git if not already done
        if not Path(".git").exists():
            initialize_git_repo()
        else:
            print("✓ Git repository already exists")
        
        # Rename README for GitHub
        if Path("README_GITHUB.md").exists() and not Path("README.md").exists():
            shutil.move("README_GITHUB.md", "README.md")
            print("✓ README.md created for GitHub")
        
        print("\n🎉 Upload preparation completed successfully!")
        
        # Display upload instructions
        display_upload_instructions()
        
    except Exception as e:
        print(f"\n❌ Upload preparation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
