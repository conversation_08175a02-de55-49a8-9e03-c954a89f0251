# 3D Adventure Game

A simple 3D adventure game created with Unity.

## Game Overview

This is a 3D adventure game where the player explores a world, collects items, and avoids or defeats enemies.

## Features

- First-person character controller with jumping
- Collectible items that award points
- Enemy AI with patrol, chase, and attack behaviors
- Health system with damage effects
- Game state management (main menu, playing, paused, game over)
- UI elements for score, health, and menus

## Controls

- **WASD**: Move the player
- **Mouse**: Look around
- **Space**: Jump
- **Escape**: Pause/Resume the game

## Project Structure

- **Assets/Models**: 3D models for the game
- **Assets/Textures**: Textures for the game
- **Assets/Scripts**: C# scripts for game functionality
- **Assets/Prefabs**: Reusable game objects
- **Assets/Scenes**: Game scenes

## Scripts

### PlayerController.cs
Handles player movement, jumping, and gravity.

### CameraController.cs
Controls the camera movement and rotation based on mouse input.

### GameManager.cs
Manages game states, score, and UI elements.

### Collectible.cs
Implements collectible items that award points when collected.

### Enemy.cs
Controls enemy behavior, including patrolling, chasing, and attacking.

### PlayerHealth.cs
Manages player health, damage effects, and death.

## How to Open the Project

1. Install Unity Hub and Unity Editor (2020.3 LTS or newer)
2. Open Unity Hub
3. Click "Add" and select the UnityGame folder
4. Open the project
5. In the Project window, navigate to Assets/Scenes
6. Double-click on the main scene to open it

## How to Play

1. Click the "Start Game" button on the main menu
2. Explore the world and collect items to increase your score
3. Avoid or defeat enemies
4. Try to get the highest score possible

## Credits

- Game assets from Kenney.nl
- Created as a demonstration of Unity game development
