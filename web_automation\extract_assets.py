"""
Script to extract downloaded asset zip files.
"""

import os
import zipfile
from tqdm import tqdm

def extract_zip(zip_path, extract_to):
    """Extract a zip file to the specified directory."""
    # Create the extraction directory if it doesn't exist
    os.makedirs(extract_to, exist_ok=True)
    
    # Open the zip file
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        # Get the total number of files for the progress bar
        total_files = len(zip_ref.namelist())
        
        # Extract all files with a progress bar
        for file in tqdm(zip_ref.namelist(), total=total_files, desc=f"Extracting {os.path.basename(zip_path)}"):
            zip_ref.extract(file, extract_to)

def main():
    """Main function."""
    print("Starting asset extraction...")
    
    # Extract model assets
    model_dir = "assets/models"
    for filename in os.listdir(model_dir):
        if filename.endswith(".zip"):
            zip_path = os.path.join(model_dir, filename)
            extract_dir = os.path.join(model_dir, os.path.splitext(filename)[0])
            print(f"Extracting {zip_path} to {extract_dir}...")
            extract_zip(zip_path, extract_dir)
    
    # Extract texture assets
    texture_dir = "assets/textures"
    for filename in os.listdir(texture_dir):
        if filename.endswith(".zip"):
            zip_path = os.path.join(texture_dir, filename)
            extract_dir = os.path.join(texture_dir, os.path.splitext(filename)[0])
            print(f"Extracting {zip_path} to {extract_dir}...")
            extract_zip(zip_path, extract_dir)
    
    # Extract audio assets
    audio_dir = "assets/audio"
    for filename in os.listdir(audio_dir):
        if filename.endswith(".zip"):
            zip_path = os.path.join(audio_dir, filename)
            extract_dir = os.path.join(audio_dir, os.path.splitext(filename)[0])
            print(f"Extracting {zip_path} to {extract_dir}...")
            extract_zip(zip_path, extract_dir)
    
    print("Asset extraction complete!")

if __name__ == "__main__":
    main()
