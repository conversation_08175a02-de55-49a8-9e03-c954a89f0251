"""
Blender setup module for ComfyUI rendering.

This module provides functionality to set up Blender scenes for rendering
with mickmumpitz's ComfyUI workflows.
"""

import os
import bpy
import json
import tempfile
import shutil
from pathlib import Path
import numpy as np

class BlenderComfyUISetup:
    """
    Class for setting up Blender scenes for ComfyUI rendering.
    """
    
    def __init__(self, output_dir=None):
        """
        Initialize the Blender ComfyUI setup.
        
        Args:
            output_dir: Directory to save the render passes (default: 'output/blender')
        """
        self.output_dir = output_dir or 'output/blender'
        
        # Create output directories
        os.makedirs(os.path.join(self.output_dir, 'depth'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'mask'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'outline'), exist_ok=True)
    
    def setup_depth_pass(self):
        """
        Set up the depth pass in Blender.
        
        This function configures Blender to render a depth pass,
        which provides distance information for the scene.
        """
        # Get the current scene
        scene = bpy.context.scene
        
        # Enable the Z pass
        scene.view_layers["View Layer"].use_pass_z = True
        
        # Set up compositing nodes for depth output
        scene.use_nodes = True
        tree = scene.node_tree
        
        # Clear existing nodes
        for node in tree.nodes:
            tree.nodes.remove(node)
        
        # Create input node (Render Layers)
        render_layers = tree.nodes.new(type='CompositorNodeRLayers')
        render_layers.location = (0, 0)
        
        # Create Map Range node to normalize depth values
        map_range = tree.nodes.new(type='CompositorNodeMapRange')
        map_range.location = (300, 0)
        map_range.inputs[1].default_value = 0.0  # Min
        map_range.inputs[2].default_value = 10.0  # Max (adjust based on scene scale)
        map_range.inputs[3].default_value = 0.0  # Target Min
        map_range.inputs[4].default_value = 1.0  # Target Max
        
        # Create output node
        file_output = tree.nodes.new(type='CompositorNodeOutputFile')
        file_output.location = (600, 0)
        file_output.base_path = os.path.join(self.output_dir, 'depth')
        file_output.file_slots[0].path = 'depth_####'
        
        # Connect nodes
        tree.links.new(render_layers.outputs['Depth'], map_range.inputs[0])
        tree.links.new(map_range.outputs[0], file_output.inputs[0])
        
        print("Depth pass setup complete.")
    
    def setup_mask_pass(self, objects_colors=None):
        """
        Set up the mask pass in Blender.
        
        This function assigns unique colors to different objects in the scene
        to create a mask pass for ComfyUI.
        
        Args:
            objects_colors: Dictionary mapping object names to hex color codes
                           (default: None, which assigns random colors)
        """
        # Get the current scene
        scene = bpy.context.scene
        
        # If no colors are provided, generate random colors
        if objects_colors is None:
            objects_colors = {}
            for obj in scene.objects:
                if obj.type == 'MESH':
                    # Generate a random hex color
                    r = int(np.random.random() * 255)
                    g = int(np.random.random() * 255)
                    b = int(np.random.random() * 255)
                    hex_color = f"#{r:02x}{g:02x}{b:02x}"
                    objects_colors[obj.name] = hex_color
        
        # Create a new material for each object with the specified color
        for obj_name, hex_color in objects_colors.items():
            if obj_name in bpy.data.objects:
                obj = bpy.data.objects[obj_name]
                
                if obj.type == 'MESH':
                    # Create a new material
                    mat_name = f"{obj_name}_mask"
                    mat = bpy.data.materials.new(name=mat_name)
                    mat.use_nodes = True
                    
                    # Clear existing nodes
                    nodes = mat.node_tree.nodes
                    for node in nodes:
                        nodes.remove(node)
                    
                    # Create emission shader
                    emission = nodes.new(type='ShaderNodeEmission')
                    emission.location = (0, 0)
                    
                    # Set color from hex
                    hex_color = hex_color.lstrip('#')
                    rgb = tuple(int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4))
                    emission.inputs[0].default_value = (*rgb, 1.0)
                    
                    # Create output node
                    output = nodes.new(type='ShaderNodeOutputMaterial')
                    output.location = (300, 0)
                    
                    # Connect nodes
                    mat.node_tree.links.new(emission.outputs[0], output.inputs[0])
                    
                    # Assign material to object
                    if len(obj.data.materials) > 0:
                        obj.data.materials[0] = mat
                    else:
                        obj.data.materials.append(mat)
        
        # Set up compositing nodes for mask output
        scene.use_nodes = True
        tree = scene.node_tree
        
        # Clear existing nodes
        for node in tree.nodes:
            tree.nodes.remove(node)
        
        # Create input node (Render Layers)
        render_layers = tree.nodes.new(type='CompositorNodeRLayers')
        render_layers.location = (0, 0)
        
        # Create output node
        file_output = tree.nodes.new(type='CompositorNodeOutputFile')
        file_output.location = (300, 0)
        file_output.base_path = os.path.join(self.output_dir, 'mask')
        file_output.file_slots[0].path = 'mask_####'
        
        # Connect nodes
        tree.links.new(render_layers.outputs['Image'], file_output.inputs[0])
        
        # Save the color mapping to a JSON file
        color_mapping = {obj_name: color for obj_name, color in objects_colors.items()}
        with open(os.path.join(self.output_dir, 'mask_colors.json'), 'w') as f:
            json.dump(color_mapping, f, indent=4)
        
        print("Mask pass setup complete.")
        print(f"Color mapping saved to: {os.path.join(self.output_dir, 'mask_colors.json')}")
    
    def setup_outline_pass(self, line_thickness=1.0, line_color=(1, 1, 1, 1)):
        """
        Set up the outline pass in Blender using Freestyle.
        
        This function configures Blender to render outlines using Freestyle,
        which defines the shapes and silhouettes of objects.
        
        Args:
            line_thickness: Thickness of the outline lines (default: 1.0)
            line_color: Color of the outline lines (default: white)
        """
        # Get the current scene
        scene = bpy.context.scene
        
        # Enable Freestyle
        scene.render.use_freestyle = True
        scene.render.line_thickness = line_thickness
        
        # Configure Freestyle settings
        freestyle_settings = scene.view_layers["View Layer"].freestyle_settings
        freestyle_settings.as_render_pass = True
        freestyle_settings.use_smoothness = True
        
        # Create a new line set
        line_set = freestyle_settings.linesets.new("Outlines")
        line_set.select_edge_type = 'CONTOUR'
        line_set.select_silhouette = True
        line_set.select_border = True
        line_set.select_crease = True
        
        # Set line color
        linestyle = line_set.linestyle
        linestyle.color = line_color[:3]
        linestyle.alpha = line_color[3]
        
        # Set up compositing nodes for outline output
        scene.use_nodes = True
        tree = scene.node_tree
        
        # Clear existing nodes
        for node in tree.nodes:
            tree.nodes.remove(node)
        
        # Create input node (Render Layers)
        render_layers = tree.nodes.new(type='CompositorNodeRLayers')
        render_layers.location = (0, 0)
        
        # Create output node
        file_output = tree.nodes.new(type='CompositorNodeOutputFile')
        file_output.location = (300, 0)
        file_output.base_path = os.path.join(self.output_dir, 'outline')
        file_output.file_slots[0].path = 'outline_####'
        
        # Connect nodes
        tree.links.new(render_layers.outputs['Freestyle'], file_output.inputs[0])
        
        print("Outline pass setup complete.")
    
    def setup_all_passes(self, objects_colors=None):
        """
        Set up all render passes for ComfyUI rendering.
        
        This function sets up depth, mask, and outline passes in one go.
        
        Args:
            objects_colors: Dictionary mapping object names to hex color codes
                           (default: None, which assigns random colors)
        """
        # Set up depth pass
        self.setup_depth_pass()
        
        # Set up mask pass
        self.setup_mask_pass(objects_colors)
        
        # Set up outline pass
        self.setup_outline_pass()
        
        print("All passes setup complete.")
        print(f"Output directory: {self.output_dir}")
    
    def render_animation(self, frame_start=1, frame_end=250, resolution_x=512, resolution_y=512):
        """
        Render the animation with all passes.
        
        Args:
            frame_start: First frame to render (default: 1)
            frame_end: Last frame to render (default: 250)
            resolution_x: X resolution of the render (default: 512)
            resolution_y: Y resolution of the render (default: 512)
        """
        # Get the current scene
        scene = bpy.context.scene
        
        # Set frame range
        scene.frame_start = frame_start
        scene.frame_end = frame_end
        
        # Set resolution
        scene.render.resolution_x = resolution_x
        scene.render.resolution_y = resolution_y
        
        # Set output format to PNG
        scene.render.image_settings.file_format = 'PNG'
        
        # Render animation
        bpy.ops.render.render(animation=True)
        
        print("Animation rendering complete.")
        print(f"Output directory: {self.output_dir}")
    
    def render_still(self, resolution_x=512, resolution_y=512):
        """
        Render a still image with all passes.
        
        Args:
            resolution_x: X resolution of the render (default: 512)
            resolution_y: Y resolution of the render (default: 512)
        """
        # Get the current scene
        scene = bpy.context.scene
        
        # Set resolution
        scene.render.resolution_x = resolution_x
        scene.render.resolution_y = resolution_y
        
        # Set output format to PNG
        scene.render.image_settings.file_format = 'PNG'
        
        # Render still
        bpy.ops.render.render(write_still=True)
        
        print("Still rendering complete.")
        print(f"Output directory: {self.output_dir}")

# Example usage (to be run in Blender Python console or script):
"""
import sys
sys.path.append('path/to/your/project')
from src.integrations.blender.comfyui_render_setup import BlenderComfyUISetup

# Create setup object
setup = BlenderComfyUISetup(output_dir='C:/path/to/output')

# Set up all passes
setup.setup_all_passes()

# Render animation or still
setup.render_animation(frame_start=1, frame_end=100)
# or
# setup.render_still()
"""
