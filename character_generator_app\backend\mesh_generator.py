#!/usr/bin/env python3
"""
3D mesh generation module for character creation
"""

import numpy as np
import math

class MeshGenerator:
    """Generate 3D meshes for characters"""
    
    def __init__(self):
        self.quality_settings = {
            'low': {'rings': 8, 'sectors': 12, 'subdivisions': 0},
            'medium': {'rings': 12, 'sectors': 16, 'subdivisions': 1},
            'high': {'rings': 16, 'sectors': 20, 'subdivisions': 2},
            'ultra': {'rings': 24, 'sectors': 32, 'subdivisions': 3}
        }
    
    def generate_character_mesh(self, face_analysis, character_params, quality='high'):
        """Generate complete character mesh"""
        try:
            print(f"🎨 Generating {quality} quality character mesh...")
            
            # Get quality settings
            settings = self.quality_settings.get(quality, self.quality_settings['high'])
            
            # Generate head based on face analysis
            head_vertices, head_indices, head_normals = self.generate_head(
                face_analysis, character_params, settings
            )
            
            # Generate body
            body_vertices, body_indices, body_normals = self.generate_body(
                character_params, settings
            )
            
            # Generate limbs
            limb_vertices, limb_indices, limb_normals = self.generate_limbs(
                character_params, settings
            )
            
            # Combine all meshes
            vertices, indices, normals = self.combine_meshes([
                (head_vertices, head_indices, head_normals),
                (body_vertices, body_indices, body_normals),
                (limb_vertices, limb_indices, limb_normals)
            ])
            
            # Apply smoothing if requested
            if settings['subdivisions'] > 0:
                vertices, indices, normals = self.smooth_mesh(
                    vertices, indices, normals, settings['subdivisions']
                )
            
            print(f"✅ Generated mesh: {len(vertices)//3} vertices, {len(indices)//3} faces")
            
            return vertices, indices, normals
            
        except Exception as e:
            print(f"❌ Mesh generation failed: {e}")
            return self.generate_fallback_mesh()
    
    def generate_head(self, face_analysis, character_params, settings):
        """Generate head mesh based on face analysis"""
        try:
            # Head parameters
            head_center = [0, 1.6, 0]
            base_radius = 0.15
            
            # Apply face shape modifications
            face_shape = face_analysis.get('face_shape', {})
            aspect_ratio = face_shape.get('aspect_ratio', 0.8)
            shape_type = face_shape.get('shape', 'oval')
            
            # Adjust proportions based on face shape
            width_scale = 1.0
            height_scale = 1.0
            depth_scale = 1.0
            
            if shape_type == 'round':
                width_scale = 1.1
                height_scale = 0.95
            elif shape_type == 'long':
                height_scale = 1.1
                width_scale = 0.9
            elif shape_type == 'square':
                width_scale = 1.05
                depth_scale = 1.05
            
            # Apply aspect ratio
            width_scale *= aspect_ratio
            
            # Generate ellipsoid head
            vertices = []
            indices = []
            normals = []
            
            rings = settings['rings']
            sectors = settings['sectors']
            
            for r in range(rings + 1):
                lat = math.pi * (-0.5 + r / rings)
                y = math.sin(lat) * height_scale
                xz = math.cos(lat)
                
                for s in range(sectors + 1):
                    lon = 2 * math.pi * s / sectors
                    x = xz * math.cos(lon) * width_scale
                    z = xz * math.sin(lon) * depth_scale
                    
                    # Apply facial asymmetry if detected
                    symmetry = face_analysis.get('symmetry', {}).get('symmetry_score', 1.0)
                    if x > 0:  # Right side of face
                        x *= symmetry
                    
                    # World position
                    world_pos = [
                        head_center[0] + base_radius * x,
                        head_center[1] + base_radius * y,
                        head_center[2] + base_radius * z
                    ]
                    
                    vertices.extend(world_pos)
                    
                    # Normal (pointing outward from center)
                    normal = [x, y, z]
                    length = math.sqrt(sum(n*n for n in normal))
                    if length > 0:
                        normal = [n/length for n in normal]
                    else:
                        normal = [0, 1, 0]
                    
                    normals.extend(normal)
            
            # Generate indices
            for r in range(rings):
                for s in range(sectors):
                    current = r * (sectors + 1) + s
                    next_ring = (r + 1) * (sectors + 1) + s
                    
                    # Two triangles per quad
                    indices.extend([current, next_ring, current + 1])
                    indices.extend([current + 1, next_ring, next_ring + 1])
            
            return vertices, indices, normals
            
        except Exception as e:
            print(f"Head generation error: {e}")
            return self.generate_basic_sphere([0, 1.6, 0], 0.15, settings)
    
    def generate_body(self, character_params, settings):
        """Generate body mesh"""
        try:
            vertices = []
            indices = []
            normals = []
            
            # Body parameters
            body_center = [0, 0.8, 0]
            body_radius = 0.2
            body_height = 0.8
            
            sectors = settings['sectors']
            
            # Generate cylinder
            for i in range(sectors + 1):
                angle = 2 * math.pi * i / sectors
                x = body_radius * math.cos(angle)
                z = body_radius * math.sin(angle)
                
                # Bottom vertex
                vertices.extend([body_center[0] + x, body_center[1] - body_height/2, body_center[2] + z])
                normals.extend([x/body_radius, 0, z/body_radius])
                
                # Top vertex
                vertices.extend([body_center[0] + x, body_center[1] + body_height/2, body_center[2] + z])
                normals.extend([x/body_radius, 0, z/body_radius])
            
            # Generate indices
            for i in range(sectors):
                base = i * 2
                next_base = ((i + 1) % (sectors + 1)) * 2
                
                # Two triangles per quad
                indices.extend([base, base + 1, next_base])
                indices.extend([base + 1, next_base + 1, next_base])
            
            return vertices, indices, normals
            
        except Exception as e:
            print(f"Body generation error: {e}")
            return [], [], []
    
    def generate_limbs(self, character_params, settings):
        """Generate arms and legs"""
        try:
            vertices = []
            indices = []
            normals = []
            
            limb_radius = 0.08
            arm_length = 0.6
            leg_length = 0.8
            
            # Arm positions
            arm_positions = [
                [-0.3, 0.8, 0],  # Left arm
                [0.3, 0.8, 0]    # Right arm
            ]
            
            # Leg positions
            leg_positions = [
                [-0.1, 0.0, 0],  # Left leg
                [0.1, 0.0, 0]    # Right leg
            ]
            
            # Generate arms
            for pos in arm_positions:
                limb_verts, limb_inds, limb_norms = self.generate_cylinder(
                    pos, [0, -arm_length, 0], limb_radius, settings['sectors']//2
                )
                
                vertex_offset = len(vertices) // 3
                vertices.extend(limb_verts)
                normals.extend(limb_norms)
                
                for idx in limb_inds:
                    indices.append(idx + vertex_offset)
            
            # Generate legs
            for pos in leg_positions:
                limb_verts, limb_inds, limb_norms = self.generate_cylinder(
                    pos, [0, -leg_length, 0], limb_radius, settings['sectors']//2
                )
                
                vertex_offset = len(vertices) // 3
                vertices.extend(limb_verts)
                normals.extend(limb_norms)
                
                for idx in limb_inds:
                    indices.append(idx + vertex_offset)
            
            return vertices, indices, normals
            
        except Exception as e:
            print(f"Limb generation error: {e}")
            return [], [], []
    
    def generate_cylinder(self, start_pos, direction, radius, sectors):
        """Generate a cylindrical mesh"""
        vertices = []
        indices = []
        normals = []
        
        end_pos = [start_pos[i] + direction[i] for i in range(3)]
        
        for i in range(sectors + 1):
            angle = 2 * math.pi * i / sectors
            x_offset = radius * math.cos(angle)
            z_offset = radius * math.sin(angle)
            
            # Start vertex
            vertices.extend([start_pos[0] + x_offset, start_pos[1], start_pos[2] + z_offset])
            normals.extend([x_offset/radius, 0, z_offset/radius])
            
            # End vertex
            vertices.extend([end_pos[0] + x_offset, end_pos[1], end_pos[2] + z_offset])
            normals.extend([x_offset/radius, 0, z_offset/radius])
        
        # Generate indices
        for i in range(sectors):
            base = i * 2
            next_base = ((i + 1) % (sectors + 1)) * 2
            
            indices.extend([base, base + 1, next_base])
            indices.extend([base + 1, next_base + 1, next_base])
        
        return vertices, indices, normals
    
    def generate_basic_sphere(self, center, radius, settings):
        """Generate a basic sphere"""
        vertices = []
        indices = []
        normals = []
        
        rings = settings['rings']
        sectors = settings['sectors']
        
        for r in range(rings + 1):
            lat = math.pi * (-0.5 + r / rings)
            y = math.sin(lat)
            xz = math.cos(lat)
            
            for s in range(sectors + 1):
                lon = 2 * math.pi * s / sectors
                x = xz * math.cos(lon)
                z = xz * math.sin(lon)
                
                vertices.extend([
                    center[0] + radius * x,
                    center[1] + radius * y,
                    center[2] + radius * z
                ])
                normals.extend([x, y, z])
        
        # Generate indices
        for r in range(rings):
            for s in range(sectors):
                current = r * (sectors + 1) + s
                next_ring = (r + 1) * (sectors + 1) + s
                
                indices.extend([current, next_ring, current + 1])
                indices.extend([current + 1, next_ring, next_ring + 1])
        
        return vertices, indices, normals
    
    def combine_meshes(self, mesh_list):
        """Combine multiple meshes into one"""
        combined_vertices = []
        combined_indices = []
        combined_normals = []
        
        vertex_offset = 0
        
        for vertices, indices, normals in mesh_list:
            # Add vertices and normals
            combined_vertices.extend(vertices)
            combined_normals.extend(normals)
            
            # Add indices with offset
            for idx in indices:
                combined_indices.append(idx + vertex_offset)
            
            vertex_offset += len(vertices) // 3
        
        return combined_vertices, combined_indices, combined_normals
    
    def smooth_mesh(self, vertices, indices, normals, iterations=1):
        """Apply basic mesh smoothing"""
        try:
            vertex_count = len(vertices) // 3
            smoothed_vertices = vertices.copy()
            
            for iteration in range(iterations):
                # Build adjacency list
                adjacency = [[] for _ in range(vertex_count)]
                
                for i in range(0, len(indices), 3):
                    v1, v2, v3 = indices[i], indices[i+1], indices[i+2]
                    if v1 < vertex_count and v2 < vertex_count and v3 < vertex_count:
                        adjacency[v1].extend([v2, v3])
                        adjacency[v2].extend([v1, v3])
                        adjacency[v3].extend([v1, v2])
                
                # Apply Laplacian smoothing
                for v in range(vertex_count):
                    if adjacency[v]:
                        neighbors = list(set(adjacency[v]))  # Remove duplicates
                        
                        if neighbors:
                            avg_x = sum(vertices[n*3] for n in neighbors if n < vertex_count) / len(neighbors)
                            avg_y = sum(vertices[n*3+1] for n in neighbors if n < vertex_count) / len(neighbors)
                            avg_z = sum(vertices[n*3+2] for n in neighbors if n < vertex_count) / len(neighbors)
                            
                            # Blend with original position
                            blend_factor = 0.3
                            smoothed_vertices[v*3] = vertices[v*3] * (1-blend_factor) + avg_x * blend_factor
                            smoothed_vertices[v*3+1] = vertices[v*3+1] * (1-blend_factor) + avg_y * blend_factor
                            smoothed_vertices[v*3+2] = vertices[v*3+2] * (1-blend_factor) + avg_z * blend_factor
                
                vertices = smoothed_vertices.copy()
            
            # Recalculate normals
            smoothed_normals = self.recalculate_normals(smoothed_vertices, indices)
            
            return smoothed_vertices, indices, smoothed_normals
            
        except Exception as e:
            print(f"Mesh smoothing error: {e}")
            return vertices, indices, normals
    
    def recalculate_normals(self, vertices, indices):
        """Recalculate vertex normals"""
        vertex_count = len(vertices) // 3
        normals = [0.0] * len(vertices)
        
        # Calculate face normals and accumulate
        for i in range(0, len(indices), 3):
            if i + 2 < len(indices):
                v1_idx, v2_idx, v3_idx = indices[i], indices[i+1], indices[i+2]
                
                if v1_idx < vertex_count and v2_idx < vertex_count and v3_idx < vertex_count:
                    # Get vertices
                    v1 = [vertices[v1_idx*3], vertices[v1_idx*3+1], vertices[v1_idx*3+2]]
                    v2 = [vertices[v2_idx*3], vertices[v2_idx*3+1], vertices[v2_idx*3+2]]
                    v3 = [vertices[v3_idx*3], vertices[v3_idx*3+1], vertices[v3_idx*3+2]]
                    
                    # Calculate face normal
                    edge1 = [v2[j] - v1[j] for j in range(3)]
                    edge2 = [v3[j] - v1[j] for j in range(3)]
                    
                    # Cross product
                    normal = [
                        edge1[1]*edge2[2] - edge1[2]*edge2[1],
                        edge1[2]*edge2[0] - edge1[0]*edge2[2],
                        edge1[0]*edge2[1] - edge1[1]*edge2[0]
                    ]
                    
                    # Normalize
                    length = math.sqrt(sum(n*n for n in normal))
                    if length > 0:
                        normal = [n/length for n in normal]
                    
                    # Add to vertex normals
                    for v_idx in [v1_idx, v2_idx, v3_idx]:
                        normals[v_idx*3] += normal[0]
                        normals[v_idx*3+1] += normal[1]
                        normals[v_idx*3+2] += normal[2]
        
        # Normalize vertex normals
        for v in range(vertex_count):
            length = math.sqrt(
                normals[v*3]**2 + normals[v*3+1]**2 + normals[v*3+2]**2
            )
            if length > 0:
                normals[v*3] /= length
                normals[v*3+1] /= length
                normals[v*3+2] /= length
        
        return normals
    
    def generate_fallback_mesh(self):
        """Generate simple fallback mesh"""
        print("⚠️ Using fallback mesh generation")
        
        # Simple character: sphere head + cylinder body
        head_vertices, head_indices, head_normals = self.generate_basic_sphere(
            [0, 1.6, 0], 0.15, self.quality_settings['medium']
        )
        
        body_vertices, body_indices, body_normals = self.generate_cylinder(
            [0, 0.4, 0], [0, 0.8, 0], 0.2, 12
        )
        
        vertices, indices, normals = self.combine_meshes([
            (head_vertices, head_indices, head_normals),
            (body_vertices, body_indices, body_normals)
        ])
        
        return vertices, indices, normals
