"""
Direct Unreal Engine Editing Integration

This module provides functionality to directly edit and create content in Unreal Engine
and integrate the results with the AI content generation pipeline.
"""

import os
import sys
import subprocess
import tempfile
import json
import time
import shutil
from pathlib import Path

class UnrealDirectEditor:
    """
    Class for directly editing and creating content in Unreal Engine.
    """
    
    def __init__(self, unreal_path=None, unreal_project_path=None):
        """
        Initialize the Unreal Engine direct editor.
        
        Args:
            unreal_path: Path to the Unreal Engine executable (default: from environment variable)
            unreal_project_path: Path to the Unreal Engine project (default: from environment variable)
        """
        self.unreal_path = unreal_path or os.environ.get('UNREAL_PATH', 'UE4Editor.exe')
        self.unreal_project_path = unreal_project_path or os.environ.get('UNREAL_PROJECT_PATH')
        
        # Create scripts directory if it doesn't exist
        self.scripts_dir = os.path.join(os.path.dirname(__file__), 'scripts')
        os.makedirs(self.scripts_dir, exist_ok=True)
        
        # Create the Unreal Engine scripts if they don't exist
        self._create_unreal_scripts()
    
    def _create_unreal_scripts(self):
        """Create Unreal Engine Python scripts for automation."""
        # Script for importing assets
        import_script = """
import unreal
import sys
import os
import json

# Get arguments
args = sys.argv
if len(args) > 1:
    params_json = args[1]
    params = json.loads(params_json)
else:
    params = {}

asset_path = params.get('asset_path', '')
destination_path = params.get('destination_path', '/Game/ImportedAssets')
asset_type = params.get('asset_type', 'StaticMesh')

# Import the asset
if asset_path and os.path.exists(asset_path):
    file_ext = os.path.splitext(asset_path)[1].lower()
    
    # Create import task
    import_task = unreal.AssetImportTask()
    import_task.filename = asset_path
    import_task.destination_path = destination_path
    import_task.replace_existing = True
    import_task.automated = True
    
    # Set import options based on asset type
    if asset_type == 'StaticMesh' and (file_ext == '.fbx' or file_ext == '.obj' or file_ext == '.glb' or file_ext == '.gltf'):
        import_task.options = unreal.FbxImportUI()
        import_task.options.static_mesh_import_data.combine_meshes = True
        import_task.options.static_mesh_import_data.generate_lightmap_u_vs = True
        import_task.options.static_mesh_import_data.auto_generate_collision = True
    elif asset_type == 'SkeletalMesh' and file_ext == '.fbx':
        import_task.options = unreal.FbxImportUI()
        import_task.options.skeletal_mesh_import_data.update_skeleton_reference_pose = True
        import_task.options.skeletal_mesh_import_data.use_t0_as_ref_pose = True
    elif asset_type == 'Animation' and file_ext == '.fbx':
        import_task.options = unreal.FbxImportUI()
        import_task.options.mesh_type_to_import = unreal.FBXImportType.FBXIT_ANIMATION
    elif asset_type == 'Texture' and (file_ext == '.png' or file_ext == '.jpg' or file_ext == '.jpeg' or file_ext == '.tga'):
        # Texture import doesn't need special options
        pass
    
    # Execute the import task
    unreal.AssetToolsHelpers.get_asset_tools().import_asset_tasks([import_task])
    
    # Log the imported assets
    imported_assets = []
    for path in import_task.imported_object_paths:
        imported_assets.append(path)
        print(f"Imported: {path}")
    
    # Save the imported assets
    unreal.EditorAssetLibrary.save_loaded_assets(imported_assets)
    
    print(f"Asset import complete: {asset_path}")
else:
    print(f"Asset not found or path not provided: {asset_path}")
"""
        
        # Script for creating a new level
        create_level_script = """
import unreal
import sys
import os
import json

# Get arguments
args = sys.argv
if len(args) > 1:
    params_json = args[1]
    params = json.loads(params_json)
else:
    params = {}

level_name = params.get('level_name', 'NewLevel')
template = params.get('template', 'Default')
save_path = params.get('save_path', '/Game/Maps')

# Create a new level
level_path = f"{save_path}/{level_name}"
level_factory = unreal.LevelFactory()

# Create the level based on the template
if template == 'Default':
    new_level = level_factory.create_new(level_path)
elif template == 'Empty':
    new_level = level_factory.create_new(level_path, unreal.LevelStreamingAlwaysLoaded)
else:
    # Try to use the template as a path to an existing level
    template_path = template
    if unreal.EditorAssetLibrary.does_asset_exist(template_path):
        new_level = unreal.EditorAssetLibrary.duplicate_asset(template_path, level_path)
    else:
        new_level = level_factory.create_new(level_path)

# Save the new level
unreal.EditorAssetLibrary.save_asset(level_path)

print(f"Level created: {level_path}")
"""
        
        # Script for placing assets in a level
        place_assets_script = """
import unreal
import sys
import os
import json
import random

# Get arguments
args = sys.argv
if len(args) > 1:
    params_json = args[1]
    params = json.loads(params_json)
else:
    params = {}

asset_paths = params.get('asset_paths', [])
positions = params.get('positions', [])
rotations = params.get('rotations', [])
scales = params.get('scales', [])
random_placement = params.get('random_placement', False)
num_instances = params.get('num_instances', 1)
area_size = params.get('area_size', 1000)

# Get the current level
level = unreal.EditorLevelLibrary.get_editor_world()

# Place assets in the level
placed_actors = []

for i, asset_path in enumerate(asset_paths):
    # Check if the asset exists
    if not unreal.EditorAssetLibrary.does_asset_exist(asset_path):
        print(f"Asset not found: {asset_path}")
        continue
    
    # Load the asset
    asset = unreal.EditorAssetLibrary.load_asset(asset_path)
    
    # Determine how many instances to place
    instances_to_place = num_instances
    
    for j in range(instances_to_place):
        # Determine position, rotation, and scale
        if random_placement:
            # Random position within the specified area
            x = random.uniform(-area_size/2, area_size/2)
            y = random.uniform(-area_size/2, area_size/2)
            z = 0  # Place on the ground
            
            # Random rotation
            yaw = random.uniform(0, 360)
            pitch = 0
            roll = 0
            
            # Random scale (within reasonable limits)
            scale_factor = random.uniform(0.8, 1.2)
            scale_x = scale_factor
            scale_y = scale_factor
            scale_z = scale_factor
        else:
            # Use provided position, rotation, and scale if available
            if i < len(positions):
                x, y, z = positions[i]
            else:
                x, y, z = 0, 0, 0
            
            if i < len(rotations):
                yaw, pitch, roll = rotations[i]
            else:
                yaw, pitch, roll = 0, 0, 0
            
            if i < len(scales):
                scale_x, scale_y, scale_z = scales[i]
            else:
                scale_x, scale_y, scale_z = 1, 1, 1
        
        # Create location, rotation, and scale
        location = unreal.Vector(x, y, z)
        rotation = unreal.Rotator(pitch, yaw, roll)
        scale = unreal.Vector(scale_x, scale_y, scale_z)
        
        # Place the asset in the level
        actor = unreal.EditorLevelLibrary.spawn_actor_from_object(asset, location, rotation)
        
        if actor:
            # Set the scale
            actor.set_actor_scale3d(scale)
            placed_actors.append(actor)
            print(f"Placed {asset_path} at {location}")

# Save the level
unreal.EditorLevelLibrary.save_current_level()

print(f"Placed {len(placed_actors)} actors in the level")
"""
        
        # Save the scripts
        with open(os.path.join(self.scripts_dir, 'import_asset.py'), 'w') as f:
            f.write(import_script)
        
        with open(os.path.join(self.scripts_dir, 'create_level.py'), 'w') as f:
            f.write(create_level_script)
        
        with open(os.path.join(self.scripts_dir, 'place_assets.py'), 'w') as f:
            f.write(place_assets_script)
    
    def open_unreal_editor(self, project_path=None, map_path=None):
        """
        Open the Unreal Engine editor.
        
        Args:
            project_path: Path to the Unreal Engine project (default: None, which uses the initialized path)
            map_path: Path to the map to open (default: None, which opens the default map)
            
        Returns:
            subprocess.Popen: The Unreal Engine process
        """
        project_path = project_path or self.unreal_project_path
        
        if not project_path:
            raise ValueError("Unreal Engine project path not specified")
        
        # Construct the command
        cmd = [self.unreal_path, project_path]
        
        # Add map path if provided
        if map_path:
            cmd.append(map_path)
        
        try:
            process = subprocess.Popen(cmd)
            print(f"Unreal Engine opened.")
            print(f"Project: {project_path}")
            if map_path:
                print(f"Map: {map_path}")
            return process
        except Exception as e:
            print(f"Error opening Unreal Engine: {e}")
            raise
    
    def import_asset(self, asset_path, destination_path='/Game/ImportedAssets', asset_type='StaticMesh'):
        """
        Import an asset into Unreal Engine.
        
        Args:
            asset_path: Path to the asset file
            destination_path: Path in the Unreal Engine project to import the asset to (default: '/Game/ImportedAssets')
            asset_type: Type of the asset ('StaticMesh', 'SkeletalMesh', 'Animation', 'Texture') (default: 'StaticMesh')
            
        Returns:
            bool: True if the import was successful, False otherwise
        """
        if not os.path.exists(asset_path):
            raise FileNotFoundError(f"Asset file not found: {asset_path}")
        
        if not self.unreal_project_path:
            raise ValueError("Unreal Engine project path not specified")
        
        # Prepare parameters
        params = {
            'asset_path': asset_path,
            'destination_path': destination_path,
            'asset_type': asset_type
        }
        
        # Run Unreal Engine with the import script
        script_path = os.path.join(self.scripts_dir, 'import_asset.py')
        cmd = [
            self.unreal_path,
            self.unreal_project_path,
            '-ExecutePythonScript=' + script_path,
            json.dumps(params)
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Error importing asset: {e}")
            print(f"Unreal Engine output: {e.stdout}")
            print(f"Unreal Engine error: {e.stderr}")
            return False
    
    def create_level(self, level_name, template='Default', save_path='/Game/Maps'):
        """
        Create a new level in Unreal Engine.
        
        Args:
            level_name: Name of the new level
            template: Template to use ('Default', 'Empty', or a path to an existing level) (default: 'Default')
            save_path: Path in the Unreal Engine project to save the level to (default: '/Game/Maps')
            
        Returns:
            bool: True if the level creation was successful, False otherwise
        """
        if not self.unreal_project_path:
            raise ValueError("Unreal Engine project path not specified")
        
        # Prepare parameters
        params = {
            'level_name': level_name,
            'template': template,
            'save_path': save_path
        }
        
        # Run Unreal Engine with the create level script
        script_path = os.path.join(self.scripts_dir, 'create_level.py')
        cmd = [
            self.unreal_path,
            self.unreal_project_path,
            '-ExecutePythonScript=' + script_path,
            json.dumps(params)
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Error creating level: {e}")
            print(f"Unreal Engine output: {e.stdout}")
            print(f"Unreal Engine error: {e.stderr}")
            return False

# Example usage
if __name__ == "__main__":
    editor = UnrealDirectEditor()
    
    # Open Unreal Engine
    # editor.open_unreal_editor()
    
    # Import an asset
    # editor.import_asset("path/to/asset.fbx")
    
    # Create a new level
    # editor.create_level("MyNewLevel")
