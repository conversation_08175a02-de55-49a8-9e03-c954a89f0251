#!/usr/bin/env python3
"""Simple server test"""

import sys
import os
sys.path.append('backend')

print("🧪 Testing Server Startup")
print("=" * 25)

try:
    print("1. Importing Flask...")
    from flask import Flask, jsonify
    from flask_cors import CORS
    print("✅ Flask imported")
    
    print("2. Creating app...")
    app = Flask(__name__)
    CORS(app)
    print("✅ App created")
    
    print("3. Adding routes...")
    @app.route('/')
    def index():
        return jsonify({'status': 'working', 'message': 'Test server'})
    
    @app.route('/test')
    def test():
        return jsonify({'test': 'success'})
    
    print("✅ Routes added")
    
    print("4. Starting server...")
    print("🌐 Server will start on http://localhost:5001")
    print("🔍 Check http://localhost:5001/ in your browser")
    
    app.run(host='0.0.0.0', port=5001, debug=False)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
